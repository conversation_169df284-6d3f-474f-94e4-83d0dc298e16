package com.asmtunis.procaisseinventory.setting.adaptive_view

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AutoMode
import androidx.compose.material.icons.filled.DarkMode
import androidx.compose.material.icons.filled.LightMode
import androidx.compose.material.icons.twotone.AvTimer
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.RadioButton
import androidx.compose.material3.SegmentedButton
import androidx.compose.material3.SegmentedButtonDefaults
import androidx.compose.material3.SingleChoiceSegmentedButtonRow
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.domaine.Licence
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.SubscribtionRoute
import com.asmtunis.procaisseinventory.core.print.PrintingData
import com.asmtunis.procaisseinventory.core.utils.PackageUtils
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.SubscribtionButton
import com.asmtunis.procaisseinventory.shared_ui_components.theme.ThemeValues
import com.asmtunis.procaisseinventory.shared_ui_components.theme.data.ThemeMode
import com.simapps.ui_kit.SwitchWithLabel
import com.simapps.ui_kit.custom_cards.ItemDetail
import com.simapps.ui_kit.utils.remainingDays

@Composable
fun SettingColumnView(
    navigate: (route: Any) -> Unit,
    padding: PaddingValues,
    paddingStart: Dp = 20.dp,
    selectedBaseconfig: BaseConfig,
    licenceProCaisse: Licence?,
    licenceProInventory: Licence?,
    utilisateur: Utilisateur,
    isOperateurPatrimoine: Boolean,
    printParams: PrintingData,
    dataVm: DataViewModel,
    settingVM: SettingViewModel,
    isDemo: Boolean,
    ) {
    val context = LocalContext.current
    Column(
        modifier = Modifier//.padding(padding)
            //.fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(padding)
            .padding(start = 9.dp, end = 9.dp),
        verticalArrangement = Arrangement.Top,
        horizontalAlignment = Alignment.Start,
    ) {




        SubscribtionButton(
            productName = Globals.PRO_CAISSE_MOBILITY,
            isActivated = dataVm::getProcaisseActivationState,
            isSubscriptionSent = dataVm::getProcaisseSubscribtionSent,
            isDemo = isDemo,
            selectedBaseconfig = selectedBaseconfig,
            onClick =  { navigate(SubscribtionRoute) }
        )

        SubscribtionButton(
            productName = Globals.PRO_INVENTORY,
            isActivated = dataVm::getInventoryActivationState,
            isSubscriptionSent =  dataVm::getProInventorySubscribtionSent,
            isDemo = isDemo,
            selectedBaseconfig = selectedBaseconfig,
            onClick = { navigate(SubscribtionRoute) }
        )



        OutlinedButton(
            modifier = Modifier.fillMaxWidth().padding(top = 12.dp, bottom = 12.dp),
            onClick = {
                settingVM.onShowAuthorisationListChange(true)
            }) {
            Text(
                text = stringResource(id = R.string.list_authorisation),
                fontSize = MaterialTheme.typography.titleMedium.fontSize,
                fontWeight = MaterialTheme.typography.titleMedium.fontWeight
            )
        }

        Text(
            text = stringResource(id = R.string.automatic_sync),
            fontSize = MaterialTheme.typography.titleLarge.fontSize,
            fontWeight = MaterialTheme.typography.titleLarge.fontWeight
        )



        if(dataVm.getIsProcaisseLicenseSelected()) {
            SwitchWithLabel(
                label = stringResource(id = R.string.procaisse_mobility),
                state = settingVM.isProcaisseAutoSync,
                paddingStart = paddingStart
            ) {
                settingVM.setProCaisseSyncMode(!settingVM.isProcaisseAutoSync)
            }
        }


        if(dataVm.getIsProInventoryLicenseSelected()) {
            SwitchWithLabel(
                label = stringResource(id = R.string.pro_inventory),
                state = settingVM.isProInventoryAutoSync,
                paddingStart = paddingStart
            ) {
                settingVM.setProInventorySyncMode(!settingVM.isProInventoryAutoSync)
            }
        }


        Spacer(modifier = Modifier.height(9.dp))
        HorizontalDivider()
        Spacer(modifier = Modifier.height(9.dp))

        //Spacer(modifier = Modifier.height(3.dp))


        Text(
            text = stringResource(id = R.string.impression),
            fontSize = MaterialTheme.typography.titleLarge.fontSize,
            fontWeight = MaterialTheme.typography.titleLarge.fontWeight
        )


        // Print method selection with radio buttons
        Text(
            text = stringResource(id = R.string.print_method),
            fontSize = MaterialTheme.typography.titleMedium.fontSize,
            fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Column(Modifier.selectableGroup()) {
            // Bluetooth Radio Button
            Row(
                Modifier
                    .fillMaxWidth()
                    .height(48.dp)
                    .selectable(
                        selected = (!printParams.printViaWifi && !printParams.useSunmiPrinter),
                        onClick = {
                            settingVM.setPrintParams(printParams.copy(printViaWifi = false, useSunmiPrinter = false))
                        },
                        role = Role.RadioButton
                    )
                    .padding(horizontal = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (!printParams.printViaWifi && !printParams.useSunmiPrinter),
                    onClick = null
                )
                Text(
                    text = "Bluetooth",
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.padding(start = 16.dp)
                )
            }

            // WiFi (A4) Radio Button
            Row(
                Modifier
                    .fillMaxWidth()
                    .height(48.dp)
                    .selectable(
                        selected = printParams.printViaWifi,
                        onClick = {
                            settingVM.setPrintParams(printParams.copy(printViaWifi = true, useSunmiPrinter = false))
                        },
                        role = Role.RadioButton
                    )
                    .padding(horizontal = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = printParams.printViaWifi,
                    onClick = null
                )
                Text(
                    text = "WiFi (A4)",
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.padding(start = 16.dp)
                )
            }

            // Sunmi Radio Button
            Row(
                Modifier
                    .fillMaxWidth()
                    .height(48.dp)
                    .selectable(
                        selected = printParams.useSunmiPrinter,
                        onClick = {
                            settingVM.setPrintParams(printParams.copy(printViaWifi = false, useSunmiPrinter = true))
                        },
                        role = Role.RadioButton
                    )
                    .padding(horizontal = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = printParams.useSunmiPrinter,
                    onClick = null
                )
                Text(
                    text = "Sunmi",
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.padding(start = 16.dp)
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        SwitchWithLabel(
            label = stringResource(id = R.string.icon_entreprise),
            state = printParams.printIcon,
            paddingStart = paddingStart
        ) {
            settingVM.setPrintParams(printParams.copy(printIcon = !printParams.printIcon))
        }




        SwitchWithLabel(
            label = stringResource(id = R.string.designation_entreprise),
            state = printParams.printCompanyName,
            paddingStart = paddingStart
        ) {
            settingVM.setPrintParams(printParams.copy(printCompanyName = !printParams.printCompanyName))
        }

        SwitchWithLabel(
            label = stringResource(id = R.string.print_tax_article),
            state = printParams.taxeArticle,
            paddingStart = paddingStart
        ) {
            settingVM.setPrintParams(printParams.copy(taxeArticle = !printParams.taxeArticle))
        }

        SwitchWithLabel(
            label = stringResource(id = R.string.solde_client),
            state = printParams.printClientSold,
            paddingStart = paddingStart
        ) {
            settingVM.setPrintParams(printParams.copy(printClientSold = !printParams.printClientSold))
        }


        SwitchWithLabel(
            label = stringResource(id = R.string.versionApp),
            state = printParams.printAppVersion,
            paddingStart = paddingStart
        ) {
            settingVM.setPrintParams(printParams.copy(printAppVersion = !printParams.printAppVersion))
        }


        SwitchWithLabel(
            label = stringResource(id = R.string.cachet_signature),
            state = printParams.printCachet,
            paddingStart = paddingStart
        ) {
            settingVM.setPrintParams(printParams.copy(printCachet = !printParams.printCachet))
        }

        SwitchWithLabel(
            label = stringResource(id = R.string.enable_notes),
            state = printParams.enableNotes,
            paddingStart = paddingStart
        ) {
            settingVM.setPrintParams(printParams.copy(enableNotes = !printParams.enableNotes))
        }


        Spacer(modifier = Modifier.height(12.dp))
        Text(
            text = stringResource(id = R.string.taille_papier_impression),
            fontSize = MaterialTheme.typography.titleMedium.fontSize,
            fontWeight = MaterialTheme.typography.titleMedium.fontWeight
        )



        val radioOptions = listOf("32 mm", "48 mm", "80 mm")
// Note that Modifier.selectableGroup() is essential to ensure correct accessibility behavior
        Column(Modifier.selectableGroup()) {
            radioOptions.forEach { text ->
                Row(
                    Modifier
                        .fillMaxWidth()
                        .height(56.dp)
                        .selectable(
                            selected = (text == printParams.paperSize.toString()),
                            onClick = {
                                settingVM.setPrintParams(
                                    printParams.copy(
                                        paperSize = text
                                            .replace(Regex("\\D+"), "")
                                            .toIntOrNull() ?: 80
                                    )
                                )
                            },
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = (text.replace(Regex("\\D+"), "") == printParams.paperSize.toString()),
                        onClick = null // null recommended for accessibility with screenreaders
                    )
                    Text(
                        text = text,
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }

            }
        }
        Spacer(modifier = Modifier.height(12.dp))


        if(isOperateurPatrimoine) {
            HorizontalDivider()
            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = stringResource(id = R.string.inventaire_batiment_patrimoine),
                fontSize = MaterialTheme.typography.titleLarge.fontSize,
                fontWeight = MaterialTheme.typography.titleLarge.fontWeight
            )
            SwitchWithLabel(
                label = if(settingVM.invStationOrigineIsFromUtil) stringResource(id = R.string.id_station_origine_utilisateur) else stringResource(id = R.string.id_station_origine_societe),
                state = settingVM.invStationOrigineIsFromUtil,
                paddingStart = paddingStart
            ) {
                settingVM.setInventaireStationOrigineIsFromUtilisateur(!settingVM.invStationOrigineIsFromUtil)
            }
            Spacer(modifier = Modifier.height(12.dp))
        }



        HorizontalDivider()
        Spacer(modifier = Modifier.height(12.dp))
        Text(
            text = stringResource(id = R.string.license),
            fontSize = MaterialTheme.typography.titleLarge.fontSize,
            fontWeight = MaterialTheme.typography.titleLarge.fontWeight
        )

        if(dataVm.getProcaisseActivationState() &&
            //  dataVm.getIsProcaisseLicenseSelected() &&
            selectedBaseconfig != BaseConfig()
        ){
            if(licenceProCaisse != null) {
                Spacer(modifier = Modifier.height(12.dp))

                OutlinedCard(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(10.dp),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 4.dp
                    )
                ) {
                    ItemDetail(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = licenceProCaisse.produit + ": "+ stringResource(id = R.string.jours_restant, remainingDays(
                            date = licenceProCaisse.datef,
                            defaultRemainingDays =  licenceProCaisse.dater?.toInt()?: 0).toString()),
                        dataText = stringResource(id = R.string.date_expiration,licenceProCaisse.datef?: "N/A"),
                        icon = Icons.TwoTone.AvTimer,
                    )
                }

            }
        }


        if(
            dataVm.getInventoryActivationState() &&
            //  dataVm.getIsProInventoryLicenseSelected() &&
            selectedBaseconfig != BaseConfig()
        ){
            if(licenceProInventory != null) {
                Spacer(modifier = Modifier.height(12.dp))

                OutlinedCard(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(10.dp),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 4.dp
                    )
                ) {
                    ItemDetail(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = licenceProInventory.produit + ": "+ stringResource(id = R.string.jours_restant, remainingDays(
                            date = licenceProInventory.datef,
                            defaultRemainingDays =  licenceProInventory.dater?.toInt()?: 0).toString()),
                        dataText = stringResource(id = R.string.date_expiration,licenceProInventory.datef?: "N/A"),
                        icon = Icons.TwoTone.AvTimer,
                    )
                }
            }
        }




        Spacer(modifier = Modifier.height(26.dp))

        Text(
            text = stringResource(id = R.string.Code_Caisse_Code_Carnet, utilisateur.Code_Caisse?: "N/A ", utilisateur.Code_Carnet?: "N/A"),
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.End,
            style = MaterialTheme.typography.bodySmall
        )

        Spacer(modifier = Modifier.height(26.dp))

        SingleChoiceSegmentedButtonRow(
            //  modifier = Modifier.fillMaxWidth()
        ) {
            val itemList = listOf(
                ThemeMode(
                    themeMode = ThemeValues.SYSTEM_THEME.theme,
                    imageVector = Icons.Filled.AutoMode
                ),
                ThemeMode(
                    themeMode = ThemeValues.DARK.theme,
                    imageVector = Icons.Filled.DarkMode
                ),
                ThemeMode(
                    themeMode = ThemeValues.LIGHT.theme,
                    imageVector = Icons.Filled.LightMode
                )
            )
            itemList.forEachIndexed { index, item ->
                SegmentedButton(
                    shape = SegmentedButtonDefaults.itemShape(index = index, count = itemList.size),
                    onClick = {  settingVM.setThemeMode(item.themeMode) },
                    selected = settingVM.selectedTheme == item.themeMode
                ) {

                    Icon(
                        imageVector = item.imageVector,
                        contentDescription = item.themeMode,
                        //   modifier = Modifier.size(AssistChipDefaults.IconSize),
                        tint = if (settingVM.selectedTheme == item.themeMode) MaterialTheme.colorScheme.error
                        else MaterialTheme.colorScheme.primary
                    )
                }

            }


        }

        Spacer(modifier = Modifier.height(16.dp))
        Spacer(modifier = Modifier.weight(1F))
        Text(
            text = stringResource(id = R.string.version, PackageUtils.getVersionName(context = context)),
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.End,
            style = MaterialTheme.typography.bodySmall
        )

        Spacer(modifier = Modifier.height(28.dp))
    }
}