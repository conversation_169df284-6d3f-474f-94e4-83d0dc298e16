package com.asmtunis.procaisseinventory.shared_ui_components.tables.three_column

import android.Manifest
import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.outlined.ImageNotSupported
import androidx.compose.material.icons.outlined.PhotoCamera
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import coil3.compose.SubcomposeAsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.domaine.SelectedPatrimoine
import com.asmtunis.procaisseinventory.core.utils.ImageUtils.getImageModel
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.shared_ui_components.AskPermission
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.simapps.ui_kit.ModifiersUtils.detectTapGestures
import kotlinx.coroutines.delay

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun ThreeColumnTableWithImage(
    haveCamera: Boolean,
    marqueList: List<Marque>,
    articleMapByBarCode: Map<String, Article>,
    showFilterLine: Boolean = false,
    onShowFilterLineChange: (Boolean) -> Unit = {},
    fiterValue: String = "",
    onFilterValueChange: (String) -> Unit = {},
    canModify: Boolean = false,
    selectedPatrimoineList: List<SelectedPatrimoine>,
    onPressTakeImage: (SelectedPatrimoine) -> Unit = {},
    onPressSeeImage: (SelectedPatrimoine) -> Unit = {},
    onPress: (SelectedPatrimoine) -> Unit = {},
    onLongPress: (SelectedPatrimoine) -> Unit = {},
    onSwipeToDelete: (SelectedPatrimoine) -> Unit = {},
    rowTitls: List<String> = listOf("N° Serie", "Produit"),
) {
    val context = LocalContext.current
    val density = LocalDensity.current
    val listState = rememberLazyListState()
    val nbrOfLines = selectedPatrimoineList.size

    val keyboardController = LocalSoftwareKeyboardController.current
    val focusRequester = remember { FocusRequester() }

    LaunchedEffect(key1 = focusRequester, key2 = showFilterLine) {
        if(!showFilterLine) return@LaunchedEffect
        focusRequester.requestFocus()
        delay(100) // Make sure you have delay here
        keyboardController?.show()

    }


        HorizontalDivider(color = MaterialTheme.colorScheme.outline)
        ThreeTableHeaderTitles(
            rowTitls = rowTitls,
            showFilterLine = showFilterLine,
            onShowFilterLineChange = { onShowFilterLineChange(it) }
        )


        AnimatedVisibility(
            visible = showFilterLine,
            enter = slideInVertically {
                with(density) { 40.dp.roundToPx() }
            } + fadeIn(),
            exit = fadeOut(
                animationSpec = keyframes {
                    this.durationMillis = 120
                }
            )
        ) {

            OutlinedTextField(
                value = fiterValue,
                onValueChange = onFilterValueChange,
                label = { Text(stringResource(id = R.string.filter) + " ($nbrOfLines linges)") },
                modifier = Modifier
                    .focusRequester(focusRequester)
                    .fillMaxWidth()
                    .padding(18.dp),
                trailingIcon = {
                    Row(
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        AnimatedVisibility(
                            visible = fiterValue.isNotEmpty(),
                            enter = fadeIn(),
                            exit = fadeOut(),
                        ) {
                            IconButton(onClick = { onFilterValueChange("") }) {
                                Icon(
                                    imageVector = Icons.Filled.Clear,
                                    contentDescription = stringResource(id = R.string.your_divice_id),
                                )
                            }
                        }


                    }
                },
                keyboardActions = KeyboardActions(
                    onSearch = {
                        keyboardController?.hide()
                    }
                ),
                keyboardOptions = KeyboardOptions(
                    imeAction = ImeAction.Search,
                    keyboardType = KeyboardType.Password
                )
            )


        }
        HorizontalDivider(color = MaterialTheme.colorScheme.outline)

        LazyColumn(
            // modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
            state = listState,
        ) {
            items(
                //  count = selectedPatrimoineList.size,
                items = selectedPatrimoineList.reversed(),
                key = { item -> item.numSerie }
            ) { item ->

                val marque = marqueList.firstOrNull { it.mARCode == item.marqueCode }?: Marque()
                val article = articleMapByBarCode[item.articleCode]?: Article()
                /*  val currentItem by rememberUpdatedState(item)
            SwipeToDeleteContainer(
                canModify = canModify,
                state = rememberDeleteState(
                        onDelete = {
                            onSwipeToDelete(currentItem)
                        }
            ),
                content = {*/
                Column(
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Start,
                        modifier =
                        Modifier
                            .padding(start = 3.dp, end = 3.dp)
                            .heightIn(40.dp, 150.dp)
                            .fillMaxWidth()
                            .detectTapGestures(
                                key1 = item,
                                onPress = {
                                    //  selectArtPatrimoineVM.setSelectedPat(selectedArticle[index])

                                },
                                onDoubleTap = {
                                },
                                onLongPress = {
                                    onLongPress(item)
                                },
                                onTap = {
                                    //  mainViewModel.setAddNewProductDialogueVisibility(true)
                                    onPress(item)
                                },
                            ),
                    ) {
                        Text(
                            text = item.numSerie,
                            fontSize = MaterialTheme.typography.bodySmall.fontSize,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth(0.4f),
                        )

//                        Text(
//                            text =
//                            (listArticle.firstOrNull { it.aRTCodeBar == item.article.aRTCodeBar }?.aRTDesignation
//                                ?: "Article Introuvable !") +
//
//                                    " (" + item.article.aRTCodeBar + ")",
//                            textAlign = TextAlign.Center,
//                            fontSize = MaterialTheme.typography.bodySmall.fontSize,
//                            modifier = Modifier.fillMaxWidth(),
//                        )

                        Text(
                            text =
                            (article.aRTDesignation.ifBlank { "Article Introuvable !" }) +

                                    " (" + article.aRTCode + ")",
                            textAlign = TextAlign.Center,
                            fontSize = MaterialTheme.typography.bodySmall.fontSize,
                            modifier = Modifier.fillMaxWidth(),
                        )
                    }

                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Start,
                        modifier =
                        Modifier
                            .padding(start = 3.dp, end = 3.dp)
                            // .heightIn(40.dp, 150.dp)
                            .fillMaxWidth()
                            .detectTapGestures(
                                key1 = item,
                                onPress = {
                                    //  selectArtPatrimoineVM.setSelectedPat(selectedArticle[index])

                                },
                                onDoubleTap = {

                                },
                                onLongPress = {

                                },
                                onTap = {
                                    //  mainViewModel.setAddNewProductDialogueVisibility(true)
                                    onPress(item)

                                },
                            ),
                    ) {
                        AskPermission(
                            permission =
                            listOf(Manifest.permission.CAMERA,),
                            permissionNotAvailableContent = { permissionState ->
                                if (!canModify && item.imageList.isNotEmpty()) {
                                    ImageView(
                                        item = item,
                                        onPressSeeImage = {
                                            onPressSeeImage(item)
                                        }
                                    )


                                } else if (canModify) {

                                    Icon(
                                        modifier =
                                        Modifier.clickable {
                                            permissionState.launchMultiplePermissionRequest()
                                        },
                                        imageVector = Icons.Outlined.PhotoCamera,
                                        contentDescription = null,
                                    )
                                }
                            },
                            content = {
                                Column(
                                    verticalArrangement = Arrangement.Top,
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                ) {
                                    if (item.imageList.isNotEmpty()) {
                                        BadgedBox(
                                            badge = {
                                                Badge {
                                                    val badgeNumber = item.imageList.size.toString()
                                                    Text(
                                                        text = badgeNumber,
                                                        modifier =
                                                        Modifier.semantics {
                                                            contentDescription =
                                                                "$badgeNumber Images"
                                                        },
                                                    )
                                                }
                                            },
                                        ) {
                                            ImageView(
                                                item = item,
                                                onPressSeeImage = {
                                                    onPressSeeImage(item)
                                                }
                                            )
                                        }
                                        Spacer(modifier = Modifier.height(6.dp))
                                    }

                                    if (canModify && haveCamera) {
                                        Icon(
                                            modifier =
                                            Modifier.clickable {
                                                onPressTakeImage(item)
                                            },
                                            imageVector = Icons.Outlined.PhotoCamera,
                                            contentDescription = null,
                                        )
                                    }
                                }
                            },
                        )

                        Column(
                            modifier = Modifier.padding(6.dp),
                            verticalArrangement = Arrangement.Top,
                            horizontalAlignment = Alignment.Start,
                        ) {
                            //  if (item.extraInfo.isNotEmpty()) {
                            if (marque.mARDesignation.isNotEmpty()) {
                                // Text(text = "("+selectedArticle[index].extraInfo+")", color = MaterialTheme.colorScheme.error )
                                Text(
                                    text = buildAnnotatedString {
                                        withStyle(
                                            SpanStyle(
                                                //  color = MaterialTheme.colorScheme.onError,
                                                fontSize = MaterialTheme.typography.bodyMedium.fontSize
                                            )
                                        ) {
                                            append(context.getString(R.string.marque_title) + ": ")
                                        }




                                        append(marque.mARDesignation)
                                    },
                                    color = MaterialTheme.colorScheme.outline,
                                    fontSize = MaterialTheme.typography.bodySmall.fontSize
                                )
                            }

                            if (item.note.isNotEmpty()) {
                                Spacer(modifier = Modifier.height(3.dp))
                                // Text(text = "("+selectedArticle[index].extraInfo+")", color = MaterialTheme.colorScheme.error )
                                Text(
                                    text =
                                    buildAnnotatedString {
                                        withStyle(
                                            SpanStyle(
                                                //color = MaterialTheme.colorScheme.onError,
                                                fontSize = MaterialTheme.typography.bodyMedium.fontSize
                                            )
                                        ) {
                                            append(context.getString(R.string.note_field) + ": ")
                                        }




                                        append(item.note)
                                    },
                                    color = MaterialTheme.colorScheme.outline,
                                    fontSize = MaterialTheme.typography.bodySmall.fontSize
                                )
                            }
                        }
                        //   }
                        //  }
                    }

                    HorizontalDivider(color = MaterialTheme.colorScheme.outline)
                }
                //  }
                //  )


            }
        }


}

@Composable
fun ImageView(
    item: SelectedPatrimoine,
    onPressSeeImage: () -> Unit
) {

    Log.d("cvvddxxxxx", "imgData: ${getImageModel(image = item.imageList.first())}")
    SubcomposeAsyncImage(
        model =
        ImageRequest.Builder(LocalContext.current)
            .data(getImageModel(image = item.imageList.first()))
            .crossfade(true)
            .build(),
        modifier =
        Modifier
            .clip(CircleShape)
            .size(50.dp)
            .clickable {
                onPressSeeImage()
            },
        contentDescription = null,
        loading = {
            LottieAnim(
                lotti = R.raw.loading,
                size = 25.dp
            )
        },
        error = {
            Icon(
                imageVector = Icons.Outlined.ImageNotSupported,
                contentDescription = null,
            )
        }
    )
}


