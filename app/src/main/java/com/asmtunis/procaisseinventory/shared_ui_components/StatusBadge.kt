package com.asmtunis.procaisseinventory.shared_ui_components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.enum_classes.EtatBonTransfert

@Composable
fun StatusBadge(
    status: String,
    modifier: Modifier = Modifier
) {
    val (statusText, backgroundColor, textColor) = when (status) {
        "2", "0", EtatBonTransfert.VALIDEE.value -> Triple(
            stringResource(id = R.string.validee),
            Color(0xFF4CAF50), // Green
            Color.White
        )
        "1", EtatBonTransfert.EN_INSTANCE.value -> Triple(
            stringResource(id = R.string.en_instance),
            Color(0xFFFF9800), // Orange
            Color.White
        )
        EtatBonTransfert.ANNULEE.value -> Triple(
            stringResource(id = R.string.annulee),
            Color(0xFFF44336), // Red
            Color.White
        )
        else -> Triple(
            status,
            MaterialTheme.colorScheme.surfaceVariant,
            MaterialTheme.colorScheme.onSurfaceVariant
        )
    }

    Box(
        modifier = modifier
            .clip(RoundedCornerShape(12.dp))
            .background(backgroundColor)
            .padding(horizontal = 8.dp, vertical = 4.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = statusText,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            color = textColor
        )
    }
}

@Composable
fun DeplacementStatusBadge(
    etatBon: String?,
    modifier: Modifier = Modifier
) {
    when (etatBon) {
        "1" -> StatusBadge(
            status = EtatBonTransfert.EN_INSTANCE.value,
            modifier = modifier
        )
        "2", "0" -> StatusBadge(
            status = EtatBonTransfert.VALIDEE.value,
            modifier = modifier
        )
        else -> {
            // Don't show badge for unknown status
        }
    }
}
