package com.asmtunis.procaisseinventory.shared_ui_components.lazy_column

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim

/**
 * Composant ListItem avec support pour badge de statut
 * Version étendue de ListItem qui permet d'afficher un badge personnalisé
 */
@Composable
fun ListItemWithBadge(
    onItemClick: () -> Unit,
    firstText: String,
    secondText: String,
    thirdText: String,
    dateText: String,
    isSync: Boolean,
    status: String,
    onResetDeletedClick: () -> Unit,
    moreClickIsVisible: Boolean,
    onMoreClick: () -> Unit,
    isSelected: Boolean = false,
    firstTextRemarque: String = "",
    secondTextRemarque: String = "",
    thirdTextRemarque: String = "",
    dateTextRemarque: String = "",
    statusBadge: @Composable (() -> Unit)? = null // Badge personnalisé
) {
    OutlinedCard(
        border = if(!isSelected) CardDefaults.outlinedCardBorder() else BorderStroke(color = MaterialTheme.colorScheme.outline, width = 3.dp),
        modifier = Modifier.padding(start = 12.dp, end = 12.dp),
        onClick = { onItemClick()}) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Start,
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp)
                .wrapContentHeight()
        ) {

            if (!isSync && status != ItemStatus.DELETED.status && status != ItemStatus.WAITING.status) {
                LottieAnim(lotti = R.raw.connection_error, size = 70.dp)
            }
            else if(status == ItemStatus.WAITING.status && !isSync)
                LottieAnim(lotti = R.raw.waiting_document, size = 70.dp)

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.Start
            ) {
                // Première ligne avec texte et badge
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = buildAnnotatedString {
                            append(firstText)

                            if(firstTextRemarque.isNotEmpty()) {
                                withStyle(
                                    SpanStyle(
                                        color = MaterialTheme.colorScheme.error,
                                        fontSize = MaterialTheme.typography.bodyMedium.fontSize
                                    )
                                ) {
                                    append(firstTextRemarque)
                                }
                            }
                        },
                        fontSize = 16.sp,
                        fontWeight = FontWeight.ExtraBold,
                        maxLines = 1,
                        modifier = Modifier.weight(1f)
                    )
                    
                    // Afficher le badge si fourni
                    statusBadge?.let {
                        Spacer(modifier = Modifier.width(8.dp))
                        it()
                    }
                }

                Text(
                    text = buildAnnotatedString {
                        append(secondText)

                        if(secondTextRemarque.isNotEmpty()) {
                            withStyle(
                                SpanStyle(
                                    color = MaterialTheme.colorScheme.error,
                                    fontSize = MaterialTheme.typography.bodyMedium.fontSize
                                )
                            ) {
                                append(secondTextRemarque)
                            }
                        }
                    },
                    fontSize = MaterialTheme.typography.bodyMedium.fontSize,
                    maxLines = 1,
                )

                Text(
                    text = buildAnnotatedString {
                        append(thirdText)

                        if(thirdTextRemarque.isNotEmpty()) {
                            withStyle(
                                SpanStyle(
                                    color = MaterialTheme.colorScheme.error,
                                    fontSize = MaterialTheme.typography.bodyMedium.fontSize
                                )
                            ) {
                                append(thirdTextRemarque)
                            }
                        }
                    },
                    fontSize = MaterialTheme.typography.bodyMedium.fontSize,
                    maxLines = 1,
                )

                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = buildAnnotatedString {
                        if(dateTextRemarque.isNotEmpty()) {
                            withStyle(
                                SpanStyle(
                                    color = MaterialTheme.colorScheme.error,
                                    fontSize = MaterialTheme.typography.bodyMedium.fontSize,
                                )
                            ) {
                                append(dateTextRemarque)
                            }
                        }

                        append(" ")

                        withStyle(
                            SpanStyle(
                                fontSize = MaterialTheme.typography.bodyMedium.fontSize,
                            )
                        ) {
                            append(dateText)
                        }
                    },
                    maxLines = 1,
                    textAlign = TextAlign.End
                )
            }

            if (!isSync && status == ItemStatus.DELETED.status) {
                LottieAnim(lotti = R.raw.to_delete, size = 50.dp, onClick = {
                    onResetDeletedClick()
                })
            }

            if (moreClickIsVisible) {
                Icon(
                    modifier = Modifier.clickable {
                        onMoreClick()
                    },
                    imageVector = Icons.Default.MoreVert,
                    contentDescription = stringResource(id = R.string.print)
                )
            }
        }
    }
}
