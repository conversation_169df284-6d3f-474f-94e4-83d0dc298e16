package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine.PrixVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine.PrixVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation.ValidationAddNewVcEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class PrixViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb
) : ViewModel() {

    var modify: Boolean by mutableStateOf(false)
        private set
    fun onModifyChange(value: Boolean) {
        modify = value
    }

    fun saveNewPromo(prixVC: PrixVC) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.prixVC.upsert(prixVC)
        }
    }
    var selectedPrixVcWithImages: PrixVCWithImages by mutableStateOf(PrixVCWithImages())
        private set
    fun onselectedPrixChange(value: PrixVCWithImages) {
        selectedPrixVcWithImages = value
    }

    var prixSearchTextState: TextFieldValue by mutableStateOf(TextFieldValue(""))
        private set
    fun onPrixSearchValueChange(value: TextFieldValue) {
        prixSearchTextState = value
    }


    var prixVCListstate: PrixFilterListState by mutableStateOf(PrixFilterListState())
        private set
    fun onEventPrixVC(event: ListEvent) {
        when (event) {
            is ListEvent.Order -> {
                if (prixVCListstate.listOrder::class == event.listOrder::class &&
                    prixVCListstate.listOrder.orderType == event.listOrder.orderType
                ) {
                    return
                }
                prixVCListstate = prixVCListstate.copy(
                    listOrder = event.listOrder
                )
                filterPrixVC(prixVCListstate)
            }
            is ListEvent.Delete -> TODO()
            is ListEvent.Restore -> TODO()



            is ListEvent.ListSearch -> {
                prixVCListstate = prixVCListstate.copy(
                    search = event.listSearch
                )

                filterPrixVC(prixVCListstate)
            }

            is ListEvent.FirstCustomFilter -> TODO()

            is ListEvent.SecondCustomFilter -> {
                prixVCListstate = prixVCListstate.copy(
                    filterByTypeCommunication = event.secondFiter
                )

                filterPrixVC(prixVCListstate)
            }

            is ListEvent.ThirdCustomFilter -> TODO()
        }

    }
    var getPrixJob: Job = Job()
    fun filterPrixVC(prixFilterListState: PrixFilterListState) {
        val searchedText = prixSearchTextState.text
        val searchValue = prixFilterListState.search
        val filterByTypeComm = prixFilterListState.filterByTypeCommunication

        getPrixJob.cancel()

        if (searchedText.isEmpty()) {
            getPrixJob = when (prixFilterListState.listOrder.orderType) {
                is OrderType.Ascending -> {
                    when (prixFilterListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proCaisseLocalDb.prixVC.getAllFiltred(
                                isAsc = 1,
                                sortBy = "CodeVCPromo",
                                filterByTypComm = filterByTypeComm
                            ).collect {
                                setPrixList(listPrixVC=it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proCaisseLocalDb.prixVC.getAllFiltred(
                                isAsc = 1,
                                sortBy = "DateOp",
                                filterByTypComm = filterByTypeComm).collect {
                                setPrixList(listPrixVC=it)
                            }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proCaisseLocalDb.prixVC.getAllFiltred(
                                isAsc = 1,
                                sortBy = "PrixConcur",
                                filterByTypComm = filterByTypeComm
                            ).collect {
                                setPrixList(listPrixVC=it)
                            }
                        }
                    }
                }

                is OrderType.Descending -> {
                    when (prixFilterListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proCaisseLocalDb.prixVC.getAllFiltred(
                                isAsc = 2,
                                sortBy = "CodeVCPromo",
                                filterByTypComm = filterByTypeComm).collect {
                                setPrixList(listPrixVC=it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proCaisseLocalDb.prixVC.getAllFiltred(
                                isAsc = 2,
                                sortBy = "DateOp",
                                filterByTypComm = filterByTypeComm
                            ).collect {
                                setPrixList(listPrixVC=it)
                            }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proCaisseLocalDb.prixVC.getAllFiltred(
                                isAsc = 2,
                                sortBy = "PrixConcur",
                                filterByTypComm = filterByTypeComm
                            ).collect {
                                setPrixList(listPrixVC=it)
                            }
                        }
                    }
                }
            }
        } else {
            if (searchedText.isNotEmpty()) {
                if (searchValue is ListSearch.FirstSearch) {
                    getPrixJob =  when (prixFilterListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (prixFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "CodeVCPromo",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "DateOp",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "PrixConcur",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (prixFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "CodeVCPromo",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "DateOp",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "PrixConcur",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (searchValue is ListSearch.SecondSearch) {
                    getPrixJob = when (prixFilterListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (prixFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByCodeArtLocal(
                                        searchString = searchedText,
                                        sortBy = "CodeVCPromo",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByCodeArtLocal(
                                        searchString = searchedText,
                                        sortBy = "DateOp",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByCodeArtLocal(
                                        searchString = searchedText,
                                        sortBy = "PrixConcur",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (prixFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByCodeArtLocal(
                                        searchString = searchedText,
                                        sortBy = "CodeVCPromo",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByCodeArtLocal(
                                        searchString = searchedText,
                                        sortBy = "DateOp",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByCodeArtLocal(
                                        searchString = searchedText,
                                        sortBy = "PrixConcur",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (searchValue is ListSearch.ThirdSearch) {
                    getPrixJob = when (prixFilterListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (prixFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByArtConcurrent(
                                        searchString = searchedText,
                                        sortBy = "CodeVCPromo",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByArtConcurrent(
                                        searchString = searchedText,
                                        sortBy = "DateOp",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByArtConcurrent(
                                        searchString = searchedText,
                                        sortBy = "PrixConcur",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (prixFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByArtConcurrent(
                                        searchString = searchedText,
                                        sortBy = "CodeVCPromo",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByArtConcurrent(
                                        searchString = searchedText,
                                        sortBy = "DateOp",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.prixVC.filterByArtConcurrent(
                                        searchString = searchedText,
                                        sortBy = "PrixConcur",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPrixList(listPrixVC=it)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }




    private fun setPrixList(listPrixVC: List<PrixVCWithImages>){
        prixVCListstate = prixVCListstate.copy(lists = emptyList())
        prixVCListstate = prixVCListstate.copy(lists = listPrixVC)


       // getPrixJob.cancel()
    }

    fun setDeletedPrix(prixVC: PrixVC) {
        viewModelScope.launch(dispatcher) {
            if(!prixVC.isSync && prixVC.status == ItemStatus.INSERTED.status) {
                proCaisseLocalDb.prixVC.deleteByCode(prixVC.codeVCPrix)
                proCaisseLocalDb.imageVC.deleteByCodeTypeVc(prixVC.codeVCPrixM)
            }

            else
            proCaisseLocalDb.prixVC.setDeleted(
                code = prixVC.codeVCPrix,
                codeMobile = prixVC.codeVCPrixM
            )
            filterPrixVC(prixVCListstate)
        }



    }

    fun restDeletedPrix(prixVCWithImages: PrixVCWithImages) {
        viewModelScope.launch(dispatcher) {

val prixVC = prixVCWithImages.prixVC!!


            val status :String
            val isSync :Boolean
            if(prixVC.codeVCPrix == prixVC.codeVCPrixM){
                status = ItemStatus.DELETED.status
                isSync = false
            }
            else {
                status = ItemStatus.SELECTED.status
                isSync = true

            }

            proCaisseLocalDb.prixVC.restDeleted(
                code = prixVC.codeVCPrix,
                status =status,
                isSync =isSync
            )
            filterPrixVC(prixVCListstate)
        }
    }


    fun handleAddVisiteEvents(
        codeM: String,
        validationAddVcEvents: ValidationAddNewVcEvent,
        popBackStack: () -> Unit,
        utilisateur: Utilisateur,
        saveImageList: () -> Unit

        ) {

                when (validationAddVcEvents) {
                    is ValidationAddNewVcEvent.AddNewVc -> {
                        val userID = utilisateur.codeUt




                        saveImageList()
                        saveNewProduct(
                            codeM = codeM,
                            userID = userID,
                            event = validationAddVcEvents
                        )


                        onModifyChange(false)
                        // navController.navigate(Screen.Clients.Route)
                        popBackStack()

            }
        }
    }


    private fun saveNewProduct(
        codeM: String,
        userID: String,
        event: ValidationAddNewVcEvent.AddNewVc
    ) {
        val selectedPrix = selectedPrixVcWithImages.prixVC?: PrixVC()
        val prixVC =
            PrixVC(
                id = if (selectedPrix.id != 0L) selectedPrix.id else 0,

                codeVCPrix = if (selectedPrix.codeVCPrix != "") selectedPrix.codeVCPrix
                else codeM,
                codeVCPrixM = codeM,
                articleConcur = event.addNewtVc.produit,
                dateOp = getCurrentDateTime(),
                codeConcur = event.addNewtVc.concurrent.codeconcurrent,
                codeArtLocal = event.addNewtVc.localProduct.aRTCode,
                noteOp = event.addNewtVc.note,
                prixConcur = StringUtils.stringToDouble(event.addNewtVc.prix),
                codeUser = userID.toInt(),
                infoOp1 = "",
                codeTypeCom = event.addNewtVc.typeCommunication.codeTypeCom
            )



        prixVC.status = ItemStatus.INSERTED.status
        prixVC.isSync = false

        saveNewPromo(prixVC = prixVC)
    }
}