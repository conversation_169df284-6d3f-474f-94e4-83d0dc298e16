package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Expand
import androidx.compose.material.icons.filled.Minimize
import androidx.compose.material.icons.twotone.AccountBalanceWallet
import androidx.compose.material.icons.twotone.AttachMoney
import androidx.compose.material.icons.twotone.Expand
import androidx.compose.material.icons.twotone.Minimize
import androidx.compose.material.icons.twotone.Payment
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.authorizations.AuthorizationFunction
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.CREDIT_CLIENT
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationViewModule
import com.asmtunis.procaisseinventory.core.enum_classes.TicketState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.BonLivraisonRoute
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils.timbersValueSum
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.banques.domaine.Banque
import com.asmtunis.procaisseinventory.data.carte_resto.domaine.CarteResto
import com.asmtunis.procaisseinventory.nav_components.ID
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.PaymentBottomAppBar
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.PaymentConst
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.cash.CashPaymentInputView
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.cash.CashPaymentView
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.cheque.CheckPaymentInputView
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.cheque.ChequeTable
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.ticket_resto.TicketRestoInputView
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.ticket_resto.TicketRestoTable
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.custom_cards.ItemDetail
import com.simapps.ui_kit.utils.getCurrentDateTime
import kotlin.math.abs

@Composable
fun PaymentsScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    clientId: String,
    navDrawerViewModel: NavigationDrawerViewModel,
    mainViewModel: MainViewModel,
    selectArtMobilityVM: SelectArticleCalculViewModel,
    proCaisseViewModels: ProCaisseViewModels,
    dataViewModel: DataViewModel,
    locationViewModule: LocationViewModule,
    networkViewModel: NetworkViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    settingViewModel: SettingViewModel
) {
   val bonLivraisonVM = proCaisseViewModels.bonLivraisonViewModel
   val paymentViewModel = proCaisseViewModels.paymentViewModel
   val bonCommandeVM = proCaisseViewModels.bonCommandeViewModel

    val context = LocalContext.current
    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val selectedBonLivraisonWithFactureAndPayments = bonLivraisonVM.selectedBonLivraisonWithFactureAndPayments
    val ticket = selectedBonLivraisonWithFactureAndPayments.ticket

    val selectedBonCommandeWithClient = bonCommandeVM.selectedBonCommandeWithClient
    //if from bon commande (bc to bl) get bonCommande!!.dEVNum else get bl codeM
    val bonCommande = selectedBonCommandeWithClient.bonCommande

    val clientFromBonCommande = selectedBonCommandeWithClient.client

    val codeM = bonCommande?.dEVNum?.ifEmpty { bonLivraisonVM.codeM } ?:ticket?.tIKNumTicketM?.ifEmpty { bonLivraisonVM.codeM } ?: bonLivraisonVM.codeM

   val listActifTimber = mainViewModel.listActifTimber
    val update = bonLivraisonVM.update


    val banqueList = mainViewModel.banqueList
    val clientList = mainViewModel.clientList
    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val clientByCode = clientList.firstOrNull { it.cLICode == clientId }?: Client()//mainViewModel.clientByCode

    val client = clientFromBonCommande?: if (update) selectedBonLivraisonWithFactureAndPayments.client ?: Client() else clientByCode

    val clientHaveCreditPermission = AuthorizationFunction.haveAuth(value = client.cLIIsCredit)

    val proCaisseAuthorization = getProCaisseDataViewModel.authorizationList
    val userHaveBlCreditAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == CREDIT_CLIENT }

    val listStationStockArticl = mainViewModel.stationStockArticlMapByBarCode

    val selectedPaymentMode = bonLivraisonVM.selectedPaymentMode
    val selectedListChequeCaisse = paymentViewModel.selectedListChequeCaisse
    val selectedTicketRestoList = paymentViewModel.selectedTicketRestoList

    val selectedArticleMobilityList = selectArtMobilityVM.selectedArticleList

   val requestFocus = paymentViewModel.requestFocus

  //  val totPriceWithoutDicount = Calculations.totalPriceWithoutDiscountTTC(listArt = selectedArticleMobilityList)
   // val totPriceWithoutDicount = CalculationsUtils.totalPriceTTC(selectedArticleMobilityList)
    val totPriceWithoutDicount = selectArtMobilityVM.totPriceWithoutDicount

    val totPriceTTCWithDicount = selectArtMobilityVM.totalPriceAfterDiscountChange
    //TODO SEE IF RECALCULATE IS NEEDED
 //   val totPriceTTCWithDicount = totPriceWithoutDicount - selectedArticleMobilityList.sumOf { it.mntDiscount }


    val haveAutoBLTimbreEnabledAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.BL_TIMBRE }

    val haveReglementPartielAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.REGLEMENT_PARTIEL }

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig


    val utilisateur = mainViewModel.utilisateur
    val exerciceList = mainViewModel.exerciceList
    val sessionCaisse = navDrawerViewModel.sessionCaisse

   val montantTotalCheques = paymentViewModel.montantTotalCheques




    val totPriceTTCWithDicountAndTimber = selectArtMobilityVM.totPriceTTCWithDicountAndTimber


     val showChequeDetail = paymentViewModel.showChequeDetail
    val showTicketRestoDetail =paymentViewModel.showTicketRestoDetail



    val restPaymentValue = paymentViewModel.restPaymentValue
    val cashValue = paymentViewModel.cashValue
    val montantTotalTicketResto = paymentViewModel.montantTotalTicketResto

    LaunchedEffect(
        key1 = cashValue,
        key2 = montantTotalCheques,
        key3 = montantTotalTicketResto
    ) {
        paymentViewModel.onRestPaymentValueChange(total = totPriceTTCWithDicountAndTimber)
    }

    LaunchedEffect(key1 = restPaymentValue) {
        val paymentType =
            if (stringToDouble(restPaymentValue) == 0.0 || stringToDouble(restPaymentValue) == -0.0 || stringToDouble(restPaymentValue) < 0.0)
                TicketState.PAYED.getValue()
            else {
                if(totPriceTTCWithDicountAndTimber == stringToDouble(restPaymentValue))
                TicketState.CREDIT.getValue()
                else TicketState.PARTIAL_PAYMENT.getValue()
            }

        paymentViewModel.onPaymentTypeChange(paymentType)
    }


//    val paymentsValueAreValid = ((stringToDouble(restPaymentValue) == 0.0 || stringToDouble(restPaymentValue) == -0.0)
//            && bonLivraisonVM.selectedPaymentMode == bonLivraisonVM.paymentModeList[0]) || (stringToDouble(restPaymentValue) != totPriceTTCWithDicountAndTimber
//                    && bonLivraisonVM.selectedPaymentMode == bonLivraisonVM.paymentModeList[1])


    val typePayment = paymentViewModel.paymentType


    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = {
                    popBackStack()
                },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,


                title = client.cLINomPren + " (" + client.cLICode + ")",//stringResource(id = R.string.invpat_number_field),
            )
        },
        bottomBar = {//haveReglementPartielAuthorisation
            PaymentBottomAppBar(
                floatingActionButtonVisibility = if(typePayment == TicketState.PARTIAL_PAYMENT.getValue()) haveReglementPartielAuthorisation
                else {
                    if (typePayment == TicketState.CREDIT.getValue()) {
                        clientHaveCreditPermission && userHaveBlCreditAuthorisation
                    }
                    else true
                }, //paymentsValueAreValid,
                onTicketClick = { paymentViewModel.onShowPaimentTicketRestoChange(true) },
                onChequeClick = {
                    paymentViewModel.onRequestFocusChange(true)
                    paymentViewModel.onShowPaimentChequeChange(true)
                },
                onEspeceClick = {
                    paymentViewModel.onShowPaimentEspeceChange(true)
                  //  if (stringToDouble(restPaymentValue) >= 0.001) {
                        paymentViewModel.onCashValueChange(
                            //  convertStringToDoubleFormat(restPaymentValue)
                            restPaymentValue
                        )
                  //  }
                },
                onFloatingActionButtonClick = {
//                        if (stringToDouble(restPaymentValue) < 0) {
//
//                            showToast(
//                                context = context,
//                                toaster = toaster,
//                                message = context.resources.getString(R.string.reste) + "\n "+ context.resources.getString(R.string.rest_is_negatif, StringUtils.convertStringToPriceFormat(restPaymentValue)),
//                                type =  ToastType.Error,
//                            )
//                            return@PaymentBottomAppBar
//                        }

                        val maxNumTicket =
                            if (update) dataViewModel.getMaxNumTicket() else dataViewModel.getMaxNumTicket() + 1

                        if (update) {
                            bonLivraisonVM.deleteBLNotSync(
                                articleMapByBarCode = articleMapByBarCode,
                                clientList = clientList,
                                listStationStockArticl = listStationStockArticl,
                                updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                                    mainViewModel.updateQtePerStation (
                                        newQteStation = newQteStation,
                                        newSartQteDeclare = newSartQteDeclare,
                                        codeArticle = codeArticle,
                                        codeStation = codeStation
                                    )
                                },
                                updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                                    mainViewModel.updateArtQteStock(
                                        newQteAllStations = newQteAllStations,
                                        newQteStation = newQteStation,
                                        codeArticle = codeArticle
                                    )
                                }
                            )
                        }

                    val currentDate = getCurrentDateTime()
                    val haveTimber = bonLivraisonVM.haveTimbre(
                        client = client,
                        autoBLTimbreEnabled = haveAutoBLTimbreEnabledAuthorisation
                    )
                    val mntTTC = selectedArticleMobilityList.sumOf { stringToDouble(it.lTMtTTC) }

                    val timbersValueSum = timbersValueSum(listActifTimber = listActifTimber)
                    val tIKMtTTC = if (haveTimber) (mntTTC + timbersValueSum).toString() else mntTTC.toString()
                        bonLivraisonVM.saveNewBonLivraison(
                            typePayment = typePayment,
                            currentDate = currentDate,
                            haveTimber = haveTimber,
                            tIKMtTTC = tIKMtTTC,
                            cashValue = cashValue,
                            montantTotalCheques = montantTotalCheques,
                            montantTotalTicketResto = montantTotalTicketResto,
                            listStationStockArticl = listStationStockArticl,
                            sessionCaisse = sessionCaisse,
                            customAdress = bonLivraisonVM.customAdress,
                            totalDiscount = selectArtMobilityVM.totalDiscount,
                            exerciceCode = exerciceList.first().exerciceCode,
                            utilisateur = utilisateur,
                            client = client,
                            bonLivraisonCodeM = codeM,
                            selectedArticleList = selectedArticleMobilityList,
                            listActifTimber = listActifTimber,
                            nbrLinTIK = selectedArticleMobilityList.size,
                            location = locationViewModule.locationState,
                            maxNumTicket = maxNumTicket.toString(),
                            saveBonTransfert = { ticket ->
                                bonLivraisonVM.saveBonTransfert(ticket = ticket)
                            },
                            setMaxNumTicket = {
                                dataViewModel.saveMaxNumTicket(maxNumTicket)
                            },
                            updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                                mainViewModel.updateQtePerStation(
                                    newQteStation = newQteStation,
                                    newSartQteDeclare = newSartQteDeclare,
                                    codeArticle = codeArticle,
                                    codeStation = codeStation
                                )
                            },
                            updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                                mainViewModel.updateArtQteStock(
                                    newQteAllStations = newQteAllStations,
                                    newQteStation = newQteStation,
                                    codeArticle = codeArticle
                                )
                            }

                        )

                        // if (selectedPaymentMode == bonLivraisonVM.paymentModeList.first())
                        if (typePayment == TicketState.PAYED.getValue() || typePayment == TicketState.PARTIAL_PAYMENT.getValue()) {
                            paymentViewModel.savePayment(
                                isRegPart = typePayment == TicketState.PARTIAL_PAYMENT.getValue(),
                                dateTime = currentDate,
                                tIKMtTTC = tIKMtTTC,
                                canSync = false,
                                codeM = codeM,
                                sessionCaisse = sessionCaisse,
                                client = client,
                                user = utilisateur,
                                exercice = exerciceList.first().exerciceCode,
                                rEGCNumTicket = maxNumTicket.toLong(),
                                regRemarque = PaymentConst.VENTE_JOUR_PAYEMENT_REMARK
                            )
                        }

                        mainViewModel.resetClientByCode()

                        bonLivraisonVM.restBonLivraison()
                    bonLivraisonVM.onCustomAdressChange("")



                        NavigationDrawerViewModel.proCaisseDrawerItems.find { it.id == ID.BON_LIVRAISON_ID }
                            ?.let { navDrawerViewModel.onSelectedMenuChange(it) }


                            navigate(BonLivraisonRoute(clientId = ""))


                }
            )
        }

    ) { padding ->
        if (paymentViewModel.showPaimentEspece) {
            CashPaymentInputView(
                cashValue = cashValue,
                onValueChange = {
                    paymentViewModel.onCashValueChange(it)
                },
                cashErrorValue = null,
                onDismissRequest = {
                    paymentViewModel.onShowPaimentEspeceChange(false)
                }
            )
        }



        if (paymentViewModel.showPaimentCheque) {
            CheckPaymentInputView(
                montantCheque = paymentViewModel.montantCheque,
                requestFocus = requestFocus,
                montantChequeError = null,
                onMontantChequeChange = {
                    paymentViewModel.onMontantChequeChange(it)
                },
                checkNbr = paymentViewModel.numeroCheque,
                checkNbrError = null,
                onCheckNbrChange = {
                    paymentViewModel.onNumeroChequeChange(it)
                },
                checkDeadline = paymentViewModel.echeance,
                checkDeadlineError = null,
                onCheckDeadlineChange = { paymentViewModel.onEcheanceChange(it) },
                onDismissRequest = {
                    paymentViewModel.resetChequeVariable()
                    paymentViewModel.onShowPaimentChequeChange(false)
                },
                onAddClicked = {
                    // textValidationViewModel.onAddChequeEvent(AddChequeFormEvent.SubmitAddCheque)
                    paymentViewModel.addPaimentCheque(
                        codeM = codeM,
                        client = client,
                        utilisateur = utilisateur,
                        exerciceList = exerciceList,
                        sessionCaisse = sessionCaisse
                    )
                },
                bankList = banqueList,
                onSelectedBanqueChange = {
                    //  textValidationViewModel.onAddChequeEvent(AddChequeFormEvent.Bank(it))
                    paymentViewModel.onBankChange(it)
                },
                isBanqueExpanded = paymentViewModel.banqueExpand,
                onBanqueExpandChange = {
                    paymentViewModel.onBanqueExpandChange(it)
                },
                selectedBanque = paymentViewModel.bank,
                selectedBanqueError = null,
                showDatePicker = mainViewModel.showDatePicker,
                onShowDatePickerChange = {
                    mainViewModel.onShowDatePickerChange(it)
                }

            )
        }



        if (paymentViewModel.showPaimentTicketResto) {
            TicketRestoInputView(

                montantTicketResto = paymentViewModel.montantTicketResto,
                montantTicketRestoError = null,
                onMontantTicketRestoChange = { paymentViewModel.onMontantTicketRestoChange(it) },
                ticketRestoNbr = paymentViewModel.ticketRestoNbr,
                ticketRestoNbrError = null,
                onTicketRestoNbrChange = { paymentViewModel.onTicketRestoNbrChange(it) },
                taux = paymentViewModel.tauxTicketResto,
                tauxError = null,
                onTicketRestoTauxChange = { paymentViewModel.onTauxTicketRestoChange(it) },
                onDismissRequest = {
                    paymentViewModel.resetTicketRestoVariable()
                    paymentViewModel.onShowPaimentTicketRestoChange(false)
                },
                onAddClicked = {
                   paymentViewModel.addTicketRestoPayment(
                       codeM = codeM,
                       client = client,
                       exerciceList = exerciceList,
                    sessionCaisse = sessionCaisse
                    )
                },
                ticketRestoList = mainViewModel.ticketRestoList,
                onSelectedTicketRestoChange = {
                    //  textValidationViewModel.onAddTicketRestoEvent(AddTicketRestoFormEvent.Bank(it))
                    paymentViewModel.onSelectedTicketRestoChange(it)

                },
                isTicketRestoExpanded = paymentViewModel.ticketRestoExpand,
                onTicketRestoExpandChange = {
                    paymentViewModel.onTicketRestoExpandChange(it)
                },
                selectedTicketResto = paymentViewModel.selectedTicketResto,
                selectedTicketRestoError = null

            )
        }


        Column(modifier = Modifier.padding(padding)) {


            Spacer(modifier = Modifier.height(12.dp))
            ItemDetail(
                modifier = Modifier.padding(top = 8.dp, bottom = 8.dp),
                title = if(stringToDouble(restPaymentValue) < 0.0) stringResource(id = R.string.made_field_title) else stringResource(id = R.string.reste),
                dataText = if(stringToDouble(restPaymentValue) < 0.0) StringUtils.convertStringToPriceFormat(abs(stringToDouble(restPaymentValue)).toString())/*.replace("-", "")*/ else StringUtils.convertStringToPriceFormat(restPaymentValue),
                icon = Icons.TwoTone.Payment
            )

            ItemDetail(
                modifier = Modifier.padding(top = 8.dp, bottom = 8.dp),
                title = stringResource(id = R.string.total),
                dataText = StringUtils.convertStringToPriceFormat(totPriceTTCWithDicountAndTimber.toString()),
                icon = Icons.TwoTone.AttachMoney
            )

            ItemDetail(
                modifier = Modifier.padding(top = 8.dp, bottom = 8.dp),
                title = stringResource(id = R.string.mode_payment),
                dataText = typePayment,
                icon = Icons.TwoTone.AccountBalanceWallet,
                tint = when (typePayment) {
                    TicketState.PAYED.getValue() -> MaterialTheme.colorScheme.outline
                    TicketState.CREDIT.getValue() -> MaterialTheme.colorScheme.error
                    TicketState.PARTIAL_PAYMENT.getValue() -> MaterialTheme.colorScheme.errorContainer
                    else -> LocalContentColor.current
                }

            )
            Spacer(modifier = Modifier.height(12.dp))
            AnimatedVisibility(
                visible = typePayment == TicketState.PARTIAL_PAYMENT.getValue() && !haveReglementPartielAuthorisation,
                enter = fadeIn(),
                exit = fadeOut()
            ) {
               Text(
                   modifier = Modifier.fillMaxWidth(),
                   textAlign = TextAlign.Center,
                   text = stringResource(id = R.string.cant_proceed_regl_partiel),
                   style = MaterialTheme.typography.titleLarge,
                   color = MaterialTheme.colorScheme.error
               )
            }

            AnimatedVisibility(
                visible = typePayment == TicketState.CREDIT.getValue() && (!clientHaveCreditPermission || !userHaveBlCreditAuthorisation),
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center,
                    text = if(!clientHaveCreditPermission) stringResource(id = R.string.client_cant_proceed_credit) else stringResource(id = R.string.cant_proceed_credit),
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.error
                )
            }







            Spacer(modifier = Modifier.height(12.dp))
            AnimatedVisibility(
                visible = cashValue.isNotEmpty() && stringToDouble(cashValue) != 0.0,
                enter = fadeIn(),
                exit = fadeOut()
            ) {

                CashPaymentView(
                    cashValue = cashValue,
                    onLongPress = {
                        paymentViewModel.onCashValueChange("")
                    },
                    onTap = {
                        paymentViewModel.onShowPaimentEspeceChange(true)
                    }
                )

            }

            Spacer(modifier = Modifier.height(12.dp))
            AnimatedVisibility(
                visible = selectedListChequeCaisse.isNotEmpty(),
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                OutlinedCard(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 16.dp, end = 16.dp)
                ) {
                    ItemDetail(
                        title = stringResource(id = R.string.check_title),
                        modifier = Modifier.padding(top = 18.dp, bottom = 18.dp),
                        dataText = StringUtils.convertStringToPriceFormat(montantTotalCheques),
                        icon = if(showChequeDetail) Icons.TwoTone.Minimize else Icons.TwoTone.Expand,
                        onClick = {
                            paymentViewModel.onShowChequeDetailChange(!showChequeDetail)
                        }
                    )
                }

            }

            AnimatedVisibility(
                visible = showChequeDetail && selectedListChequeCaisse.isNotEmpty(),
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                ChequeTable(
                    listChequeCaisse = selectedListChequeCaisse,
                    onTap = { index, banque ->
                        paymentViewModel.onRequestFocusChange(false)
                        paymentViewModel.onShowPaimentChequeChange(true)
                        paymentViewModel.onBankChange(banque)
                        paymentViewModel.onEcheanceChange(selectedListChequeCaisse[index].echeanceCheque ?: "N/A")
                        paymentViewModel.onNumeroChequeChange(selectedListChequeCaisse[index].numCheque)
                        paymentViewModel.onMontantChequeChange(selectedListChequeCaisse[index].montant)
                    },
                    onLongPress = { index ->
                        paymentViewModel.removeChequeCaisse(chequeCaisse = selectedListChequeCaisse[if (selectedListChequeCaisse.size == 1) 0 else index])
                        paymentViewModel.claculateMontantTotalCheques()

                    },
                    banque = { index ->
                        banqueList.firstOrNull { it.bANDes == selectedListChequeCaisse[index].banque }?: Banque(
                            bANCode = selectedListChequeCaisse[index].banque ?: "N/A",
                            bANDes = "N/A"
                        )
                    },
                    onShowChequeTableChange = { paymentViewModel.onShowChequeDetailChange(false) }
                )
            }





            Spacer(modifier = Modifier.height(12.dp))
            AnimatedVisibility(
                visible = selectedTicketRestoList.isNotEmpty(),
                enter = fadeIn(),
                exit = fadeOut()
            ) {

                OutlinedCard(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 16.dp, end = 16.dp)
                ) {
                    ItemDetail(
                        title = stringResource(id = R.string.resto_ticket),
                        modifier = Modifier.padding(top = 18.dp, bottom = 18.dp),
                        dataText = StringUtils.convertStringToPriceFormat(montantTotalTicketResto),
                        icon = if(showTicketRestoDetail) Icons.Default.Minimize else Icons.Default.Expand,
                        onClick = {
                            paymentViewModel.onShowTicketRestoDetailChange(!showTicketRestoDetail)
                        }
                        )
                }

            }


            Spacer(modifier = Modifier.height(12.dp))
            AnimatedVisibility(
                visible = showTicketRestoDetail && selectedTicketRestoList.isNotEmpty(),
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                TicketRestoTable(
                    listTraiteCaisse = selectedTicketRestoList,
                    onTap = { index, carteResto ->
                        paymentViewModel.onShowPaimentTicketRestoChange(true)


                        paymentViewModel.onSelectedTicketRestoChange(
                            CarteResto(
                                code = selectedTicketRestoList[if (selectedTicketRestoList.size == 1) 0 else index].tRAITCompteLocal,
                                societe = selectedTicketRestoList[if (selectedTicketRestoList.size == 1) 0 else index].tRAITType
                                    ?: "N/A"
                            )
                        )
                        paymentViewModel.onTauxTicketRestoChange(
                            selectedTicketRestoList[if (selectedTicketRestoList.size == 1) 0 else index].taux.toString()
                        )
                        paymentViewModel.onTicketRestoNbrChange(
                            selectedTicketRestoList[if (selectedTicketRestoList.size == 1) 0 else index].nbrTicket.toString()
                        )
                        paymentViewModel.onMontantTicketRestoChange(
                            selectedTicketRestoList[if (selectedTicketRestoList.size == 1) 0 else index].montantInitial.toString()
                        )

                    },
                    onLongPress = { index ->
                        paymentViewModel.removeTraiteCaisse(traiteCaisse = selectedTicketRestoList[if (selectedTicketRestoList.size == 1) 0 else index])
                        paymentViewModel.claculateMontantTotalTicketResto()

                    },
                    carteResto = { index ->
                        CarteResto(
                            code = selectedTicketRestoList[if (selectedTicketRestoList.size == 1) 0 else index].tRAITCompteLocal,
                            societe = selectedTicketRestoList[if (selectedTicketRestoList.size == 1) 0 else index].tRAITType
                                ?: "N/A"
                        )

                    },
                    onShowTicketRestoTableChange = {
                        paymentViewModel.onShowTicketRestoDetailChange(false)
                    }
                )
            }


        }
    }
}