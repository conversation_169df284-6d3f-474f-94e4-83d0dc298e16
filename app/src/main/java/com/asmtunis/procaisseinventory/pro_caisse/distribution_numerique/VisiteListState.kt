package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique


import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.LigneVisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.VisitesDn
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch


data class VisiteListState(
    val lists: Map<VisitesDn, List<LigneVisitesDn>> = emptyMap(),
    val codeClient : String = "",
    val listOrder: ListOrder = ListOrder.Date(OrderType.Descending),
    val filter: ListSearch = ListSearch.FirstSearch()
)