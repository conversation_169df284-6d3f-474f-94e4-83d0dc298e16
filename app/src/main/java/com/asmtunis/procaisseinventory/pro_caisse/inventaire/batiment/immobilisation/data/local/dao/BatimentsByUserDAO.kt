package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.BATIMENT_BY_USER
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentByUser
import kotlinx.coroutines.flow.Flow


@Dao
interface BatimentsByUserDAO {
    @get:Query("SELECT * FROM $BATIMENT_BY_USER order by CLI_NomPren asc")
    val all: Flow<List<BatimentByUser>?>



    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: BatimentByUser)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<BatimentByUser>)

    @Query("DELETE FROM $BATIMENT_BY_USER")
    fun deleteAll()




    @Query(
        "SELECT * FROM $BATIMENT_BY_USER " +
                "WHERE CLI_NomPren LIKE '%' || :filterString || '%' " +
               // "and  TyEmpImNom = 'ZONE DE CONSOMMATION' " +
                " ORDER BY " +

                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 1 THEN CLI_NomPren END ASC, " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 2 THEN CLI_NomPren END DESC, " +
                "CASE WHEN :sortBy = 'CLI_Code'  AND :isAsc = 1 THEN CLI_Code END ASC, " +
                "CASE WHEN :sortBy = 'CLI_Code'  AND :isAsc = 2 THEN CLI_Code END DESC, " +
                "CASE WHEN :sortBy = 'Clt_ImoCB'  AND :isAsc = 1 THEN Clt_ImoCB END ASC, " +
                "CASE WHEN :sortBy = 'Clt_ImoCB'  AND :isAsc = 2 THEN Clt_ImoCB END DESC "

    )
    fun filterByName(filterString: String, sortBy: String, isAsc: Int): Flow<List<BatimentByUser>>

    @Query(
        "SELECT * FROM $BATIMENT_BY_USER " +
                "WHERE CLI_Code LIKE '%' || :filterString || '%' " +
               // "and  TyEmpImNom = 'ZONE DE CONSOMMATION' " +
                " ORDER BY " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 1 THEN CLI_NomPren END ASC, " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 2 THEN CLI_NomPren END DESC, " +
                "CASE WHEN :sortBy = 'CLI_Code'  AND :isAsc = 1 THEN CLI_Code END ASC, " +
                "CASE WHEN :sortBy = 'CLI_Code'  AND :isAsc = 2 THEN CLI_Code END DESC, " +
                "CASE WHEN :sortBy = 'Clt_ImoCB'  AND :isAsc = 1 THEN Clt_ImoCB END ASC, " +
                "CASE WHEN :sortBy = 'Clt_ImoCB'  AND :isAsc = 2 THEN Clt_ImoCB END DESC "
    )
    fun filterByCLICode(filterString: String, sortBy: String, isAsc: Int): Flow<List<BatimentByUser>>

    @Query(
        "SELECT * FROM $BATIMENT_BY_USER " +
                "WHERE Clt_ImoCB LIKE '%' || :filterString || '%'" +
              //  "and  TyEmpImNom = 'ZONE DE CONSOMMATION' " +
                " ORDER BY " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 1 THEN CLI_NomPren END ASC, " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 2 THEN CLI_NomPren END DESC, " +
                "CASE WHEN :sortBy = 'CLI_Code'  AND :isAsc = 1 THEN CLI_Code END ASC, " +
                "CASE WHEN :sortBy = 'CLI_Code'  AND :isAsc = 2 THEN CLI_Code END DESC, " +
                "CASE WHEN :sortBy = 'Clt_ImoCB'  AND :isAsc = 1 THEN Clt_ImoCB END ASC, " +
                "CASE WHEN :sortBy = 'Clt_ImoCB'  AND :isAsc = 2 THEN Clt_ImoCB END DESC "
    )
    fun filterByCltImoCB(filterString: String, sortBy: String, isAsc: Int): Flow<List<BatimentByUser>>

    @Query(
        "SELECT * FROM $BATIMENT_BY_USER " +
             //   "WHERE TyEmpImNom = 'ZONE DE CONSOMMATION' " +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 1 THEN CLI_NomPren END ASC, " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 2 THEN CLI_NomPren END DESC, " +
                "CASE WHEN :sortBy = 'CLI_Code'  AND :isAsc = 1 THEN CLI_Code END ASC, " +
                "CASE WHEN :sortBy = 'CLI_Code'  AND :isAsc = 2 THEN CLI_Code END DESC, " +
                "CASE WHEN :sortBy = 'Clt_ImoCB'  AND :isAsc = 1 THEN Clt_ImoCB END ASC, " +
                "CASE WHEN :sortBy = 'Clt_ImoCB'  AND :isAsc = 2 THEN Clt_ImoCB END DESC "
    )
    fun getAllFiltred(isAsc: Int, sortBy: String): Flow<List<BatimentByUser>>


}