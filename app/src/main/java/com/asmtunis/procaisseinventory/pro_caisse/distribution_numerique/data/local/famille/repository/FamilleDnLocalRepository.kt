package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.famille.repository


import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.FamilleDn
import kotlinx.coroutines.flow.Flow

interface FamilleDnLocalRepository {

    fun upsert(value: FamilleDn)

    fun upsertAll(value: List<FamilleDn>)


    fun deleteAll()

    fun getAll(): Flow<List<FamilleDn>>

}