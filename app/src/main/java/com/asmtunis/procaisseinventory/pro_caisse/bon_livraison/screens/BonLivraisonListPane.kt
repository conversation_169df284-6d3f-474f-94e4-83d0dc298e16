package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.Compress
import androidx.compose.material3.DrawerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothInfoDialogue
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.BonLivraisonDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.RecapBonLivraisonRoute
import com.asmtunis.procaisseinventory.core.print.PrintFunctions
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.wifi.WifiPrintViewModel
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicketWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.BonLivraisonViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.reglement.ReglementUtils
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.bottom_sheet.CustomModalBottomSheet
import com.asmtunis.procaisseinventory.shared_ui_components.floating_button.SnapScrollingButton
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.ListItem
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyColumn
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.ModifiersUtils.floatingBtnIsVisible
import com.simapps.ui_kit.drop_down_menu.LargeDropdownMenu
import kotlinx.coroutines.launch
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BonLivraisonListPane (
    navigate: (route: Any) -> Unit,
    navigator: (route: Any) -> Unit,
  //  navigatorTo: () -> Unit,
    clientId: String,
    drawer: DrawerState,
    navDrawerViewmodel: NavigationDrawerViewModel,
    bonLivraisonVM: BonLivraisonViewModel,
    mainViewModel : MainViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    selectArtMobilityVM : SelectArticleCalculViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    printViewModel: PrintViewModel,
    wifiPrintVM: WifiPrintViewModel,
    settingViewModel: SettingViewModel,
    bluetoothVM: BluetoothViewModel,
    syncProcaisseViewModels: SyncProcaisseViewModels,


    ) {
    val syncBonLivraisonVM =  syncProcaisseViewModels.syncBonLivraisonVM
    val uiWindowState = settingViewModel.uiWindowState

    val isConnected = networkViewModel.isConnected
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val sheetState = rememberModalBottomSheetState()

    val sessionCaisseList = navDrawerViewmodel.sessionCaisseList
    val sessionCaisseFilter = bonLivraisonVM.sessionCaisseFilter
    val bonTransfertListstate = bonLivraisonVM.bonTransfertListstate
    val listFilter = bonTransfertListstate.search
    val listOrder = bonTransfertListstate.listOrder
    val filterList = context.resources.getStringArray(R.array.bn_livraison_filter)

    val ticketCaisseState  = getProCaisseDataViewModel.bonLivraisonState
    val ligneTicketCaisseState  = getProCaisseDataViewModel.ligneTicketCaisseState
    val sessionCaisseExpanded = bonLivraisonVM.sessionCaisseExpanded
    val selectedSessionCaisse = if(bonLivraisonVM.selectedSessionCaisse == SessionCaisse()) navDrawerViewmodel.sessionCaisse
    else bonLivraisonVM.selectedSessionCaisse

    val lgBnLivraisonAndArticleList = bonLivraisonVM.selectedListLgBonLivraisonWithArticle

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val printParams = dataViewModel.printData

    val listState = rememberLazyListState()

    val isVisible = floatingBtnIsVisible(
        listeSize = bonTransfertListstate.lists.size,
        canScrollForward = listState.canScrollForward
    )

    val isRefreshing  = ticketCaisseState.loading || ligneTicketCaisseState.loading || getProCaisseDataViewModel.sessionCaisseState.loading

    val prefixList = mainViewModel.prefixList
    val searchTextState = bonLivraisonVM.searchTextState
    val showSearchView = bonLivraisonVM.showSearchView

    val selectedBonLivraison = bonLivraisonVM.selectedBonLivraisonWithFactureAndPayments
    val selectedListLgBonLivraison = bonLivraisonVM.selectedListLgBonLivraison

    val ticketWithFactureAndPayments = bonLivraisonVM.ticketWithFactureAndPayments

    val selectedListLgBonLivraisonWithArticle =  bonLivraisonVM.selectedListLgBonLivraisonWithArticle

    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val utilisateur = mainViewModel.utilisateur
    val exerciceList = mainViewModel.exerciceList
    val exerciceCode = exerciceList.firstOrNull()?.exerciceCode?: ""
    val clientList = mainViewModel.clientList

    val firstTimeConnected = printViewModel.firstTimeConnected

    val clientByCode = clientList.firstOrNull { it.cLICode == clientId }?: Client()  //mainViewModel.clientByCode


    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)







    LaunchedEffect(key1 = selectedSessionCaisse) {
        bonLivraisonVM.onEvent(ListEvent.ThirdCustomFilter(selectedSessionCaisse.sCIdSCaisse))
    }



    LaunchedEffect(key1 = clientByCode) {
        if(clientByCode == Client()) return@LaunchedEffect

        bonLivraisonVM.onSearchValueChange(TextFieldValue(clientByCode.cLICode))
        bonLivraisonVM.onEvent(event = ListEvent.ListSearch(ListSearch.SecondSearch()))
    }

    LaunchedEffect(key1 = firstTimeConnected){
        if(!firstTimeConnected) return@LaunchedEffect

        printViewModel.awaitPrint(
            context = context,
            toPrint = {
                printViewModel.printTicket(
                    context = context,
                    ticketWithFactureAndPayments = selectedBonLivraison,
                    listLigneTicket = selectedListLgBonLivraisonWithArticle,
                    utilisateur = utilisateur,
                    printParams = printParams,
                    prefixList = prefixList
                )
            }
        )

        printViewModel.onFirstTimeConnectedChange(firstConnect = false)
        bonLivraisonVM.onShowCustomModalBottomSheetChange(false)
    }

    LaunchedEffect(
        key1 = searchTextState.text,
        key2 = bonTransfertListstate.lists,
        key3 = bonTransfertListstate.filterByTicketEtat
    ) {
        bonLivraisonVM.filterBonTransfert(bonTransfertListstate)
    }

    LaunchedEffect(
        key1 = mainViewModel.listTicketandLignes.size
    ) {
        bonLivraisonVM.filterBonTransfert(bonTransfertListstate)
    }

        Scaffold(
            topBar = {
                AppBar(
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    onNavigationClick = { scope.launch { drawer.open() } },
                    showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                    title = stringResource(id = navDrawerViewmodel.proCaisseSelectedMenu.title),
                    titleVisibilty = !showSearchView && searchTextState.text.isEmpty(),

                    actions = {
                        SearchSectionComposable(
                            label = context.getString(
                                R.string.filter_by,
                                when (listFilter) {
                                    is ListSearch.FirstSearch -> filterList[0]
                                    is ListSearch.SecondSearch -> filterList[1]
                                    else -> filterList[2]}),
                            searchVisibility = showSearchView || searchTextState.text.isNotEmpty(),
                            searchTextState = searchTextState,
                            onSearchValueChange = {
                                bonLivraisonVM.onSearchValueChange(TextFieldValue(it))
                                if(it == "")   {

                                    /** this bloc to handle search visibility when custom search by client*/

                                    /** this bloc to handle search visibility when custom search by client*/
                                //    mainViewModel.onSelectedClientChange(Client())
                                }
                            },
                            onShowSearchViewChange = {
                                bonLivraisonVM.onShowSearchViewChange(it)
                                if(!it) {
                                    /** this bloc to handle search visibility when custom search by client*/
                                    /** this bloc to handle search visibility when custom search by client*/
                                    bonLivraisonVM.onSearchValueChange(TextFieldValue(""))
                                  //  mainViewModel.onSelectedClientChange(Client())
                                }
                            },
                            onShowCustomFilterChange = {
                                bonLivraisonVM.onShowCustomFilterChange(it)
                            }
                        )


                    }
                )
            },
            floatingActionButton = {
                val density = LocalDensity.current
                Column(
                    verticalArrangement = Arrangement.spacedBy(12.dp) ,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    AnimatedVisibility(
                        visible = isVisible && !isRefreshing && bonTransfertListstate.lists.isNotEmpty(),
                        enter = slideInVertically { with(density) { 40.dp.roundToPx() } } + fadeIn(),
                        exit = fadeOut(animationSpec = keyframes { this.durationMillis = 120 })
                    ) {
                        FloatingActionButton(
                            onClick = {
                                selectArtMobilityVM.resetSelectedMobilityArticles()

                                val bonLivraison: MutableList<TicketWithFactureAndPayments> = arrayListOf()
                                val ligneBonLivraison: MutableList<LigneTicketWithArticle> = arrayListOf()



                                bonTransfertListstate.lists.forEach { (key, value) ->
                                    run {
                                        bonLivraison.add(key)
                                        ligneBonLivraison.addAll(value)
                                    }
                                }
                                bonLivraisonVM.addAllTiketMoney(
                                    tickets = bonLivraison.map { it.ticket?: Ticket() }.toMutableList(),
                                    ligneTicket = ligneBonLivraison
                                )

                                navigate(RecapBonLivraisonRoute)


                            }
                        ) {
                            Icon(
                                imageVector = Icons.TwoTone.Compress,
                                contentDescription = stringResource(id = R.string.add_Client_button)
                            )
                        }
                    }


                    SnapScrollingButton(
                        isScrollInProgress = listState.isScrollInProgress,
                        isVisible = remember { derivedStateOf { listState.firstVisibleItemIndex } }.value > 15 && isVisible,
                        density = density,
                        animateScrollToItem = {
                            listState.animateScrollToItem(index = it)
                        }
                    )
                }
            }

        ) { padding ->
            if(printViewModel.openPrintInfoDialogue) {
                BluetoothInfoDialogue(
                    printResult = printViewModel.printResult,
                    onOpenPrintInfoDialogueChange = {
                        printViewModel.onOpenPrintInfoDialogueChange(it)
                    }
                )
            }
            Column(
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding)
                    .padding(top = 12.dp)
            ) {


                if(syncBonLivraisonVM.responseFactureTicket.loading){
                    LottieAnim(lotti = R.raw.loading, size = 50.dp)
                }
                //else if(syncBonLivraisonVM.responseFactureTicket.data!=null)
                //     bonLivraisonVM.setBonTransfertList(ticket = bonLivraisonVM.bonTransfertListstate.lists)



                if (bonLivraisonVM.showCustomModalBottomSheet) {
                    CustomModalBottomSheet(
                        remoteResponseState = syncBonLivraisonVM.responseAddBatchTicketWithLignesState,
                        status = selectedBonLivraison.ticket?.status?:"",
                        title = context.getString(R.string.ticket_number_field, selectedBonLivraison.ticket?.tIKNumTicket),
                        showDeleteIcon = selectedBonLivraison.ticket?.isSync == false,
                        canInvoice = selectedBonLivraison.ticket?.tIKNumeroBL.isNullOrBlank() && selectedBonLivraison.ticket?.isSync == true,
                        onDismissRequest= { bonLivraisonVM.onShowCustomModalBottomSheetChange(false) },
                        onDeleteRequest= {
                            bonLivraisonVM.deleteBLNotSync(
                                articleMapByBarCode = articleMapByBarCode,
                                clientList = clientList,
                                listStationStockArticl = mainViewModel.stationStockArticlMapByBarCode,
                                updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation->
                                    mainViewModel.updateQtePerStation (
                                        newQteStation = newQteStation,
                                        newSartQteDeclare = newSartQteDeclare,
                                        codeArticle = codeArticle,
                                        codeStation = codeStation
                                    )
                                },
                                updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                                    mainViewModel.updateArtQteStock(
                                        newQteAllStations = newQteAllStations,
                                        newQteStation = newQteStation,
                                        codeArticle = codeArticle
                                    )
                                }
                            )

                        },
                        onPrintRequest= {
                            PrintFunctions.print(
                                context = context,
                                toaster = toaster,
                                printParams = printParams,
                                navigate = { navigate(it) },
                                printViewModel = printViewModel,
                                bluetoothVM = bluetoothVM,
                                toPrintBT = {
                                    printViewModel.printTicket(
                                        context = context,
                                        ticketWithFactureAndPayments = selectedBonLivraison,
                                        listLigneTicket = selectedListLgBonLivraisonWithArticle,
                                        utilisateur = utilisateur,
                                        printParams = printParams,
                                        prefixList = prefixList
                                    )
                                },
                                toPrintWifi = {
                                    wifiPrintVM.createTicketPDFFile(
                                        context = context,
                                        listLigneTicketWithArticle = selectedListLgBonLivraisonWithArticle,
                                        ticketWithFactureAndPayments = selectedBonLivraison,
                                        printParams = printParams,
                                        listener = {}
                                    )
                                }
                            )
                        },
                        onFacturer = { syncBonLivraisonVM.facturerBL(ticketWithFactureAndPayments = selectedBonLivraison) },
                        onSyncRequest = { syncBonLivraisonVM.syncBonLivraison(selectedTicket = selectedBonLivraison) }

                    )
                }


                if (bonLivraisonVM.showCustomFilter) {
                    FilterContainer(
                        filterList = filterList,
                        listFilter = listFilter,
                        listOrder = listOrder,
                        orderList = context.resources.getStringArray(R.array.bn_livraison_order),
                        onShowCustomFilterChange  = { bonLivraisonVM.onShowCustomFilterChange(false) },
                        onEvent = { bonLivraisonVM.onEvent(it) },
                        customFilterContent = {
                            val ticketEtatList = listOf<String>("Regler","Credit")
                            FilterSectionComposable (
                                title  = stringResource(id = R.string.filter_by_etat_ticket),
                                currentFilterLable =  bonTransfertListstate.filterByTicketEtat,
                                onAllEvent = {
                                    bonLivraisonVM.onEvent(ListEvent.FirstCustomFilter(""))
                                },
                                onEvent = {
                                    bonLivraisonVM.onEvent(ListEvent.FirstCustomFilter(ticketEtatList[it]))
                                },

                                filterCount =  ticketEtatList.size,
                                customFilterCode  = {
                                    ticketEtatList[it]
                                },
                                filterLabel  = {
                                    ticketEtatList[it]
                                }
                            )



                    val ticketSourceList = listOf<String>("Mobile")

                            FilterSectionComposable (
                                title  = stringResource(id = R.string.filter_by_source_ticket),
                                currentFilterLable =  bonTransfertListstate.filterByTicketSource,
                                onAllEvent = {
                                    bonLivraisonVM.onEvent(ListEvent.SecondCustomFilter(""))
                                },
                                onEvent = {
                                    bonLivraisonVM.onEvent(ListEvent.SecondCustomFilter(ticketSourceList[it]))
                                },

                                filterCount =  ticketSourceList.size,
                                customFilterCode  = {
                                    ticketSourceList[it]
                                },
                                filterLabel  = {
                                    ticketSourceList[it]
                                }
                            )

                        }
                    )


                }


              if(sessionCaisseList.isNotEmpty())  {
                  LargeDropdownMenu(
                      modifier = Modifier.fillMaxWidth(0.95f),
                      designation = selectedSessionCaisse.sCIdSCaisse + " " + (selectedSessionCaisse.ddm?.substringBefore(".")?: ""),
                      errorValue = null,
                      itemList = if(sessionCaisseFilter.isNotEmpty()) sessionCaisseList.filter { it.sCIdSCaisse.lowercase(Locale.ROOT).contains(sessionCaisseFilter.lowercase(Locale.ROOT)) } else sessionCaisseList,
                      getItemDesignation = { it.sCIdSCaisse + " " + (it.ddm?.substringBefore(".")?:"") },
                      label = stringResource(R.string.session_caisse),
                      readOnly =  true,
                      fiterValue = sessionCaisseFilter,
                      onFilterValueChange = { bonLivraisonVM.onSessionCaisseFilterChange(it) },
                      showFilter = true,
                      showTrailingIcon = sessionCaisseList.size > 1,
                      itemExpanded = sessionCaisseExpanded,
                      selectedItem = selectedSessionCaisse,
                      onItemExpandedChange = { bonLivraisonVM.onSessionCaisseExpandedChange(it) },
                      onClick = {
                          bonLivraisonVM.onSelectedSessionCaisseChange(it)
                          bonLivraisonVM.onSessionCaisseExpandedChange(false)

                          if(!isConnected){
                              showToast(
                                  context = context,
                                  toaster = toaster,
                                  message = context.resources.getString(R.string.no_connection),
                                  type =  ToastType.Warning,
                              )
                              return@LargeDropdownMenu
                          }

                          getProCaisseDataViewModel.getTickets(
                              baseConfig = selectedBaseconfig,
                              sCIdSCaisse = it.sCIdSCaisse,
                              ddm = it.ddm?: "",
                              exercice = exerciceList.first().exerciceCode,
                              archive = false,
                              zone = true
                          )

                          getProCaisseDataViewModel.getFatures(baseConfig = selectedBaseconfig)
                      }
                      )
              }

                if(isRefreshing)
                    LottieAnim(lotti = R.raw.loading, size = 250.dp)
                else {
                    if (bonTransfertListstate.lists.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(18.dp))
                        BonLivraisonList(
                            selectedBonLivraison = selectedBonLivraison,
                            isConnected = isConnected,
                            isRefreshing = false,
                            selectedBaseconfig = selectedBaseconfig,
                            exerciceCode = exerciceCode,
                            utilisateur = utilisateur,
                            clientList = clientList,
                            ticketWithFactureAndPayments = ticketWithFactureAndPayments,
                            getProCaisseDataViewModel = getProCaisseDataViewModel,
                            onItemClick = {
                                bonLivraisonVM.restBonLivraison()
                                selectArtMobilityVM.resetSelectedMobilityArticles()
                                bonLivraisonVM.onSelectedBonLivraisonChange(it)


                                selectArtMobilityVM.setControlQte(false)
                                /* TODO COMPLETE MODIFY BL NOT SYNC

                                  val destination: String
                                  if (it.keys.first().ticket!!.status == ItemStatus.INSERTED.status){
                                      bonLivraisonVM.onUpdateChange(true)
                                      destination = Screen.AddBonLivraison.Route
                                      mainViewModel.canModify(true)
                                      for (lgTicketWithArt in it.values.first()) {
                                          val lgTicket = lgTicketWithArt.ligneTicket
                                          val artic = lgTicketWithArt.article?:Article()
                                          val prixVente = selectArtMobilityVM.getPrice(artic)
                                          val remise = lgTicket?.lTRemise?:"0"
                                          val prixCaisse = StringFormatting.stringToDouble(prixVente) * (1 - (StringFormatting.stringToDouble(
                                              remise
                                          ) /100))

                                          val seletedArt = SelectedArticleMobility(
                                              article = artic,
                                              //  selectedPriceCategory = lgTicketWithArt.selectedPriceCategory.ifEmpty { Globals.PRIX_PUBLIQUE },
                                              quantity = lgTicket?.lTQte?:"1",
                                              quantityError = null,
                                              prixCaisse = prixCaisse.toString(),//Prix Apré remise
                                              prixVente = selectArtMobilityVM.getPrice(artic),//Prix Avant remise
                                              discount = remise, //taux remise
                                              mntDiscount = StringFormatting.stringToDouble(prixVente) - prixCaisse,//mnt remise
                                              discountError = null,
                                              lTMtTTC = lgTicket?.lTMtTTC?: prixVente,
                                              mtTTCError = null,
                                              lTMtHT = lgTicket?.lTMtHT?:lgTicket?.lTMtTTC?: prixVente// stringToDouble(lgBonCommande.lGDEVMntHT)
                                          )

                                          selectArtMobilityVM.addOneItemToSelectedArticleMobilityList(seletedArt)


                                      }
                                  }


                                      else {
                                          destination = Screen.BonLivraisonDetail.Route

                                  }*/






                                for (i in lgBnLivraisonAndArticleList.indices) {
                                    val article = lgBnLivraisonAndArticleList[i].article?: Article(aRTCodeBar = lgBnLivraisonAndArticleList[i].ligneTicket?.lTCodArt?: "", aRTCode = lgBnLivraisonAndArticleList[i].ligneTicket?.lTCodArt?: "")

                                    val lgBonLiv = lgBnLivraisonAndArticleList[i].ligneTicket?: LigneTicket()
                                    val selectedArticle = SelectedArticle(
                                        article = article,
                                        quantity = lgBonLiv.lTQte,
                                        prixCaisse = lgBonLiv.lTPrixEncaisse,
                                        discount = lgBonLiv.lTTauxRemise,
                                        mntDiscount = lgBonLiv.lTRemise,
                                        lTMtTTC = lgBonLiv.lTMtTTC,
                                        lTMtBrutHT = lgBonLiv.lTMtHT,
                                        prixVente = lgBonLiv.lTPrixVente,
                                        tva = Tva(),
                                        mntTva = ""
                                    )
                                    selectArtMobilityVM.setConsultationSelectedArticleMobilityList(selectedArticle)


                                }
                                navigator(BonLivraisonDetailRoute)
                               // navigatorTo()

                            },
                            onMoreClick = {
                                bonLivraisonVM.restBonLivraison()
                                selectArtMobilityVM.resetSelectedMobilityArticles()
                                bonLivraisonVM.onSelectedBonLivraisonChange(it)
                                bonLivraisonVM.onShowCustomModalBottomSheetChange(true)
                            },
                            filteredList = bonTransfertListstate.lists,
                            listState = listState
                        )
                    }
                    else {
                        LottieAnim(lotti = if(ticketCaisseState.error!=null) R.raw.network_error else R.raw.emptystate, size = 250.dp)

                        if(ticketCaisseState.error!=null) {
                            Text(
                                text = ticketCaisseState.error,
                                color = MaterialTheme.colorScheme.error
                            )
                        }


                    }

                }


            }
        }

}

@Composable
fun BonLivraisonList(
    isConnected: Boolean,
    selectedBaseconfig: BaseConfig,
    exerciceCode: String,
    utilisateur: Utilisateur,
    isRefreshing: Boolean,
    selectedBonLivraison: TicketWithFactureAndPayments,
    clientList: List<Client>,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    filteredList: Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>,
    ticketWithFactureAndPayments: MutableList<TicketWithFactureAndPayments>,
    onItemClick :(Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>) -> Unit,
    onMoreClick :(Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>) -> Unit,
    listState: LazyListState
) {

    val context = LocalContext.current



    PullToRefreshLazyColumn(
        items = ticketWithFactureAndPayments,
        lazyListState = listState,
        isRefreshing = isRefreshing,
        pullToRefreshEnabled = !ticketWithFactureAndPayments.any { it.ticket?.isSync != true } && isConnected,
        onRefresh = {
            /**
             * Get getSessionCaissesByUser not  getTickets to ensure no conflict
             * also applicable for bon retour and bon commande
             */
            /**
             * Get getSessionCaissesByUser not  getTickets to ensure no conflict
             * also applicable for bon retour and bon commande
             */
            getProCaisseDataViewModel.getSessionCaissesByUser(
                baseConfig = selectedBaseconfig,
                utilisateur = utilisateur,
                clientList = clientList,
                exerciceCode = exerciceCode
            )
        },
        key = { ticketWithFactureAndPayment -> ticketWithFactureAndPayment.ticket?.tIKNumTicketM?: ticketWithFactureAndPayment },
        content = { ticketWithFactureAndPayment ->
            val bonLivraison = ticketWithFactureAndPayment.ticket?: Ticket()

            val mntTTC = if(ticketWithFactureAndPayment.facture!= null)
                ticketWithFactureAndPayment.facture?.factMntTTC
            else bonLivraison.tIKMtTTC

            val reglementTYPE = ReglementUtils.getReglementType(
                reglementCaisse = ticketWithFactureAndPayment.reglement?: ReglementCaisse(),
                listTicket = listOf(bonLivraison),
                context = context
            )
            ListItem(
                isSelected = ticketWithFactureAndPayment == selectedBonLivraison,
                onItemClick = {
                    onItemClick(filteredList.filter { it.key.ticket?.tIKNumTicketM == bonLivraison.tIKNumTicketM })


                },
                firstText = stringResource(R.string.ticket_number_field, bonLivraison.tIKNumTicket) + if(!bonLivraison.tIKNumeroBL.isNullOrEmpty()) " (${stringResource(R.string.fact, bonLivraison.tIKNumeroBL)})" else "",
                /*else  bonLivraison[index].tIKNumTicketM*/
                secondText = bonLivraison.tIKNomClient,
                thirdText = StringUtils.convertStringToPriceFormat(mntTTC) + " (" + reglementTYPE

                        /* getTicketEtat(context = context,
                     etat = bonLivraison.tIKEtat
                 )*/ + ")",
                forthText = if(reglementTYPE == stringResource(R.string.paiement_Partiel)) {
                    stringResource(R.string.montant_paye,  StringUtils.convertStringToPriceFormat(ticketWithFactureAndPayment.reglement?.rEGCMontant.toString()))
                } else { "" },
                dateText = (if (bonLivraison.tIKNumeroBL.isNullOrBlank()) "("+  stringResource(R.string.not_invoiced) +") "  else "")+
                        bonLivraison.syncErrorMsg.ifEmpty { if (bonLivraison.tIKDDmFormatted != "empty") bonLivraison.tIKDDmFormatted else bonLivraison.tIKDateHeureTicket },
                dateTextRemarque = if (bonLivraison.tIKNumeroBL.isNullOrBlank()) "" else stringResource(
                    R.string.invoiced),
                isSync = bonLivraison.isSync,
                status = bonLivraison.status,
                onResetDeletedClick = {
                },
                onMoreClick = {
                    onMoreClick(filteredList.filter { it.key.ticket == bonLivraison })

                },

                )
        },
    )

}
