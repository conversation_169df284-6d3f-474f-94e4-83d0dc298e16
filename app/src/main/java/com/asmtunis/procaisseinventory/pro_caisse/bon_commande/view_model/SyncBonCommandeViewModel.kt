package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.view_model

import android.util.Log // Importation pour le logging
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.model.NestedItem
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.InvPatBatchResponse
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject


@HiltViewModel
class SyncBonCommandeViewModel @Inject constructor(
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val dataStoreRepository: DataStoreRepository,
    private val listenNetwork: ListenNetwork,
    // app: Application
) : ViewModel() {

    private var autoSyncState  by mutableStateOf(false)
    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(key = PROCAISSE_AUTO_SYNC_AUTHORISATION, default = true).distinctUntilChanged()
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()

    private var  connected  by mutableStateOf(false)

    init {
        getNotSyncBonCommande()

    }


    var responseAddBonCommandeState: RemoteResponseState<List<InvPatBatchResponse>>  by mutableStateOf(RemoteResponseState())
        private set


    var bonCommandeNotSync: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set
    var notSyncBonCommandeObj : String by mutableStateOf("")
        private set


    private fun getNotSyncBonCommande() {
        viewModelScope.launch {
            val bonCommandeNotSyncFlow =  proCaisseLocalDb.bonCommande.notSynced().distinctUntilChanged()

            combine(networkFlow, bonCommandeNotSyncFlow, autoSyncFlow) { isConnected, bonCommandeNotSyncList, autoSync ->
                Triple(
                    isConnected,
                    autoSync,
                    bonCommandeNotSyncList?.ifEmpty { emptyMap() } ?: emptyMap()
                )
            }.collect { (isConnected, autoSync, bonCommandeNotSyncList) ->
                // Update state on main thread to avoid concurrent modification
                connected = isConnected
                autoSyncState = autoSync

                if (bonCommandeNotSyncList.isEmpty()) {
                    bonCommandeNotSync = emptyMap()
                    return@collect
                }
                bonCommandeNotSync = bonCommandeNotSyncList
                if(connected && autoSyncState) syncBonCommande()
            }
        }
    }



    fun syncBonCommande(selectedBonCommande: BonCommande = BonCommande()) {
        viewModelScope.launch(dispatcherIO) {
            val listVisiteWithLinesDn = ArrayList<NestedItem<BonCommande, List<LigneBonCommande>>>()

            bonCommandeNotSync.forEach { (key, value) ->
                listVisiteWithLinesDn.add(
                    NestedItem(
                        parent  = key,
                        children  = value
                    )
                )
            }

            if(selectedBonCommande != BonCommande()) {
                listVisiteWithLinesDn.removeIf { it.parent?.devCodeM != selectedBonCommande.devCodeM }
            }

            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                Json.encodeToJsonElement(listVisiteWithLinesDn)
            )

            notSyncBonCommandeObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.bonCommande.addBatchBonCommande(notSyncBonCommandeObj).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        //val ddm =  DateUtils.getCurrentDateTime() TODO MAYBE UPDATE DDM ALSO §
                        //"message":"Bon Commande Existe Deja","code":10701
                        for (i in result.data!!.indices) {
                            proCaisseLocalDb.bonCommande.setSynced(
                                bonCommandeNum = result.data[i].dEVNum,
                                bonCommandeNumM = result.data[i].dEVCodeM
                            )

                            proCaisseLocalDb.ligneBonCommande.setSynced(
                                newNum = result.data[i].dEVNum,
                                oldNum = result.data[i].dEVCodeM
                            )
                        }

                        withContext(mainImmediateDispatcher) {
                            responseAddBonCommandeState = RemoteResponseState(data = result.data, loading = false, error = null)
                        }
                    }

                    is DataResult.Loading -> {
                        withContext(mainImmediateDispatcher) {
                            responseAddBonCommandeState = RemoteResponseState(data = null, loading = true, error = null)
                        }
                    }

                    is DataResult.Error -> {
                        withContext(mainImmediateDispatcher) {
                            responseAddBonCommandeState = RemoteResponseState(data = null, loading = false, error = result.message, message = selectedBonCommande.dEVNum)
                        }
                    }

                    else -> {
                        withContext(mainImmediateDispatcher) {
                            responseAddBonCommandeState = RemoteResponseState(data = null, loading = false, error = "Unknow Error")
                        }
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }
}

