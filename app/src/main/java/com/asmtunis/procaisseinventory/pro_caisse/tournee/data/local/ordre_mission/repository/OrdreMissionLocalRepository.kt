package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ordre_mission.repository

import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.LigneOrdreMissionWithClient
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.OrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.OrdreMissionWithEtat
import kotlinx.coroutines.flow.Flow

interface OrdreMissionLocalRepository {
        fun upsertAll(value: List<OrdreMission>)
        fun upsert(value: OrdreMission)
        fun updateOrdreMissionEtat(oRDCode: String, codeEtat: String)
        fun getBydate(date: String): Flow<Map<OrdreMissionWithEtat, List<LigneOrdreMissionWithClient>>?>
        fun getAll(): Flow<Map<OrdreMissionWithEtat, List<LigneOrdreMissionWithClient>>?>
        fun deleteAll()
    }
