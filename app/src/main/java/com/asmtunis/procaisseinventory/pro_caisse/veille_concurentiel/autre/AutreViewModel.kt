package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.filter.AutreFilterListState
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation.ValidationAddNewVcEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class AutreViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb
) : ViewModel() {

    fun saveAutret(autreVC: AutreVC) {
        viewModelScope.launch(dispatcher) {
           proCaisseLocalDb.autreVC.upsert(autreVC)
        }
    }

    var selectedAutre: AutreVCWithImages by mutableStateOf(AutreVCWithImages())
        private set
    fun onselectedAutreChange(value: AutreVCWithImages) {
        selectedAutre = value
    }


    var modify: Boolean by mutableStateOf(false)
        private set
    fun onModifyChange(value: Boolean) {
        modify = value
    }


    var autreSearchTextState: TextFieldValue by mutableStateOf(TextFieldValue(""))
        private set
    fun onAutreSearchValueChange(value: TextFieldValue) {
        autreSearchTextState = value
    }


    var autreListstate: AutreFilterListState by mutableStateOf(
        AutreFilterListState()
    )
        private set
    fun onEventAutreVC(event: ListEvent) {
        when (event) {
            is ListEvent.Order -> {
                if (autreListstate.listOrder::class == event.listOrder::class &&
                    autreListstate.listOrder.orderType == event.listOrder.orderType
                ) {
                    return
                }
                autreListstate = autreListstate.copy(
                    listOrder = event.listOrder
                )
                filterAutreVC(autreListstate)
            }
            is ListEvent.Delete -> TODO()
            is ListEvent.Restore -> TODO()



            is ListEvent.ListSearch -> {
                autreListstate = autreListstate.copy(
                    search = event.listSearch
                )

                filterAutreVC(autreListstate)
            }

            is ListEvent.FirstCustomFilter -> {
                autreListstate = autreListstate.copy(
                    filterByConcurent = event.firstFilter
                )

                filterAutreVC(autreListstate)
            }

            is ListEvent.SecondCustomFilter -> {
                autreListstate = autreListstate.copy(
                    filterByTypeCommunication = event.secondFiter
                )

                filterAutreVC(autreListstate)
            }
            is ListEvent.ThirdCustomFilter -> TODO()
        }

    }
    var getAutreJob: Job = Job()
    fun filterAutreVC(autreListState: AutreFilterListState) {
        val searchedText = autreSearchTextState.text
        val searchValue = autreListState.search
        val filterByConcurent = autreListState.filterByConcurent
        val filterByTypeComm = autreListState.filterByTypeCommunication

        getAutreJob.cancel()

        if (searchedText.isEmpty()) {
            getAutreJob = when (autreListState.listOrder.orderType) {
                is OrderType.Ascending -> {
                    when (autreListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proCaisseLocalDb.autreVC.getAllFiltred(
                                isAsc = 1,
                                sortBy = "CodeVCLanP",
                                filterByConcurrent = filterByConcurent,
                                filterByTypComm = filterByTypeComm
                            ).collect {
                                setAutreList(listAutreVC = it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proCaisseLocalDb.autreVC.getAllFiltred(
                                isAsc = 1,
                                sortBy = "dateOp",
                                filterByConcurrent = filterByConcurent,
                                filterByTypComm = filterByTypeComm).collect {
                                setAutreList(listAutreVC = it)
                            }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proCaisseLocalDb.autreVC.getAllFiltred(
                                isAsc = 1,
                                sortBy = "ProduitLanP",
                                filterByConcurrent = filterByConcurent,
                                filterByTypComm = filterByTypeComm
                            ).collect {
                                setAutreList(listAutreVC = it)
                            }
                        }
                    }
                }

                is OrderType.Descending -> {
                    when (autreListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proCaisseLocalDb.autreVC.getAllFiltred(
                                isAsc = 2,
                                sortBy = "CodeVCLanP",
                                filterByConcurrent = filterByConcurent,
                                filterByTypComm = filterByTypeComm).collect {
                                setAutreList(listAutreVC = it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proCaisseLocalDb.autreVC.getAllFiltred(
                                isAsc = 2,
                                sortBy = "dateOp",
                                filterByConcurrent = filterByConcurent,
                                filterByTypComm = filterByTypeComm
                            ).collect {
                                setAutreList(listAutreVC = it)
                            }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proCaisseLocalDb.autreVC.getAllFiltred(
                                isAsc = 2,
                                sortBy = "ProduitLanP",
                                filterByConcurrent = filterByConcurent,
                                filterByTypComm = filterByTypeComm
                            ).collect {
                                setAutreList(listAutreVC = it)
                            }
                        }
                    }
                }
            }
        } else {
            if (searchedText.isNotEmpty()) {
                if (searchValue is ListSearch.FirstSearch) {
                    getAutreJob = when (autreListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (autreListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "CodeVCLanP",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "dateOp",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "ProduitLanP",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (autreListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "CodeVCLanP",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "dateOp",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "ProduitLanP",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (searchValue is ListSearch.SecondSearch) {
                    getAutreJob =  when (autreListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (autreListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByAutre(
                                        searchString = searchedText,
                                        sortBy = "CodeVCLanP",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByAutre(
                                        searchString = searchedText,
                                        sortBy = "dateOp",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByAutre(
                                        searchString = searchedText,
                                        sortBy = "ProduitLanP",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (autreListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByAutre(
                                        searchString = searchedText,
                                        sortBy = "CodeVCLanP",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByAutre(
                                        searchString = searchedText,
                                        sortBy = "dateOp",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByAutre(
                                        searchString = searchedText,
                                        sortBy = "ProduitLanP",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (searchValue is ListSearch.ThirdSearch) {
                    getAutreJob =  when (autreListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (autreListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByAutreNote(
                                        searchString = searchedText,
                                        sortBy = "CodeVCLanP",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByAutreNote(
                                        searchString = searchedText,
                                        sortBy = "dateOp",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByAutreNote(
                                        searchString = searchedText,
                                        sortBy = "ProduitLanP",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (autreListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByAutreNote(
                                        searchString = searchedText,
                                        sortBy = "CodeVCLanP",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByAutreNote(
                                        searchString = searchedText,
                                        sortBy = "dateOp",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.autreVC.filterByAutreNote(
                                        searchString = searchedText,
                                        sortBy = "ProduitLanP",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setAutreList(listAutreVC = it)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }




    private fun setAutreList(listAutreVC:  List<AutreVCWithImages>){

        autreListstate = autreListstate.copy(
            lists =  emptyList() // ,
            // listOrder = articlesListState.listOrder
        )

        autreListstate = autreListstate.copy(
            lists = listAutreVC // ,
            // listOrder = articlesListState.listOrder
        )

     //   getAutreJob.cancel()
    }

    fun setDeletedAutre(autreVC: AutreVC) {
        viewModelScope.launch(dispatcher) {
            if(!autreVC.isSync && autreVC.status == ItemStatus.INSERTED.status) {
                proCaisseLocalDb.autreVC.deleteByCode(autreVC.codeAutre)
                proCaisseLocalDb.imageVC.deleteByCodeTypeVc(autreVC.codeMob)
            }

            else

            proCaisseLocalDb.autreVC.setDeleted(
                code = autreVC.codeAutre,
                codeMobile = autreVC.codeMob
            )
            filterAutreVC(autreListstate)
        }



    }

    fun restDeletedAutre(autreVCWithImages: AutreVCWithImages) {
        val autreVC = autreVCWithImages.autreVC?: AutreVC()
        viewModelScope.launch(dispatcher) {
            val status :String
            val isSync :Boolean
            if(autreVC.codeMob == autreVC.codeAutre){
                status = ItemStatus.DELETED.status
                isSync = false
            }
            else {
                status = ItemStatus.SELECTED.status
                isSync = true

            }

            proCaisseLocalDb.autreVC.restDeleted(
                code = autreVC.codeAutre,
                status =status,
                isSync =isSync
            )
            filterAutreVC(autreListstate)
        }
    }


    private fun saveAutre(
        userID: String,
        codeM: String,
        event: ValidationAddNewVcEvent.AddNewVc
    ) {
        val selectedVc = selectedAutre.autreVC?: AutreVC()
        val autreVC =
            AutreVC(
                id = if(selectedVc.id!=0L) selectedVc.id else 0,
                codeAutre = if (selectedVc.codeAutre != "")
                    selectedVc.codeAutre
                else codeM,
                codeMob = codeM,
                autre = event.addNewtVc.produit,
                dateOp = getCurrentDateTime(),
                codeConcur = event.addNewtVc.concurrent.codeconcurrent,
                noteOp = event.addNewtVc.note,
                codeUser = userID.toInt(),
                infoOp1 = "",
                autreNote = event.addNewtVc.autreNote,
                codeTypeCom = event.addNewtVc.typeCommunication.codeTypeCom
            )
        autreVC.status = ItemStatus.INSERTED.status
        autreVC.isSync = false

        saveAutret(autreVC = autreVC)
    }



    fun handleAddVisiteEvents(
        validationAddVcEvents: ValidationAddNewVcEvent,
        popBackStack: () -> Unit,
        utilisateur: Utilisateur,
        codeM: String,
        saveImageList: () -> Unit
    ) {
                when (validationAddVcEvents) {
                    is ValidationAddNewVcEvent.AddNewVc -> {
                        val userID = utilisateur.codeUt


                        saveImageList()

                        saveAutre(
                            userID = userID,
                            event = validationAddVcEvents,
                            codeM = codeM
                        )


                        onModifyChange(false)
                        popBackStack()
                    }
                }

    }
}