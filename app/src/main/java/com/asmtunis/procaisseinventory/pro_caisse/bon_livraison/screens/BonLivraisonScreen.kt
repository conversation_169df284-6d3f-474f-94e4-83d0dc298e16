@file:OptIn(ExperimentalMaterial3AdaptiveApi::class)

package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.screens

import NavDrawer
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.adaptive.ExperimentalMaterial3AdaptiveApi
import androidx.compose.material3.adaptive.layout.AnimatedPane
import androidx.compose.material3.adaptive.layout.ListDetailPaneScaffoldRole
import androidx.compose.material3.adaptive.navigation.NavigableListDetailPaneScaffold
import androidx.compose.material3.adaptive.navigation.rememberListDetailPaneScaffoldNavigator
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AddBonLivraisonRoute
import com.asmtunis.procaisseinventory.core.navigation.BluetoothConnectRoute
import com.asmtunis.procaisseinventory.core.navigation.BonLivraisonDetailRoute
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.wifi.WifiPrintViewModel
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.BonLivraisonViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import kotlinx.coroutines.launch


@Composable
fun BonLivraisonScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    clientId: String,
    navDrawerViewmodel: NavigationDrawerViewModel,
    bonLivraisonVM: BonLivraisonViewModel,
    mainViewModel : MainViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    syncInventoryViewModel: SyncInventoryViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    selectArtMobilityVM : SelectArticleCalculViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    printViewModel: PrintViewModel,
    wifiPrintVM: WifiPrintViewModel,
    settingVM: SettingViewModel,
    bluetoothVM: BluetoothViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,

    ) {
    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)
   // val uiWindowState = settingVM.uiWindowState
    val scope = rememberCoroutineScope()
    val navigator = rememberListDetailPaneScaffoldNavigator<Any>()
    NavDrawer(
        navigate =  { navigate(it) },
        drawer = drawer,
        navDrawerViewmodel = navDrawerViewmodel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        mainViewModel = mainViewModel,

        syncInventoryViewModel = syncInventoryViewModel,
      syncSharedViewModels = syncSharedViewModels,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingVM
        ) {
     /*   BonLivraisonListPane (
            navigate = { navigate(it) },
            drawer = drawer,
            clientId = clientId,
            navDrawerViewmodel = navDrawerViewmodel,
            getProCaisseDataViewModel = getProCaisseDataViewModel,
            selectArtMobilityVM = selectArtMobilityVM,
            bonLivraisonVM = bonLivraisonVM,
            mainViewModel = mainViewModel,
            dataViewModel = dataViewModel,
            networkViewModel = networkViewModel,
            syncBonLivraisonVM = syncBLVM,
            printViewModel = printViewModel,
            bluetoothVM = bluetoothVM,
            wifiPrintVM = wifiPrintVM,
            getProInventoryDataViewModel = getProInventoryDataViewModel,
            getSharedDataViewModel = getSharedDataViewModel,
            settingVM = settingVM,
            syncVcViewModel = syncVcViewModel,
            syncDistNumViewModel = syncDistNumViewModel,
            syncReglementViewModel = syncReglementViewModel,
            syncBonRetourViewModel = syncBonRetourViewModel,
            syncInvPatrimoineViewModel = syncInvPatrimoineViewModel,
            syncBonCommandeViewModel = syncBonCommandeViewModel,
            syncBLVM = syncBLVM,
            syncClientViewModel = syncClientViewModel,
            syncInventoryViewModel = syncInventoryViewModel,
            syncArticlesViewModel = syncArticlesViewModel,
            syncTourneeViewModel = syncTourneeViewModel,
            syncInvBatimentViewModel = syncInvBatimentViewModel
        )*/

        NavigableListDetailPaneScaffold(
          //  modifier = Modifier.fillMaxSize(),
            navigator = navigator,
            listPane = {
                BonLivraisonListPane (
                    navigate = { navigate(it) },
                    navigator = {

                        when (it) {
                            is AddBonLivraisonRoute -> {
                                navigate(AddBonLivraisonRoute(it.clientId))
                            }
                            is BonLivraisonDetailRoute -> {
                                scope.launch {
                                    navigator.navigateTo(
                                        pane = ListDetailPaneScaffoldRole.Detail
                                    )
                                }

                                /* navigator.navigateTo(
                                     pane = ListDetailPaneScaffoldRole.Detail,
                                     content = it.clientId
                                 )*/
                            }
                            is BluetoothConnectRoute-> {
                                navigate(BluetoothConnectRoute)
                            }
                            else -> {
                                // Handle other cases or raise an error if needed
                                println("Unknown argument type")
                            }
                        }
                    },
                    drawer = drawer,
                    clientId = clientId,
                    navDrawerViewmodel = navDrawerViewmodel,
                    getProCaisseDataViewModel = getProCaisseDataViewModel,
                    selectArtMobilityVM = selectArtMobilityVM,
                    bonLivraisonVM = bonLivraisonVM,
                    mainViewModel = mainViewModel,
                    dataViewModel = dataViewModel,
                    networkViewModel = networkViewModel,
                    printViewModel = printViewModel,
                    bluetoothVM = bluetoothVM,
                    wifiPrintVM = wifiPrintVM,
                    settingViewModel = settingVM,
                    syncProcaisseViewModels = syncProcaisseViewModels
                )

            },
            detailPane = {

                //     val content = navigator.currentDestination?.content as Client?


             /*   val content = navigator.currentDestination?.content?.toString()?: mainViewModel.clientList.firstOrNull()?.cLICode?: ""

                if(uiWindowState.navigationType != ReplyNavigationType.PERMANENT_NAVIGATION_DRAWER) {
                    clientViewModel.onSelectedClientChange(Client())
                } else
                    if(navigator.currentDestination?.content?.toString() == null) {
                        clientViewModel.onSelectedClientChange(mainViewModel.clientList.firstOrNull()?: Client())
                    }
*/
              /*  if(uiWindowState.navigationType != ReplyNavigationType.PERMANENT_NAVIGATION_DRAWER) {
                    bonLivraisonVM.restBonLivraison()
                }*/
                AnimatedPane {
                    if(bonLivraisonVM.ticketWithFactureAndPayments.isEmpty())
                        LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
                    else
                    BonLivraisonDetailPane(
                        navigate = { navigate(it) },
                        popBackStack = {
                            //popBackStack()
                            scope.launch {
                                navigator.navigateBack()
                            }
                                       },
                        navigationDrawerViewModel = navDrawerViewmodel,
                        bonLivraisonViewModel = bonLivraisonVM,
                        mainViewModel = mainViewModel,
                        selectArtMobilityVM = selectArtMobilityVM,
                        dataViewModel = dataViewModel,
                        networkViewModel = networkViewModel,
                        printViewModel = printViewModel,
                        wifiPrintVM = wifiPrintVM,
                        bluetoothVM = bluetoothVM,
                        settingViewModel = settingVM
                    )
                }
            },
            /*  extraPane = {

                }*/
        )
    }
}


