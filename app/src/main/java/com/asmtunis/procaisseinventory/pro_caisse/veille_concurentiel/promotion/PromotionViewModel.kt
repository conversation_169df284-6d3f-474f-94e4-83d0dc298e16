package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation.ValidationAddNewVcEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class PromotionViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb
) : ViewModel() {





    fun saveNewPromo(promoVC: PromoVC) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.promoVC.upsert(promoVC)
        }
    }
    var selectedPromotionWithImages: PromoVCWithImages by mutableStateOf(PromoVCWithImages())
        private set
    fun onselectedPromotionChange(value: PromoVCWithImages) {
        selectedPromotionWithImages = value
    }

    var modify: Boolean by mutableStateOf(false)
        private set
    fun onModifyChange(value: Boolean) {
        modify = value
    }
    var promotionSearchTextState: TextFieldValue by mutableStateOf(TextFieldValue(""))
        private set
    fun onPromotionSearchValueChange(value: TextFieldValue) {
        promotionSearchTextState = value
    }


    var promotionVCListstate: PromotionFilterListState by mutableStateOf(PromotionFilterListState())
        private set
    fun onEventPromoVC(event: ListEvent) {
        when (event) {
            is ListEvent.Order -> {
                if (promotionVCListstate.listOrder::class == event.listOrder::class &&
                    promotionVCListstate.listOrder.orderType == event.listOrder.orderType
                ) {
                    return
                }
                promotionVCListstate = promotionVCListstate.copy(
                    listOrder = event.listOrder
                )
                filterPromoVC(promotionVCListstate)
            }
            is ListEvent.Delete -> TODO()
            is ListEvent.Restore -> TODO()



            is ListEvent.ListSearch -> {
                promotionVCListstate = promotionVCListstate.copy(
                    search = event.listSearch
                )

                filterPromoVC(promotionVCListstate)
            }

            is ListEvent.FirstCustomFilter -> TODO()

            is ListEvent.SecondCustomFilter -> {
                promotionVCListstate = promotionVCListstate.copy(
                    filterByTypeCommunication = event.secondFiter
                )

                filterPromoVC(promotionVCListstate)
            }
            is ListEvent.ThirdCustomFilter -> TODO()
        }

    }
    var getPromoJob: Job = Job()
    fun filterPromoVC(promotionFilterListState: PromotionFilterListState) {
        val searchedText = promotionSearchTextState.text
        val searchValue = promotionFilterListState.search
        val filterByTypeComm = promotionFilterListState.filterByTypeCommunication

        getPromoJob.cancel()

        if (searchedText.isEmpty()) {
            getPromoJob = when (promotionFilterListState.listOrder.orderType) {
                is OrderType.Ascending -> {
                    when (promotionFilterListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proCaisseLocalDb.promoVC.getAllFiltred(
                                isAsc = 1,
                                sortBy = "CodeVCPromo",
                                filterByTypComm = filterByTypeComm
                            ).collect {
                                setPromotionList(listPromoVC=it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proCaisseLocalDb.promoVC.getAllFiltred(
                                isAsc = 1,
                                sortBy = "DateOp",
                                filterByTypComm = filterByTypeComm).collect {
                                setPromotionList(listPromoVC=it)
                            }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proCaisseLocalDb.promoVC.getAllFiltred(
                                isAsc = 1,
                                sortBy = "PrixConcur",
                                filterByTypComm = filterByTypeComm
                            ).collect {
                                setPromotionList(listPromoVC=it)
                            }
                        }
                    }
                }

                is OrderType.Descending -> {
                    when (promotionFilterListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proCaisseLocalDb.promoVC.getAllFiltred(
                                isAsc = 2,
                                sortBy = "CodeVCPromo",
                                filterByTypComm = filterByTypeComm).collect {
                                setPromotionList(listPromoVC=it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proCaisseLocalDb.promoVC.getAllFiltred(
                                isAsc = 2,
                                sortBy = "DateOp",
                                filterByTypComm = filterByTypeComm
                            ).collect {
                                setPromotionList(listPromoVC=it)
                            }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proCaisseLocalDb.promoVC.getAllFiltred(
                                isAsc = 2,
                                sortBy = "PrixConcur",
                                filterByTypComm = filterByTypeComm
                            ).collect {
                                setPromotionList(listPromoVC=it)
                            }
                        }
                    }
                }
            }
        } else {
            if (searchedText.isNotEmpty()) {
                if (searchValue is ListSearch.FirstSearch) {
                    getPromoJob =  when (promotionFilterListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (promotionFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "CodeVCPromo",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "DateOp",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "PrixConcur",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (promotionFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "CodeVCPromo",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "DateOp",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "PrixConcur",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (searchValue is ListSearch.SecondSearch) {
                    getPromoJob = when (promotionFilterListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (promotionFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByCodeArtLocal(
                                        searchString = searchedText,
                                        sortBy = "CodeVCPromo",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByCodeArtLocal(
                                        searchString = searchedText,
                                        sortBy = "DateOp",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByCodeArtLocal(
                                        searchString = searchedText,
                                        sortBy = "PrixConcur",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (promotionFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByCodeArtLocal(
                                        searchString = searchedText,
                                        sortBy = "CodeVCPromo",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByCodeArtLocal(
                                        searchString = searchedText,
                                        sortBy = "DateOp",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByCodeArtLocal(
                                        searchString = searchedText,
                                        sortBy = "PrixConcur",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (searchValue is ListSearch.ThirdSearch) {
                    getPromoJob = when (promotionFilterListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (promotionFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByArtConcurrent(
                                        searchString = searchedText,
                                        sortBy = "CodeVCPromo",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByArtConcurrent(
                                        searchString = searchedText,
                                        sortBy = "DateOp",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByArtConcurrent(
                                        searchString = searchedText,
                                        sortBy = "PrixConcur",
                                        isAsc = 1,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (promotionFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByArtConcurrent(
                                        searchString = searchedText,
                                        sortBy = "CodeVCPromo",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByArtConcurrent(
                                        searchString = searchedText,
                                        sortBy = "DateOp",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.promoVC.filterByArtConcurrent(
                                        searchString = searchedText,
                                        sortBy = "PrixConcur",
                                        isAsc = 2,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setPromotionList(listPromoVC=it)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }




    private fun setPromotionList(listPromoVC:  List<PromoVCWithImages>){
        promotionVCListstate = promotionVCListstate.copy(lists = emptyList())
        promotionVCListstate = promotionVCListstate.copy(lists = listPromoVC)


      //  getPromoJob.cancel()
    }

    fun setDeletedPromotion(promoVC: PromoVC) {
        viewModelScope.launch(dispatcher) {
            if(!promoVC.isSync && promoVC.status == ItemStatus.INSERTED.status) {
                proCaisseLocalDb.promoVC.deleteByCode(promoVC.codeVCPromo)
                proCaisseLocalDb.imageVC.deleteByCodeTypeVc(promoVC.codeVCPromoM)
            }

            else

            proCaisseLocalDb.promoVC.setDeleted(
                code = promoVC.codeVCPromo,
                codeMobile = promoVC.codeVCPromoM
            )
            filterPromoVC(promotionVCListstate)
        }



    }

    fun restDeletedPromotion(promoVC: PromoVCWithImages) {
        viewModelScope.launch(dispatcher) {

            val status :String
            val isSync :Boolean
            if(promoVC.promoVC!!.codeVCPromo == promoVC.promoVC!!.codeVCPromoM){
                status = ItemStatus.DELETED.status
                isSync = false
            }
            else {
                status = ItemStatus.SELECTED.status
                isSync = true

            }

            proCaisseLocalDb.promoVC.restDeleted(
                code = promoVC.promoVC!!.codeVCPromo,
                status =status,
                isSync =isSync
            )
            filterPromoVC(promotionVCListstate)
        }
    }









    fun handleAddVisiteEvents(
        codeM: String,
        validationAddVcEvents: ValidationAddNewVcEvent,
        popBackStack: () -> Unit,
        saveImageList: () -> Unit,
        utilisateur: Utilisateur,

        ) {
         
                when (validationAddVcEvents) {
                    is ValidationAddNewVcEvent.AddNewVc -> {
                        val typeUtilisateur = utilisateur.typeUser
                        val userID = utilisateur.codeUt



                        saveImageList()

                        saveNewProduct(
                            codeM = codeM,
                            userID = userID,
                            event = validationAddVcEvents
                        )


                        onModifyChange(false)
                        // navController.navigate(Screen.Clients.Route)
                        popBackStack()
                    }
                }
          
    }


    private fun saveNewProduct(
        codeM: String,
        userID: String,
        event: ValidationAddNewVcEvent.AddNewVc
    ) {
        val promotionVc = selectedPromotionWithImages.promoVC?: PromoVC()
        val promoVC =
            PromoVC(
                id = if(promotionVc.id!=0L) promotionVc.id else 0,

                codeVCPromo = if (promotionVc.codeVCPromo != "") promotionVc.codeVCPromo
                else codeM,
                codeVCPromoM = codeM,
                articleConcur = event.addNewtVc.produit,
                dateOp = getCurrentDateTime(),
                codeConcur = event.addNewtVc.concurrent.codeconcurrent,
                codeArtLocal =event.addNewtVc.localProduct.aRTCode ,
                noteOp = event.addNewtVc.note,
                prixConcur = StringUtils.stringToDouble(event.addNewtVc.prix),
                tauxPromo = StringUtils.stringToDouble(event.addNewtVc.taux),
                codeUser = userID.toInt(),
                infoOp1 = "",
                codeTypeCom = event.addNewtVc.typeCommunication.codeTypeCom
            )



        promoVC.status = ItemStatus.INSERTED.status
        promoVC.isSync = false

        saveNewPromo(promoVC = promoVC)
    }
}