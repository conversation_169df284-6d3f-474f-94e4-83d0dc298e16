package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJointAddResponse
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.TypeVc
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.DeleteDataResponseVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ResponseBatchDataVc
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine.PrixVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVC
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject


@HiltViewModel
class SyncVcViewModel @Inject constructor(
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val listenNetwork: ListenNetwork,
    // app: Application
) : ViewModel() {//: AndroidViewModel(app) {
private var autoSyncState  by mutableStateOf(false)
    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(key = PROCAISSE_AUTO_SYNC_AUTHORISATION, default = true).distinctUntilChanged()
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()

    private var  connected  by mutableStateOf(false)
    init {
        getNotSyncImages()
        getNotSyncNewProductVc()

        getNotSyncPromoVc()
        getNotSyncPrixVc()
        getNotSyncAutreVc()


        getNotSyncNewProductDeleted()
        getNotSyncPrixDeleted()
        getNotSyncPromoDeleted()
        getNotSyncAutreDeleted()
    }

    var imageState: RemoteResponseState<List<ImagePieceJointAddResponse>> by mutableStateOf(RemoteResponseState())
        private set



    var imagesNotSync : List<ImagePieceJoint> by mutableStateOf(emptyList())
        private set
    var notSyncImageObj : String by mutableStateOf("")
        private set
    private fun getNotSyncImages() {
        viewModelScope.launch {
            val imagesNotSyncFlow =  proCaisseLocalDb.imageVC.getNotSynced().distinctUntilChanged()


            combine(networkFlow, imagesNotSyncFlow, autoSyncFlow) { isConnected, imagesNotSyncFlowList, autoSync ->
                Triple(
                    isConnected,
                    autoSync,
                    imagesNotSyncFlowList.ifEmpty { emptyList() }
                )
            }.collect { (isConnected, autoSync, images) ->
                connected = isConnected
                autoSyncState = autoSync

                if (images.isEmpty()) {
                    imagesNotSync = emptyList()
                    return@collect
                }
                imagesNotSync = images.filter {
                    it.typeVC == TypeVc.VCPRIX.typeVC ||
                            it.typeVC == TypeVc.VCLANCEMENTNP.typeVC ||
                            it.typeVC == TypeVc.VCPROMO.typeVC ||
                            it.typeVC == TypeVc.VCAUTRE.typeVC
                }.map { it.copy(imgUrl = "") }
                if(connected && autoSyncState) syncImages(imagesNotSync)
            }
        }
    }

    fun syncImages(notSyncImages: List<ImagePieceJoint>) {
        viewModelScope.launch(dispatcherIO) {
            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()?.let { Json.decodeFromString(it) }?: BaseConfig(),
                Json.encodeToJsonElement(notSyncImages)
            )
            notSyncImageObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.veilleConcurentiel.addBatchVCImage(notSyncImageObj).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                           for (image in result.data!!) {
                               if(image.code == 10200) {
                                   proCaisseLocalDb.imageVC.setImagesSynced(image.codeIMG)
                               }
                           }

                        if (!result.data.any { it.code != 10200 })
                        imageState = RemoteResponseState(data = result.data, loading = false, error = null)
                            }

                    is DataResult.Loading -> {
                        imageState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        imageState = RemoteResponseState(data = null, loading = false, error = result.message)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }
    /******************************************************
     * **************************** NEW PRODUCT ********************
     * ***************************************************
     */


    var newProductState: RemoteResponseState<List<ResponseBatchDataVc>> by mutableStateOf(RemoteResponseState())
        private set

    var newProductVcNotSync : List<NewProductVC>  by mutableStateOf(emptyList())
        private set
    var notSyncNewProductVcObj : String by mutableStateOf("")
        private set
    private fun getNotSyncNewProductVc() {
        viewModelScope.launch {

            val newProductNotSyncFlow =  proCaisseLocalDb.newProductVC.noSyncedToAddOrUpdate().distinctUntilChanged()


            combine(networkFlow, newProductNotSyncFlow, autoSyncFlow) { isConnected, newProductNotSyncFlowList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                newProductNotSyncFlowList?.ifEmpty { emptyList() }?: emptyList()
            }.collect {

                if (it.isEmpty()) {
                    newProductVcNotSync = emptyList()
                    return@collect
                }
                newProductVcNotSync = it
                if(connected && autoSyncState) syncNewProductsVc(it)
            }
        }
    }


    fun syncNewProductsVc(notSyncNewProduct: List<NewProductVC>) {
        viewModelScope.launch(dispatcherIO) {
            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()?.let { Json.decodeFromString(it) }?: BaseConfig(),
                Json.encodeToJsonElement(notSyncNewProduct)
            )
            notSyncNewProductVcObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.veilleConcurentiel.addBatchDataVConcu(notSyncNewProductVcObj, table = TypeVc.VCLANCEMENTNP.typeVC.filterNot {it.isWhitespace()}).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        //  viewModelScope.launch(dispatcherIO) {

                        for (newProduct in result.data!!) {
                            if(newProduct.table == TypeVc.VCLANCEMENTNP.typeVC.filterNot {it.isWhitespace()})
                                proCaisseLocalDb.newProductVC.updateCloudCode(code = newProduct.code, codeMobile = newProduct.codeM)
                        }



                        newProductState = RemoteResponseState(data = result.data, loading = false, error = null)
                    }

                    is DataResult.Loading -> {
                        newProductState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        newProductState = RemoteResponseState(data = null, loading = false, error = result.message, message = notSyncNewProduct.first().codeVCLanP)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }




    var newProductDeletedState: RemoteResponseState<List<DeleteDataResponseVC>> by mutableStateOf(RemoteResponseState())
        private set

    var newProductDeletedNotSync: List<NewProductVC> by mutableStateOf(emptyList())
        private set
    var notSyncNewProductDeletedVcObj : String by mutableStateOf("")
        private set
    private fun getNotSyncNewProductDeleted() {
        viewModelScope.launch {
            val newProductDeletedNotSyncFlow =  proCaisseLocalDb.newProductVC.getNoSyncedToDelete().distinctUntilChanged()


            combine(networkFlow, newProductDeletedNotSyncFlow, autoSyncFlow) { isConnected, newProductDeletedNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                newProductDeletedNotSyncList.ifEmpty { emptyList() }
            }.collect {

                if (it.isEmpty()) {
                    newProductDeletedNotSync = emptyList()
                    return@collect
                }
                newProductDeletedNotSync = it
                if(connected && autoSyncState) syncNewProductToDelete(it)
            }
        }
    }

    fun syncNewProductToDelete(notSyncNewProductDeleted : List<NewProductVC>) {
        viewModelScope.launch(dispatcherIO) {
            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()?.let { Json.decodeFromString(it) }?: BaseConfig(),
                Json.encodeToJsonElement(notSyncNewProductDeleted)
            )
            val table =  TypeVc.VCLANCEMENTNP.typeVC.filterNot {it.isWhitespace()}

            notSyncNewProductDeletedVcObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.veilleConcurentiel.deleteDataVConcu(notSyncNewProductDeletedVcObj, table = table).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        //  viewModelScope.launch(dispatcherIO) {
                        for (response in result.data!!) {

                            if(response.table == table)
                            proCaisseLocalDb.newProductVC.deleteByCode(response.CodeVCLanP)
                        }




                        newProductDeletedState = RemoteResponseState(data = result.data, loading = false, error = null)
                    }

                    is DataResult.Loading -> {
                        newProductDeletedState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        newProductDeletedState = RemoteResponseState(data = null, loading = false, error = result.message)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }


    /******************************************************
     * **************************** PROMO ********************
     * ***************************************************
     */


    var promoState: RemoteResponseState<List<ResponseBatchDataVc>> by mutableStateOf(RemoteResponseState())
        private set

    var promoVcNotSync : List<PromoVC>  by mutableStateOf(emptyList())
        private set
    var notSyncPromoVcObj : String by mutableStateOf("")
        private set
    private fun getNotSyncPromoVc() {
        viewModelScope.launch(mainDispatcher) {
            val promoVcNotSyncFlow =  proCaisseLocalDb.promoVC.noSyncedToAddOrUpdate().distinctUntilChanged()


            combine(networkFlow, promoVcNotSyncFlow, autoSyncFlow) { isConnected, promoVcNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                promoVcNotSyncList.ifEmpty { emptyList() }
            }.collect {

                if (it.isEmpty()) {
                    promoVcNotSync = emptyList()
                    return@collect
                }
                promoVcNotSync = it
                if(connected && autoSyncState) syncPromoVc(it)
            }

        }
    }


    fun syncPromoVc(notSyncPromo: List<PromoVC>) {
        viewModelScope.launch(dispatcherIO) {
            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()?.let { Json.decodeFromString(it) }?: BaseConfig(),
                Json.encodeToJsonElement(notSyncPromo)
            )
            notSyncPromoVcObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.veilleConcurentiel.addBatchDataVConcu(notSyncPromoVcObj, table = TypeVc.VCPROMO.typeVC.filterNot {it.isWhitespace()}).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        for (newProduct in result.data!!) {
                            if(newProduct.table == TypeVc.VCPROMO.typeVC.filterNot {it.isWhitespace()})
                                proCaisseLocalDb.promoVC.updateCloudCode(code = newProduct.code, codeMobile = newProduct.codeM)
                        }



                        promoState = RemoteResponseState(data = result.data, loading = false, error = null)
                    }

                    is DataResult.Loading -> {
                        promoState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        promoState = RemoteResponseState(data = null, loading = false, error = result.message, message = notSyncPromo.first().codeVCPromo)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }





    var promoDeletedState: RemoteResponseState<List<DeleteDataResponseVC>> by mutableStateOf(RemoteResponseState())
        private set

    var promoDeletedNotSync: List<PromoVC> by mutableStateOf(emptyList())
        private set
    var notSyncPromoDeletedVcObj : String by mutableStateOf("")
        private set
    private fun getNotSyncPromoDeleted() {
        viewModelScope.launch {
            val promoDeletedNotSyncFlow =  proCaisseLocalDb.promoVC.noSyncedToDelete().distinctUntilChanged()


            combine(networkFlow, promoDeletedNotSyncFlow, autoSyncFlow) { isConnected, promoDeletedNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                promoDeletedNotSyncList.ifEmpty { emptyList() }
            }.collect {

                if (it.isEmpty()) {
                    promoDeletedNotSync = emptyList()
                    return@collect
                }
                promoDeletedNotSync = it
                if(connected && autoSyncState) syncPromoToDelete(it)
            }




        }
    }

    fun syncPromoToDelete(notSyncNewProductDeleted : List<PromoVC>) {
        viewModelScope.launch(dispatcherIO) {
            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()?.let { Json.decodeFromString(it) }?: BaseConfig(),
                Json.encodeToJsonElement(notSyncNewProductDeleted)
            )
            val table = TypeVc.VCPROMO.typeVC.filterNot {it.isWhitespace()}

            notSyncPromoDeletedVcObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.veilleConcurentiel.deleteDataVConcu(notSyncPromoDeletedVcObj, table =table ).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        for (response in result.data!!) {

                            if(response.table == table)  proCaisseLocalDb.promoVC.deleteByCode(response.CodeVCPromo)
                        }

                        promoDeletedState = RemoteResponseState(data = result.data, loading = false, error = null)
                    }

                    is DataResult.Loading -> {
                        promoDeletedState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        promoDeletedState = RemoteResponseState(data = null, loading = false, error = result.message)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }

    /**
     * *******************************************************
     * ****************************  PRIX *********************
     * =========================================================
     */




    var prixState: RemoteResponseState<List<ResponseBatchDataVc>> by mutableStateOf(RemoteResponseState())
        private set

    var prixVcNotSync : List<PrixVC>  by mutableStateOf(emptyList())
        private set
    var notSyncPrixVcObj : String by mutableStateOf("")
        private set
    private fun getNotSyncPrixVc() {
        viewModelScope.launch {
            val prixVcNotSyncFlow =  proCaisseLocalDb.prixVC.noSyncedToAddOrUpdate().distinctUntilChanged()


            combine(networkFlow, prixVcNotSyncFlow, autoSyncFlow) { isConnected, prixVcNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                prixVcNotSyncList.ifEmpty { emptyList() }
            }.collect {

                if (it.isEmpty()) {
                    prixVcNotSync = emptyList()
                    return@collect
                }
                prixVcNotSync = it
                if(connected && autoSyncState) syncPrixVc(it)
            }
        }
    }


    fun syncPrixVc(notSyncPrix: List<PrixVC>) {
        viewModelScope.launch(dispatcherIO) {
            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()?.let { Json.decodeFromString(it) }?: BaseConfig(),
                Json.encodeToJsonElement(notSyncPrix)
            )
            val table = TypeVc.VCPRIX.typeVC.filterNot {it.isWhitespace()}

            notSyncPrixVcObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.veilleConcurentiel.addBatchDataVConcu(Json.encodeToString(baseConfigObj), table = table).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        for (prix in result.data!!) {
                            if(prix.table == table)
                                proCaisseLocalDb.prixVC.updateCloudCode(code = prix.code, codeM = prix.codeM)
                        }



                        prixState = RemoteResponseState(data = result.data, loading = false, error = null)
                    }

                    is DataResult.Loading -> {
                        prixState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        prixState = RemoteResponseState(data = null, loading = false, error = result.message, message = notSyncPrix.first().codeVCPrix)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }



    var prixDeletedState: RemoteResponseState<List<DeleteDataResponseVC>> by mutableStateOf(RemoteResponseState())
        private set

    var prixDeletedNotSync: List<PrixVC> by mutableStateOf(emptyList())
        private set
    var notSyncPrixDeletedVcObj : String by mutableStateOf("")
        private set
    private fun getNotSyncPrixDeleted() {
        viewModelScope.launch {
            val prixVcDeletedNotSyncFlow =  proCaisseLocalDb.prixVC.noSyncedToDelete().distinctUntilChanged()


            combine(networkFlow, prixVcDeletedNotSyncFlow, autoSyncFlow) { isConnected, prixVcDeletedNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                prixVcDeletedNotSyncList?.ifEmpty { emptyList() }?: emptyList()
            }.collect {

                if (it.isEmpty()) {
                    prixDeletedNotSync = emptyList()
                    return@collect
                }
                prixDeletedNotSync = it
                if(connected && autoSyncState) syncPrixToDelete(it)
            }


        }
    }

    fun syncPrixToDelete(notSyncNewProductDeleted : List<PrixVC>) {
        viewModelScope.launch(dispatcherIO) {
            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()?.let { Json.decodeFromString(it) }?: BaseConfig(),
                Json.encodeToJsonElement(notSyncNewProductDeleted)
            )
            val table = TypeVc.VCPRIX.typeVC.filterNot {it.isWhitespace()}

            notSyncPrixDeletedVcObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.veilleConcurentiel.deleteDataVConcu(notSyncPrixDeletedVcObj, table =table ).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        for (response in result.data!!) {
                            if(response.table == table)
                                proCaisseLocalDb.prixVC.deleteByCode(response.CodeVCPrix)
                        }

                        prixDeletedState = RemoteResponseState(data = result.data, loading = false, error = null)
                    }

                    is DataResult.Loading -> {
                        prixDeletedState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        prixDeletedState = RemoteResponseState(data = null, loading = false, error = result.message)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }

    /**
     * *******************************************************
     * ****************************  AUTRE *********************
     * =========================================================
     */




    var autreState: RemoteResponseState<List<ResponseBatchDataVc>> by mutableStateOf(RemoteResponseState())
        private set

    var autreVcNotSync : List<AutreVC>  by mutableStateOf(emptyList())
        private set
    var notSyncAutreVcObj : String by mutableStateOf("")
        private set
    private fun getNotSyncAutreVc() {
        viewModelScope.launch {
            val autreVcNotSyncFlow =  proCaisseLocalDb.autreVC.noSyncedToAddOrUpdate().distinctUntilChanged()


            combine(networkFlow, autreVcNotSyncFlow, autoSyncFlow) { isConnected, autreVcNotSyncFList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                autreVcNotSyncFList?.ifEmpty { emptyList() }?: emptyList()
            }.collect {

                if (it.isEmpty()) {
                    autreVcNotSync = emptyList()
                    return@collect
                }
                autreVcNotSync = it
                if(connected && autoSyncState) syncAutreVc(it)
            }

        }
    }


    fun syncAutreVc(notSyncAutre: List<AutreVC>) {
        viewModelScope.launch(dispatcherIO) {
            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()?.let { Json.decodeFromString(it) }?: BaseConfig(),
                Json.encodeToJsonElement(notSyncAutre)
            )

            proCaisseRemote.veilleConcurentiel.addBatchDataVConcu(Json.encodeToString(baseConfigObj), table = TypeVc.VCAUTRE.typeVC.filterNot {it.isWhitespace()}).onEach { result ->
                when (result) {
                    is DataResult.Success -> {

                        autreState = RemoteResponseState(data = result.data, loading = false, error = null)

                        if(result.data!!.isEmpty()) return@onEach
                            for (autre in result.data) {
                                if(autre.table == TypeVc.VCAUTRE.typeVC.filterNot {it.isWhitespace()})
                                    proCaisseLocalDb.autreVC.updateCloudCode(code = autre.code, codeM = autre.codeM)
                            }
                    }

                    is DataResult.Loading -> {
                        autreState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        autreState = RemoteResponseState(data = null, loading = false, error = result.message, message = notSyncAutre.first().codeAutre)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }







    var autreDeletedState: RemoteResponseState<List<DeleteDataResponseVC>> by mutableStateOf(RemoteResponseState())
        private set

    var autreDeletedNotSync: List<AutreVC> by mutableStateOf(emptyList())
        private set
    var notSyncAutreDeletedVcObj : String by mutableStateOf("")
        private set
    private fun getNotSyncAutreDeleted() {
        viewModelScope.launch {
            val autreVcDeletedNotSyncFlow =  proCaisseLocalDb.autreVC.noSyncedToDelete().distinctUntilChanged()


            combine(networkFlow, autreVcDeletedNotSyncFlow, autoSyncFlow) { isConnected, autreVcDeletedNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                autreVcDeletedNotSyncList?.ifEmpty { emptyList() }?: emptyList()
            }.collect {

                if (it.isEmpty()) {
                    autreDeletedNotSync = emptyList()
                    return@collect
                }
                autreDeletedNotSync = it
                if(connected && autoSyncState) syncAutreToDelete(autreDeletedNotSync)
            }

        }
    }


    fun syncAutreToDelete(notSyncNewProductDeleted : List<AutreVC>) {
        viewModelScope.launch(dispatcherIO) {
            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()?.let { Json.decodeFromString(it) }?: BaseConfig(),
                Json.encodeToJsonElement(notSyncNewProductDeleted)
            )
            val table = TypeVc.VCAUTRE.typeVC.filterNot {it.isWhitespace()}
            notSyncAutreDeletedVcObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.veilleConcurentiel.deleteDataVConcu(notSyncAutreDeletedVcObj, table =table ).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        for (response in result.data!!) {
                            if(response.table == table)
                                proCaisseLocalDb.autreVC.deleteByCode(response.CodeAutre)
                        }

                        autreDeletedState = RemoteResponseState(data = result.data, loading = false, error = null)
                    }

                    is DataResult.Loading -> {
                        autreDeletedState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        autreDeletedState = RemoteResponseState(data = null, loading = false, error = result.message)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }
}
