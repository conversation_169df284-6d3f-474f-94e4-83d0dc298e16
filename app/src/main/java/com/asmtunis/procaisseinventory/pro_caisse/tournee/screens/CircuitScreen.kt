package com.asmtunis.procaisseinventory.pro_caisse.tournee.screens

import NavDrawer
import android.Manifest
import android.app.Activity
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Info
import androidx.compose.material.icons.outlined.PersonPin
import androidx.compose.material.icons.rounded.DateRange
import androidx.compose.material.icons.twotone.Check
import androidx.compose.material.icons.twotone.GpsOff
import androidx.compose.material.icons.twotone.Layers
import androidx.compose.material.icons.twotone.Map
import androidx.compose.material.icons.twotone.Menu
import androidx.compose.material.icons.twotone.Reorder
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.rememberDrawerState
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.connectivity.location.CheckLocationSetting
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationUtils
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationViewModule
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AddBonCommandeRoute
import com.asmtunis.procaisseinventory.core.navigation.AddBonLivraisonRoute
import com.asmtunis.procaisseinventory.core.navigation.AddBonRetourRoute
import com.asmtunis.procaisseinventory.core.navigation.ClientsInfoRoute
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.OrdreMission
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AskPermission
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.RowClientList
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.dokar.sonner.TextToastAction
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.android.gms.maps.model.CameraPosition
import com.google.android.gms.maps.model.LatLng
import com.google.maps.android.compose.GoogleMap
import com.google.maps.android.compose.MapProperties
import com.google.maps.android.compose.MapUiSettings
import com.google.maps.android.compose.rememberCameraPositionState
import com.simapps.ui_kit.date_time_picker.DatePickerView
import com.simapps.ui_kit.dialogues.CustomAlertDialogue
import com.simapps.ui_kit.edit_text.EditTextField
import com.simapps.ui_kit.utils.getCurrentDate
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Locale
import kotlin.math.abs
import kotlin.time.Duration


@OptIn(ExperimentalMaterial3Api::class, ExperimentalPermissionsApi::class)
@Composable
fun CircuitScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    navDrawerVM: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,
    dataViewModel: DataViewModel,
    settingViewModel: SettingViewModel,
    mainViewModel: MainViewModel,
    locationViewModule: LocationViewModule,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,
    proCaisseViewModels: ProCaisseViewModels
) {
    val tourneeViewModel = proCaisseViewModels.tourneeViewModel
    val bonLivraisonVM = proCaisseViewModels.bonLivraisonViewModel
    val paymentViewModel = proCaisseViewModels.paymentViewModel

    val syncTourneeVM = syncProcaisseViewModels.syncTourneeViewModel

    val uiWindowState = settingViewModel.uiWindowState
    val showEtatOrdreMission = tourneeViewModel.showEtatOrdreMission

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    val selectedDate = tourneeViewModel.selectedDate

    val ordreMissionWithLinesState = getProCaisseDataViewModel.ordreMissionWithLinesState
    val ordreMissionWithLinesFromOnline = getProCaisseDataViewModel.ordreMissionWithLinesState.data

    val isConnected = networkViewModel.isConnected

    val showAlertDialog = mainViewModel.showAlertDialog

    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)
    val showMapType = tourneeViewModel.showMapType
    val mapTypeList = tourneeViewModel.mapTypeList
    val mapType = tourneeViewModel.mapType
    val context = LocalContext.current
    val utilisateur = mainViewModel.utilisateur
    val prefixList = mainViewModel.prefixList
    val scope = rememberCoroutineScope()
    val listState = rememberLazyListState()
    val clientByCode = mainViewModel.clientByCode
    val ordreMissionWithLines = tourneeViewModel.ordreMissionWithLines
    val ordreMissionWithEtat = tourneeViewModel.ordreMissionWithEtat

    val ligneOrdreMission = tourneeViewModel.ligneOrdreMission
    val etatOrdreMission = tourneeViewModel.etatOrdreMission
    val selectedLgOrdMissionWithCoordClient =
        tourneeViewModel.selectedOrdMissionWithLigneAndCltValidCoordinates
    val selectedLgOrdMissionWithNoCoordClient = tourneeViewModel.selectedOrdMissionWithLigneAndCltNoValidCoordinates

    val longClickedlgOrdMissionWithClt = tourneeViewModel.selectedlgOrdMissionWithClt

    val selectedEtat = tourneeViewModel.selectedEtat
    val location = locationViewModule.locationState

    val clientSearchText = tourneeViewModel.clientSearchText
    val selectedOrdreMissionWithEtat = tourneeViewModel.selectedOrdreMissionWithEtat
    val batchUpdateLigneOrdreMissionState = syncTourneeVM.batchUpdateLigneOrdreMissionState

    val showAllCltOnCarte = tourneeViewModel.showAllCltOnCarte
    val showListOrdreMission = tourneeViewModel.showListOrdreMission

    val latitude = location.latitude
    val longitude = location.longitude
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val selectedOrdMission = selectedOrdreMissionWithEtat.ordreMission?: OrdreMission()

    val cltListWithoutCoordonatFiltered = mainViewModel.cltListWithoutCoordonate.filter {
        it.cLINomPren.lowercase(Locale.getDefault()).contains(clientSearchText.lowercase(Locale.getDefault()))
        || it.cLICode.lowercase(Locale.getDefault()).contains(clientSearchText.lowercase(Locale.getDefault()))
    }

    val filteredSelectedLgOrdMissionWithNoCoordClient =
        selectedLgOrdMissionWithNoCoordClient.filter {
            it.client?.cLINomPren?.lowercase(Locale.getDefault())?.contains(clientSearchText.lowercase(Locale.getDefault())) == true
          || it.client?.cLICode?.lowercase(Locale.getDefault())?.contains(clientSearchText.lowercase(Locale.getDefault())) == true
        }

    val nbrSansAdresse = if (showAllCltOnCarte) cltListWithoutCoordonatFiltered.size
    else filteredSelectedLgOrdMissionWithNoCoordClient.size

    val gpsEnabelingResultRequest = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartIntentSenderForResult()
    ) { activityResult ->
        if (activityResult.resultCode == Activity.RESULT_OK) { Log.d("olmssqqq", "Enable gps Accepted") }
        else { Log.d("olmssqqq", "Denied") }
    }

    val cameraPositionState = rememberCameraPositionState {
        position = CameraPosition.fromLatLngZoom(
            LatLng(
                latitude ?: if (selectedLgOrdMissionWithCoordClient.isNotEmpty()) selectedLgOrdMissionWithCoordClient.first().ligneOrdreMission.lIGOR_Latitude else 34.739330501022096,
                longitude ?: if (selectedLgOrdMissionWithCoordClient.isNotEmpty()) selectedLgOrdMissionWithCoordClient.first().ligneOrdreMission.lIGOR_Longitude else 10.75393363814977
            ), 15f
        )
    }
    var uiSettings by remember { mutableStateOf(MapUiSettings()) }
    var properties by remember {
        mutableStateOf(
            MapProperties(
                isBuildingEnabled = true,
                isIndoorEnabled = true,
                isMyLocationEnabled = true,
                isTrafficEnabled = true,
                mapType = mapType
            )
        )
    }

    val cltListWithCoordonate = mainViewModel.cltListWithCoordinates

    LaunchedEffect(key1 = mapType) {
        properties = properties.copy(mapType = mapType)
    }
    LaunchedEffect(key1 = ordreMissionWithLines, key2 = ordreMissionWithLinesFromOnline) {

        if (ordreMissionWithLines.isNotEmpty()) {
            tourneeViewModel.setShowAllCltOnCarteVisibility(false)
            if (ordreMissionWithLines.isNotEmpty() && ordreMissionWithEtat.isNotEmpty()) {
                tourneeViewModel.setSelectedOrdMission(ordreMissionWithLines.filter { it.key == ordreMissionWithEtat.first() } )

            }
            // tourneeViewModel.setSelectedOrdMission(ordreMissionWithLines)
        }
        delay(1000)
        mainViewModel.onShowAlertDialogChange(ordreMissionWithLines.isEmpty())
    }



   /* LaunchedEffect(key1 = selectedDate) {

      if(selectedDate.isNotEmpty()) return@LaunchedEffect
        tourneeViewModel.getOrdreMissionByDate()

    }*/
    LaunchedEffect(key1 = Unit) {
        CheckLocationSetting.checkLocationSetting(
            context = context,
            onDisabled = { intentSenderRequest ->
                gpsEnabelingResultRequest.launch(intentSenderRequest)
            },
            onEnabled = { locationViewModule.getCurrentLocation() }
        )
    }


    NavDrawer(
        navigate = { navigate(it) },
        drawer = drawer,
        navDrawerViewmodel = navDrawerVM,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        gesturesEnabled = drawer.isOpen,
        mainViewModel = mainViewModel,
        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingViewModel
    ) {
        Scaffold(
            topBar = {



             /*   AppBar(
                    showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                    baseConfig = selectedBaseconfig,
                    isConnected = isConnected,
                    onNavigationClick = { scope.launch { drawer.open() } },
                    title = stringResource(id = navDrawerVM.proCaisseSelectedMenu.title),
                )*/
            }
        ) { padding ->
            AskPermission(
                permission = listOf(
                    Manifest.permission.ACCESS_COARSE_LOCATION,
                    Manifest.permission.ACCESS_FINE_LOCATION
                ),
                permissionNotAvailableContent = { permissionState ->
                    Column(
                        modifier = Modifier
                            .verticalScroll(rememberScrollState())
                            //  .background(colorResource(id = R.color.black))
                            .fillMaxSize()
                            .systemBarsPadding(),
                           // .padding(top = 50.dp, start = 12.dp, end = 12.dp),
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        LottieAnim(lotti = R.raw.emptystate)
                        Spacer(modifier = Modifier.height(16.dp))

                        val textToShow = if (permissionState.shouldShowRationale) {
                            stringResource(R.string.access_gps_request_permession)
                        } else {
                            stringResource(R.string.gps_not_available)
                        }
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(textToShow)
                        Spacer(modifier = Modifier.height(8.dp))
                        Button(onClick = { permissionState.launchMultiplePermissionRequest() }) {
                            Text(stringResource(R.string.request_gps_auth))
                        }
                    }
                },
                content = {
                    LaunchedEffect(key1 = Unit) {
                        if(latitude == null || longitude == null)
                        locationViewModule.getCurrentLocation()
                    }

                    if (mainViewModel.showDatePicker) {
                        DatePickerView(
                            selectedDate = selectedDate,
                            confirmText = stringResource(R.string.confirm),
                            cancelText = stringResource(R.string.cancel),
                            setDateVisibility = { mainViewModel.onShowDatePickerChange(it) },
                            onSelectedDateChange = {
                                tourneeViewModel.onSelectedDateChange(it)
                                getProCaisseDataViewModel.getOrdreWithLines(
                                    baseConfig = selectedBaseconfig,
                                    utilisateur = utilisateur,
                                    date = it
                                )
                            }
                        )
                    }

                    if (batchUpdateLigneOrdreMissionState.error != null) {
                        CustomAlertDialogue(
                            title = stringResource(id = R.string.info_label),
                            msg = batchUpdateLigneOrdreMissionState.error,
                            openDialog = showAlertDialog,
                            setDialogueVisibility = { mainViewModel.onShowAlertDialogChange(it) },
                            customAction = { syncTourneeVM.resetUpdateLgOedMissionSatate() },
                            confirmText = stringResource(R.string.confirm),
                            cancelText = stringResource(R.string.cancel)
                        )
                    }

                    if (showEtatOrdreMission) {
                        ModalBottomSheet(
                            sheetState = sheetState,
                            onDismissRequest = {
                                scope.launch { sheetState.hide() }
                                tourneeViewModel.setVisibilityEtatOrdreMission(false)
                            }
                        ) {
                            Column(
                                modifier = Modifier.fillMaxWidth(),
                                verticalArrangement = Arrangement.Top,
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                if (etatOrdreMission.isNotEmpty()) {
                                    LazyRow(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceAround,
                                        verticalAlignment = Alignment.Top,
                                        state = listState
                                    ) {
//                                        item {
//                                            Text(
//                                                fontSize = MaterialTheme.typography.labelMedium.fontSize,
//                                                text = stringResource(id = R.string.etat_ordre_mission)
//                                            )
//                                        }
                                        items(
                                            //   items= filteredList.keys.toList()
                                            count = etatOrdreMission.size,
                                            key = { etatOrdreMission[it].codeEtatOrd }

                                        ) { index ->
                                            Text(
                                                fontSize = MaterialTheme.typography.labelSmall.fontSize,
                                                text = etatOrdreMission[index].libelleEtatOrd + " ",
                                                color = Color(etatOrdreMission[index].couleur)
                                            )
                                        }


                                    }
                                }

                                if (ordreMissionWithLines.isNotEmpty()) {

                                    Spacer(modifier = Modifier.height(12.dp))
                                    OutlinedButton(
                                        onClick = {
                                            tourneeViewModel.setVisibilityEtatOrdreMission(false)
                                            tourneeViewModel.setVisibilityListEtatOrdreMission(!showListOrdreMission)

                                        }
                                    ) {
                                        Icon(
                                            imageVector = Icons.TwoTone.Reorder,
                                            contentDescription = stringResource(id = R.string.your_divice_id)
                                        )
                                        Spacer(modifier = Modifier.width(12.dp))
                                        Text(text = stringResource(id = R.string.nbr_ordre_mission, ordreMissionWithEtat.size))
                                    }
                                }
                                Spacer(modifier = Modifier.height(12.dp))


                                OutlinedButton(
                                    onClick = {
                                        uiSettings = uiSettings.copy(zoomControlsEnabled = !uiSettings.zoomControlsEnabled)
                                    }) {
                                    /*   Icon(
                                       imageVector = if (!uiSettings.zoomControlsEnabled) Icons.Filled.ZoomIn else Icons.Filled.NoiseControlOff,
                                       contentDescription = stringResource(id = R.string.your_divice_id)
                                   )*/

                                    Text(text = if (!uiSettings.zoomControlsEnabled) stringResource(id = R.string.enable_ui_controls) else stringResource(id = R.string.disable_ui_controls))
                                }



                                Spacer(modifier = Modifier.height(12.dp))
                                OutlinedButton(
                                    onClick = {
                                        tourneeViewModel.setVisibilityEtatOrdreMission(false)
                                        tourneeViewModel.onShowMapTypeChange(!showMapType)
                                    }
                                ) {
                                    Icon(
                                        imageVector = Icons.TwoTone.Layers,
                                        contentDescription = stringResource(id = R.string.your_divice_id)
                                    )
                                    Spacer(modifier = Modifier.width(12.dp))
                                    Text(text = stringResource(id = R.string.map_type))
                                }


                                if (selectedLgOrdMissionWithNoCoordClient.isNotEmpty() || (showAllCltOnCarte && mainViewModel.cltListWithoutCoordonate.isNotEmpty())) {
                                    Spacer(modifier = Modifier.height(12.dp))
                                    OutlinedButton(
                                        onClick = {
                                            tourneeViewModel.setVisibilityEtatOrdreMission(false)
                                            tourneeViewModel.setVisibilityListCltWithNoAdress(!tourneeViewModel.showListCltWithNoAdress)

                                        }
                                    ) {
                                        Icon(
                                            imageVector = Icons.TwoTone.GpsOff,
                                            contentDescription = stringResource(id = R.string.your_divice_id)
                                        )
                                        Spacer(modifier = Modifier.width(12.dp))
                                        Text(
                                            text = stringResource(
                                                id = R.string.clt_sans_gps,
                                                nbrSansAdresse
                                            )
                                        )
                                    }
                                }


                                Spacer(modifier = Modifier.height(22.dp))
                            }
                        }

                    }

                    if (showMapType) {
                        ModalBottomSheet(
                            sheetState = sheetState,
                            onDismissRequest = {
                                scope.launch {
                                    sheetState.hide()
                                }
                                tourneeViewModel.onShowMapTypeChange(false)
                            },
                        ) {
                            Column(
                                modifier = Modifier.padding(start = 12.dp, end = 12.dp),
                                verticalArrangement = Arrangement.Top,
                                horizontalAlignment = Alignment.Start
                            ) {

                                Text(
                                    modifier = Modifier.fillMaxWidth(),
                                    text = stringResource(id = R.string.map_type),
                                    style = MaterialTheme.typography.titleLarge,
                                    textAlign = TextAlign.Center
                                )
                                Spacer(modifier = Modifier.height(12.dp))


                                LazyColumn(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                    contentPadding = PaddingValues(top = 12.dp)
                                ) {

                                    items(
                                        count = mapTypeList.size,
                                        key = {
                                            mapTypeList[it].name
                                        }
                                    ) { index ->
                                        val isSelected = mapType == mapTypeList[index]
                                        OutlinedCard (
                                            modifier = Modifier.padding(top = 15.dp, bottom = 15.dp),
                                            border = if (!isSelected) CardDefaults.outlinedCardBorder() else BorderStroke(color = MaterialTheme.colorScheme.outline, width = 3.dp),
                                            onClick = {
                                            tourneeViewModel.onMapTypeChange(mapTypeList[index])
                                        }) {
                                            Row(
                                                modifier = Modifier.fillMaxWidth().padding(start = 30.dp, end = 30.dp).height(50.dp),
                                                horizontalArrangement = Arrangement.Center,
                                                verticalAlignment = Alignment.CenterVertically,
                                            ) {
                                                 Icon(
                                                    imageVector = if (isSelected) Icons.TwoTone.Check else Icons.TwoTone.Map,
                                                    contentDescription = stringResource(id = R.string.your_divice_id)
                                                )

                                                Spacer(modifier = Modifier.width(12.dp))
                                                Text(text = mapTypeList[index].name)
                                            }

                                        }

                                    }


                                }


                                Spacer(modifier = Modifier.height(16.dp))
                            }


                        }
                    }

                    if (tourneeViewModel.onMarkerLongClicked) {
                        ModalBottomSheet(
                            sheetState = sheetState,
                            onDismissRequest = {
                                scope.launch {
                                    sheetState.hide()
                                }
                                tourneeViewModel.onMarkerLongClickedChange(false)
                            },
                        ) {

                            Column(
                                verticalArrangement = Arrangement.Top,
                                horizontalAlignment = Alignment.Start,
                                modifier = Modifier.padding(
                                    top = 20.dp,
                                    bottom = 30.dp,
                                    start = 12.dp,
                                    end = 12.dp
                                )

                            ) {
                                Spacer(modifier = Modifier.height(12.dp))



                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.Center,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    IconButton(
                                        //  modifier = Modifier.padding(top = 50.dp, end = 9.dp),
                                        onClick = {
                                            //  locationViewModule.resetCurrentLocation()
                                            //  mainViewModel.onSelectedClientChange(it)
                                            navigate(ClientsInfoRoute())
                                        }) {
                                        Icon(
                                            imageVector = Icons.Outlined.PersonPin,
                                            contentDescription = stringResource(id = R.string.your_divice_id)
                                        )
                                    }


                                    Spacer(modifier = Modifier.width(12.dp))
                                    Text(
                                        //  modifier = Modifier.fillMaxWidth(),
                                        text = stringResource(
                                            id = R.string.client_info,
                                            clientByCode.cLINomPren
                                        ),
                                        style = MaterialTheme.typography.titleLarge,
                                        textAlign = TextAlign.Center

                                    )
                                }

                                Spacer(modifier = Modifier.height(12.dp))



                                Spacer(modifier = Modifier.height(22.dp))
                                OutlinedButton(
                                    onClick = {
                                        val prefixeBl =
                                            prefixList.firstOrNull { it.pREIdTable == "Bon_livraison" }?.pREPrefixe
                                                ?: "BL_M_"

                                        bonLivraisonVM.generateCodeM(
                                            utilisateur = utilisateur,
                                            prefix = prefixeBl
                                        )


                                        val prefixePayment = prefixList.firstOrNull { it.pREIdTable == "ReglementCaisse" }?.pREPrefixe ?: "REGC_"

                                        paymentViewModel.generateCodeM(
                                            utilisateur = utilisateur,
                                            prefix = prefixePayment
                                        )
                                        locationViewModule.getCurrentLocation()

                                        navigate(AddBonLivraisonRoute(clientId = clientByCode.cLICode))
                                    }) {
                                    Text(
                                        text = stringResource(id = R.string.addbl),
                                        modifier = Modifier.fillMaxWidth()

                                    )
                                }


                                Spacer(modifier = Modifier.height(12.dp))

                                OutlinedButton(onClick = {
                                    mainViewModel.generateCodeM(
                                        utilisateur = utilisateur,
                                        prefix = "BC_M"
                                    )
                                    navigate(AddBonCommandeRoute(clientId = clientByCode.cLICode))
                                }) {
                                    Text(
                                        text = stringResource(id = R.string.addbc),
                                        modifier = Modifier.fillMaxWidth()

                                    )
                                }

                                Spacer(modifier = Modifier.height(12.dp))

                                OutlinedButton(
                                    onClick = {
                                        val prefixe = prefixList.firstOrNull { it.pREIdTable == "Bon_Retour" }?.pREPrefixe ?: "BR_"

                                        mainViewModel.generateCodeM(utilisateur = utilisateur, prefix = prefixe)
                                        navigate(AddBonRetourRoute(clientId = clientByCode.cLICode))
                                    }) {
                                    Text(
                                        text = stringResource(id = R.string.addbr),
                                        modifier = Modifier.fillMaxWidth()
                                    )
                                }

                                Spacer(modifier = Modifier.height(12.dp))


                            }


                        }

                    }

                    if (tourneeViewModel.onMarkerClicked) {
                        Dialog(
                            onDismissRequest = { tourneeViewModel.onMarkerClickedChange(false) },
                            properties = DialogProperties(usePlatformDefaultWidth = true),
                            content = {
                                Card(
                                    elevation = CardDefaults.cardElevation(),
                                    shape = RoundedCornerShape(15.dp)
                                ) {
                                    Column(
                                        verticalArrangement = Arrangement.Top,
                                        horizontalAlignment = Alignment.Start,
                                        modifier = Modifier.padding(
                                            top = 20.dp,
                                            bottom = 30.dp,
                                            start = 12.dp,
                                            end = 12.dp
                                        )
                                    ) {
                                        Text(
                                            text = clientByCode.cLINomPren,
                                            modifier = Modifier.fillMaxWidth(),
                                            textAlign = TextAlign.Center,
                                            style = MaterialTheme.typography.titleLarge
                                        )



                                        Text(
                                            text = stringResource(id = R.string.changer_etat),
                                            color = MaterialTheme.colorScheme.primary,
                                            fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
                                            fontSize = MaterialTheme.typography.titleMedium.fontSize,
                                            modifier = Modifier.padding(16.dp)
                                        )
                                        LazyColumn(contentPadding = PaddingValues(top = 12.dp)) {


                                            items(
                                                count = etatOrdreMission.size,
                                                key = { etatOrdreMission[it].codeEtatOrd }
                                            ) { index ->

                                                val isSelected =
                                                    etatOrdreMission[index].codeEtatOrd == longClickedlgOrdMissionWithClt.ligneOrdreMission.lIGOREtat

                                                Row(
                                                    Modifier
                                                        .fillMaxWidth()
                                                        .padding(top = 12.dp, bottom = 18.dp)
                                                        .selectable(
                                                            selected = isSelected,
                                                            role = Role.RadioButton,
                                                            onClick = {
                                                                tourneeViewModel.onSelectedEtatChange(etatOrdreMission[index].codeEtatOrd)
                                                                tourneeViewModel.onSelectedlgOrdMissionWithClt(
                                                                    longClickedlgOrdMissionWithClt.copy(
                                                                        ligneOrdreMission = longClickedlgOrdMissionWithClt.ligneOrdreMission.copy(
                                                                            lIGOREtat = etatOrdreMission[index].codeEtatOrd
                                                                        )
                                                                    )
                                                                )
                                                            }
                                                        )
                                                        .padding(horizontal = 16.dp),
                                                    verticalAlignment = Alignment.CenterVertically
                                                ) {
                                                    RadioButton(
                                                        selected = if (selectedEtat == "") isSelected else etatOrdreMission[index].codeEtatOrd == selectedEtat,
                                                        onClick = null, // null recommended for accessibility with screenreaders
                                                        //colors = RadioButtonColors(unselectedColor = Color(etatOrdreMission[index].couleur))
                                                    )
                                                    Text(
                                                        text = etatOrdreMission[index].libelleEtatOrd,
                                                        style = MaterialTheme.typography.bodyLarge,
                                                        modifier = Modifier.padding(start = 16.dp),
                                                        color = Color(etatOrdreMission[index].couleur)
                                                    )
                                                }
                                            }


                                            item {
                                                EditTextField(
                                                    modifier = Modifier.fillMaxWidth(0.8f),
                                                    text = tourneeViewModel.etatRemarque,
                                                    errorValue = null,
                                                    label = stringResource(R.string.remarques),
                                                    onValueChange = {
                                                        tourneeViewModel.onEtatRemarqueChange(it)
                                                    },
                                                    readOnly = false,
                                                    keyboardType = KeyboardType.Text
                                                )
                                            }

                                        }

                                        Row(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(top = 16.dp),
                                            horizontalArrangement = Arrangement.Center,
                                            verticalAlignment = Alignment.Top
                                        ) {

                                            OutlinedButton(onClick = {
                                                tourneeViewModel.onMarkerClickedChange(false)
                                            }) {
                                                Text(text = stringResource(R.string.quitter))
                                            }

                                            Spacer(modifier = Modifier.width(12.dp))
                                            Button(
                                                onClick = {
                                                    CheckLocationSetting.checkLocationSetting(
                                                        context = context,
                                                        onDisabled = { intentSenderRequest ->
                                                            gpsEnabelingResultRequest.launch(
                                                                intentSenderRequest
                                                            )
                                                        },
                                                        onEnabled = {

                                                            locationViewModule.getCurrentLocation()

                                                            tourneeViewModel.updateLigneOrdreMission(
                                                                ligneOrdreMission = longClickedlgOrdMissionWithClt.ligneOrdreMission,
                                                                remarque = tourneeViewModel.etatRemarque,
                                                                etatOrdreMission = if (selectedEtat != "") selectedEtat else longClickedlgOrdMissionWithClt.ligneOrdreMission.lIGOREtat,
                                                                latitude = latitude ?: 34.739330501022096,
                                                                longitude = longitude ?: 10.75393363814977
                                                            )

                                                            tourneeViewModel.onMarkerClickedChange(false)
                                                        }
                                                    )


                                                }) {
                                                Text(text = stringResource(R.string.confirm))
                                            }
                                        }

                                    }


                                }

                            }
                        )
                    }

                    if (showListOrdreMission) {
                        ModalBottomSheet(
                            sheetState = sheetState,
                            onDismissRequest = {
                                scope.launch { sheetState.hide() }
                                tourneeViewModel.setVisibilityListEtatOrdreMission(false)
                            }
                        ) {
                            Column(
                                verticalArrangement = Arrangement.Top,
                                horizontalAlignment = Alignment.Start
                            ) {

                                LazyColumn(contentPadding = PaddingValues(12.dp)) {
                                    item {
                                        Text(
                                            text = stringResource(R.string.nbr_ordre_mission, ordreMissionWithEtat.size),
                                            color = MaterialTheme.colorScheme.primary,
                                            fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
                                            fontSize = MaterialTheme.typography.titleMedium.fontSize,
                                            modifier = Modifier.padding(16.dp)
                                        )
                                        Spacer(modifier = Modifier.height(12.dp))
                                    }


                                    items(
                                        count = ordreMissionWithEtat.size,
                                        key = { ordreMissionWithEtat[it].ordreMission?.oRDCode!! }
                                    ) { index ->
                                        val ordreMission = ordreMissionWithEtat[index].ordreMission?: OrdreMission()
                                        val etatOrdMission = ordreMissionWithEtat[index].etatOrdreMission
                                        val libelEtatOrdMission = etatOrdMission?.libelleEtatOrd ?: ordreMission.oRDCode


                                        Spacer(modifier = Modifier.padding(top = 12.dp))


                                        val isSelected = ordreMission.oRDCode == selectedOrdMission.oRDCode

                                        OutlinedCard(
                                            //   modifier = Modifier.padding(start = 12.dp, end = 12.dp),
                                            onClick = {
                                                tourneeViewModel.setSelectedOrdMission(ordreMissionWithLines.filter { it.key.ordreMission?.oRDCode == ordreMission.oRDCode })

                                            }) {
                                            Row(
                                                modifier = Modifier
                                                    .fillMaxWidth()
                                                    .height(56.dp)

                                                    .padding(horizontal = 16.dp),
                                                verticalAlignment = Alignment.CenterVertically
                                            ) {
                                                RadioButton(
                                                    selected = if (selectedOrdMission.oRDCode == "") isSelected else ordreMission.oRDCode == selectedOrdMission.oRDCode,
                                                    onClick = null, // null recommended for accessibility with screenreaders
                                                    //colors = RadioButtonColors(unselectedColor = Color(etatOrdreMission[index].couleur))
                                                )

                                                Text(

                                                    text = buildAnnotatedString {
                                                        append(ordreMission.oRDCode)


                                                        withStyle(
                                                            SpanStyle(
                                                                color = if(etatOrdMission!= null) Color(etatOrdMission.couleur) else Color.Unspecified,
                                                                fontSize = MaterialTheme.typography.bodyMedium.fontSize
                                                            )
                                                        ) {
                                                            append(" ($libelEtatOrdMission)")
                                                        }
                                                    },
                                                    style = MaterialTheme.typography.bodyLarge,
                                                    modifier = Modifier.padding(start = 16.dp)
                                                )
                                            }

                                            //  Spacer(modifier = Modifier.height(12.dp))
                                            Text(
                                                modifier = Modifier.padding(start = 16.dp),
                                                text = stringResource(id = R.string.stations_count, ligneOrdreMission.filter { it.ligneOrdreMission.lIGORCode == ordreMission.oRDCode }.size)
                                            )
                                            if(!ordreMission.oRD_Type.isNullOrEmpty()) {
                                                Spacer(modifier = Modifier.height(12.dp))


                                                Text(
                                                    modifier = Modifier.padding(start = 16.dp),
                                                    text = stringResource(id = R.string.type_ordre_mission, ordreMission.oRD_Type?:"")
                                                )

                                            }

                                            if(!ordreMission.oRDNote.isNullOrEmpty()) {
                                                Spacer(modifier = Modifier.height(12.dp))
                                                Text(
                                                    modifier = Modifier.padding(start = 16.dp),
                                                    text = stringResource(id = R.string.note_field_extra, ordreMission.oRDNote?:"")
                                                )
                                            }

                                            Spacer(modifier = Modifier.height(12.dp))



                                        }

                                        Spacer(modifier = Modifier.height(12.dp))

                                    }


                                }

                            }


                        }


                    }

                    if (tourneeViewModel.showListCltWithNoAdress) {
                        ModalBottomSheet(
                            sheetState = sheetState,
                            onDismissRequest = {
                                scope.launch { sheetState.hide() }
                                tourneeViewModel.setVisibilityListCltWithNoAdress(false)
                            }
                        ) {
                            Column(
                                modifier = Modifier.padding(start = 12.dp, end = 12.dp),
                                verticalArrangement = Arrangement.Top,
                                horizontalAlignment = Alignment.Start
                            ) {

                                Spacer(modifier = Modifier.height(12.dp))

                                EditTextField(
                                    modifier = Modifier.fillMaxWidth(),
                                    text = clientSearchText,
                                    errorValue = null,
                                    label = stringResource(id = R.string.clt_sans_gps, nbrSansAdresse),
                                    onValueChange = { tourneeViewModel.onClientSearchTextChange(it) },
                                    readOnly = false,
                                    keyboardType = KeyboardType.Text
                                )
                                Spacer(modifier = Modifier.height(12.dp))

                                LazyColumn(
                                    modifier = Modifier.fillMaxHeight(),
                                    contentPadding = PaddingValues(top = 12.dp)
                                ) {
                                    if (showAllCltOnCarte) {
                                        items(
                                            count = cltListWithoutCoordonatFiltered.size,
                                            key = { cltListWithoutCoordonatFiltered[it].cLICode }
                                        ) { index ->
                                            Spacer(modifier = Modifier.padding(top = 12.dp))
                                            RowClientList(
                                                client = cltListWithoutCoordonatFiltered[index],
                                                onClick = { mainViewModel.onSelectedClientChange(cltListWithoutCoordonatFiltered[index]) },
                                                onLongPress = {
                                                    tourneeViewModel.setVisibilityListCltWithNoAdress(false)
                                                    tourneeViewModel.onMarkerLongClickedChange(true)
                                                    mainViewModel.onSelectedClientChange(cltListWithoutCoordonatFiltered[index])
                                                }
                                            )
                                        }
                                    }
                                    else {
                                        items(
                                            count = filteredSelectedLgOrdMissionWithNoCoordClient.size,
                                            key = { filteredSelectedLgOrdMissionWithNoCoordClient[it].ligneOrdreMission.lIGORClt }
                                        ) { index ->
                                            Spacer(modifier = Modifier.padding(top = 12.dp))

                                            val etatOrdMission = etatOrdreMission.firstOrNull { it.codeEtatOrd == filteredSelectedLgOrdMissionWithNoCoordClient[index].ligneOrdreMission.lIGOREtat }

                                            val etat = etatOrdMission?.libelleEtatOrd ?: filteredSelectedLgOrdMissionWithNoCoordClient[index].ligneOrdreMission.lIGOREtat


                                            val clientNom =
                                                if (filteredSelectedLgOrdMissionWithNoCoordClient[index].client != null)
                                                    filteredSelectedLgOrdMissionWithNoCoordClient[index].client?.cLINomPren?: filteredSelectedLgOrdMissionWithNoCoordClient[index].client?.cLICode?: "N/A"
                                                else
                                                    stringResource(
                                                        id = R.string.code_client_non_disponible,
                                                        filteredSelectedLgOrdMissionWithNoCoordClient[index].ligneOrdreMission.lIGORClt
                                                    )

                                            RowClientList(
                                                client = filteredSelectedLgOrdMissionWithNoCoordClient[index].client!!,
                                                cLINomPren = "$clientNom ($etat)",
                                                color = if (etatOrdMission != null) Color(etatOrdMission.couleur) else Color.Unspecified,
                                                onClick = {
                                                    tourneeViewModel.onSelectedlgOrdMissionWithClt(
                                                        filteredSelectedLgOrdMissionWithNoCoordClient[index]
                                                    )
                                                    mainViewModel.onSelectedClientChange(
                                                        filteredSelectedLgOrdMissionWithNoCoordClient[index].client
                                                            ?: Client(
                                                                cLINomPren = "N/A",
                                                                cLICode = filteredSelectedLgOrdMissionWithNoCoordClient[index].ligneOrdreMission.lIGORClt,
                                                                cLIType = "N/A",
                                                                cLIAdresse = "N/A",
                                                                cLISolde = "N/A"

                                                            )
                                                    )
                                                    if((filteredSelectedLgOrdMissionWithNoCoordClient[index].client?.cLINomPren?:"N/A") == "N/A") {

                                                        showToast(
                                                            context = context,
                                                            toaster = toaster,
                                                            message = context.resources.getString(
                                                                R.string.code_client_non_disponible,
                                                                filteredSelectedLgOrdMissionWithNoCoordClient[index].ligneOrdreMission.lIGORClt
                                                            ),
                                                            type = ToastType.Warning,
                                                            duration = Duration.INFINITE,
                                                            action = TextToastAction(
                                                                text = context.resources.getString(R.string.continuer),
                                                                onClick = {
                                                                    toaster.dismiss(it)
                                                                    tourneeViewModel.onMarkerClickedChange(true)
                                                                },
                                                            )

                                                        )
                                                        return@RowClientList
                                                    }
                                                    else {
                                                        tourneeViewModel.onMarkerClickedChange(true)
                                                    }


                                                },
                                                onLongPress = {
                                                    if (filteredSelectedLgOrdMissionWithNoCoordClient[index].client == null) {
                                                        showToast(
                                                            context = context,
                                                            toaster = toaster,
                                                            message = context.resources.getString(
                                                                R.string.code_client_non_disponible,
                                                                filteredSelectedLgOrdMissionWithNoCoordClient[index].ligneOrdreMission.lIGORClt
                                                            ),
                                                            type = ToastType.Success
                                                        )
                                                        return@RowClientList
                                                    }
                                                    tourneeViewModel.onMarkerLongClickedChange(true)
                                                    mainViewModel.onSelectedClientChange(filteredSelectedLgOrdMissionWithNoCoordClient[index].client
                                                            ?: Client(
                                                                cLINomPren = "",
                                                                cLICode = filteredSelectedLgOrdMissionWithNoCoordClient[index].ligneOrdreMission.lIGORClt
                                                            )
                                                    )
                                                }
                                            )
                                        }
                                    }
                                }
                                Spacer(modifier = Modifier.height(16.dp))
                            }
                        }
                    }


                        CustomAlertDialogue(
                            title = context.getString(R.string.tourne_affectée,
                                selectedDate.ifEmpty { getCurrentDate() }),
                            msg = context.getString(R.string.pas_de_tourne_affectée),
                            openDialog = showAlertDialog,
                            confirmText = context.getString(R.string.OK),
                            cancelText = "",
                            setDialogueVisibility = { mainViewModel.onShowAlertDialogChange(it) },
                            customAction = { tourneeViewModel.setShowAllCltOnCarteVisibility(true) },
                            negatifAction = { tourneeViewModel.setShowAllCltOnCarteVisibility(false) }
                        )


                    Box(
                        contentAlignment = Alignment.TopEnd,
                        modifier = Modifier.fillMaxSize().padding(padding)
                    ) {

                        GoogleMap(
                            mergeDescendants = false,
                            modifier = Modifier.matchParentSize(),
                            cameraPositionState = cameraPositionState,
                            properties = properties,
                            uiSettings = uiSettings
                        ) {
                            ClientMarkersOnCarte(
                                toaster = toaster,
                                tourneeViewModel = tourneeViewModel,
                                mainViewModel = mainViewModel,
                                selectedLgOrdMissionWithCoordClient = selectedLgOrdMissionWithCoordClient,
                                cltListWithCoordonate = cltListWithCoordonate,
                                etatOrdreMission = etatOrdreMission,
                                myCurrentLatitude = latitude ?: 34.739330501022096,
                                myCurrentLongitude = longitude ?: 10.75393363814977,
                                onInfoWindowLongClick = {
                                    if(latitude == null || longitude == null) {
                                        showToast(
                                            context = context,
                                            toaster = toaster,
                                            message = context.getString(R.string.gps_coordonate_invalid),
                                            type =  ToastType.Error
                                            )
                                        return@ClientMarkersOnCarte
                                    }
                                    val distance = LocationUtils.getDistance(
                                        lat1 = it.cltLatitude!!,
                                        lon1 = it.cltLongitude!!,
                                        myLat = latitude,
                                        myLong = longitude
                                    )
                                    if (abs(distance) > LocationUtils.MAX_DISTANCE_TO_CLIENTS_IN_METERS) {
                                        showToast(
                                            context = context,
                                            toaster = toaster,
                                            message = context.getString(
                                                R.string.must_be_near_client,
                                                StringUtils.metersToKilometers(distance)
                                            ) + " \n"+ context.getString(R.string.client_info, it.cLINomPren),
                                            type =  ToastType.Warning,
                                            )
                                        return@ClientMarkersOnCarte
                                    }
                                    tourneeViewModel.onMarkerLongClickedChange(true)
                                    mainViewModel.onSelectedClientChange(it)
                                }
                            )
                        }

                        if(ordreMissionWithLinesState.loading) {
                            LottieAnim(
                                modifier = Modifier.align(Alignment.Center),
                                lotti = R.raw.loading,
                                size = 250.dp
                            )
                            // return@Box
                        }

                        val configuration = LocalConfiguration.current
                        val screenWidth = configuration.screenWidthDp

                        Card(
                            modifier = Modifier.padding(top = 45.dp, end = (screenWidth - 50).dp ),
                            onClick = {  scope.launch { drawer.open() } }) {
                                    Icon(
                                        imageVector = Icons.TwoTone.Menu,
                                        contentDescription = stringResource(id = R.string.your_divice_id),
                                        tint = MaterialTheme.colorScheme.error
                                    )
                                }

                        Card(
                            modifier = Modifier.padding(top = 50.dp, end = 9.dp),
                            onClick = { tourneeViewModel.setVisibilityEtatOrdreMission(!showEtatOrdreMission) }) {
                                Icon(
                                    imageVector = Icons.Outlined.Info,
                                    contentDescription = stringResource(id = R.string.your_divice_id)
                                )
                        }

                         Card(
                             modifier = Modifier.padding(top = 85.dp, end = 9.dp),
                             enabled = isConnected,
                             onClick = { mainViewModel.onShowDatePickerChange(true) }) {
                                 Icon(
                                     imageVector = Icons.Rounded.DateRange,
                                     contentDescription = stringResource(id = R.string.your_divice_id)
                                 )
                         }
                    }
                }
            )
        }
    }
}













