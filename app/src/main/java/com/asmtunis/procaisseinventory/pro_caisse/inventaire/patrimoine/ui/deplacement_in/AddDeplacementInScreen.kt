package com.asmtunis.procaisseinventory.pro_caisse.inventaire.patrimoine.ui.deplacement_in

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.Constants
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.SetNumSerieView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.ControleInventaire
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.updateInvPatQty
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.tables.three_column.ThreeColumnTableWithImage
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu
import java.util.Locale

@Composable
fun AddDeplacementInScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    navigateUp: () -> Unit,
    clientId: String,
    navDrawerViewModel: NavigationDrawerViewModel,
    mainViewModel: MainViewModel,
    barCodeViewModel : BarCodeViewModel,
    settingViewModel : SettingViewModel,

    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    proCaisseViewModels: ProCaisseViewModels
) {
    val context = LocalContext.current

   val invPatViewModel = proCaisseViewModels.invPatViewModel
   val selectPatrimoineVM = proCaisseViewModels.selectPatrimoineVM
   val patrimoineVerificationState = selectPatrimoineVM.patrimoineVerificationState




   val barCodeInfo = barCodeViewModel.barCodeInfo

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val imageList = mainViewModel.imageList
    val codeM = mainViewModel.codeM
    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val marqueList = mainViewModel.marqueList
    val utilisateur = mainViewModel.utilisateur

    val selectedPatrimoineList = selectPatrimoineVM.selectedPatrimoineList
    val typePieceFilter = selectPatrimoineVM.typePiece
    val selectedPatrimoine = selectPatrimoineVM.selectedPatrimoine

    val haveCamera = dataViewModel.getHaveCameraDevice()

    val clientList = mainViewModel.clientList
    val clientByCode = clientList.firstOrNull { it.cLICode == clientId }?: Client()//mainViewModel.clientByCode
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = { popBackStack() },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = codeM,//context.getString(R.string.invpat_number_field,"nbr todo"),
            )
        },
        floatingActionButton = {
            Column {
                FloatingActionButton(
                    onClick = {
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    }) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = stringResource(id = R.string.cd_achat_button)
                    )
                }


                Spacer(modifier = Modifier.height(12.dp))
                FloatingActionButton(
                    onClick = {
                            invPatViewModel.saveInvPat(
                                articleMapByBarCode = articleMapByBarCode,
                                codeM = codeM,
                                listSelectedPatrimoine = selectedPatrimoineList,
                                exercice = mainViewModel.exerciceList.first().exerciceCode,
                                client = clientByCode,
                                utilisateur = utilisateur,
                                typeInv = invPatViewModel.typeInvetaireState,
                                devEtat = Constants.PATRIMOINE,
                                onComplete = { navigateUp() }
                            )


                    }) {
                    Icon(
                        imageVector =  Icons.Default.Save,
                        contentDescription = stringResource(id = R.string.cd_achat_button)
                    )
                }
            }

        }
    ) {padding ->
        if (selectPatrimoineVM.showSetNumeSerie)
            SetNumSerieView(
                articleMapByBarCode = articleMapByBarCode,
                haveCamera = haveCamera,
                marqueList = marqueList,
                selectedPatrimoine = selectedPatrimoine,
                selectedPatrimoineList = selectedPatrimoineList,
                onNumSerieChange = {
                    selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(numSerie = it))
                },
                onDismiss = {

                    selectPatrimoineVM.onShowSetNumeSerieChange(false)
                    selectPatrimoineVM.resetPatrimoineVerificationState()
                },
                onConfirm = {
                    val controlInvPat = ControleInventaire(
                        LG_DEV_NumSerie = selectedPatrimoine.numSerie,
                        DEV_CodeClient = clientByCode.cLICode,
                        DEV_info3 = invPatViewModel.typeInvetaireState
                    )

                    selectPatrimoineVM.patrimoineVerification(
                        baseConfig = mainViewModel.selectedBaseconfig,
                        controlPatrimoine = controlInvPat
                    )

                },
                onAddInvPat = {
                    selectPatrimoineVM.onShowSetNumeSerieChange(false)
                    val codeArt = selectPatrimoineVM.invPatByNumSerie.entries.first().value.first().lGDEVCodeArt
                    updateInvPatQty(
                        imageList = imageList,
                        articleCode = codeArt,
                        numeSerie = selectedPatrimoine.numSerie,
                        patrimoineVerificationState = patrimoineVerificationState,
                        selectedPatrimoineList =  selectedPatrimoineList,
                        addItemToSelectedPatrimoineList = {
                            selectPatrimoineVM.addItemToSelectedPatrimoineList(it)
                        },
                        marque = marqueList.firstOrNull { it.mARCode == selectedPatrimoine.marqueCode }?: Marque(),
                        note = selectedPatrimoine.note
                    )


                    selectPatrimoineVM.resetPatrimoineVerificationState()

                },
                onBareCodeScan = {
                    openBareCodeScanner(
                        navigate = { navigate(it) },
                        onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) }
                    )
                },
                barCodeInfo = barCodeInfo,
                patrimoineVerificationState = patrimoineVerificationState,
                showDropDownMenuComposable = true,
                dropDownMenuComposable = {
                  val marque = marqueList.firstOrNull { it.mARCode == selectedPatrimoine.marqueCode }?: Marque()
                    GenericDropdownMenu(
                        label = stringResource(R.string.type_piece),
                        designation = marque.mARDesignation,

                        itemList = if(typePieceFilter.isNotEmpty()) marqueList.filter { it.mARDesignation.lowercase(Locale.ROOT).contains(typePieceFilter.lowercase(Locale.ROOT)) } else marqueList,
                        fiterValue = typePieceFilter,
                        onFilterValueChange = { selectPatrimoineVM.onTypePieceChange(it) },
                        showFilter = true,
                        itemExpanded = invPatViewModel.marqueExpanded,
                        selectedItem = marque,
                        onItemExpandedChange = {
                        invPatViewModel.onMarqueExpandedChange(it)
                    },
                        getItemDesignation = { it.mARDesignation },
                        getItemSyncStatus = { it.isSync },
                        getItemTrailing = { it.mARCode },
                    onClick = {
                        selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(marqueCode = it.mARCode))
                        invPatViewModel.onMarqueExpandedChange(false)
                    },
                        lottieAnimEmpty = {
                            LottieAnim(lotti = R.raw.emptystate)
                        },
                        lottieAnimError = {
                            LottieAnim(lotti = R.raw.connection_error, size = it)
                        }
                    )
                },
                onNoteChange = {
                    selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(note = it))
                }
            )

        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {
            Text(text = clientByCode.cLINomPren)
            ThreeColumnTableWithImage(
                articleMapByBarCode = articleMapByBarCode,
                marqueList = marqueList,
                haveCamera = haveCamera,
                canModify = true,
                selectedPatrimoineList = selectedPatrimoineList,
                onPress = {
                    //TODO Maybe show more detail : TVA / DISCOUNT / . . .
                }
            )
        }
    }
}
