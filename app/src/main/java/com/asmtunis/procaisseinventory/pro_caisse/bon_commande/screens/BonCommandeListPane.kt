package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.screens

import android.Manifest
import android.app.Activity.RESULT_OK
import android.os.Build
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DrawerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals.DEVICE_ID
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.BluetoothUtil.enableBluetooth
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothInfoDialogue
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AddBonCommandeRoute
import com.asmtunis.procaisseinventory.core.navigation.AddBonLivraisonRoute
import com.asmtunis.procaisseinventory.core.navigation.BonCommandeDetailRoute
import com.asmtunis.procaisseinventory.core.print.PrintFunctions
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.wifi.WifiPrintViewModel
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.getClientName
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.BonCommandeUtils.bcTobl
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommandeWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommandeWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.view_model.BonCommandeViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.bottom_sheet.CustomModalBottomSheet
import com.asmtunis.procaisseinventory.shared_ui_components.floating_button.SnapScrollingButton
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.ListItem
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyColumn
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.FondCaisseInputView
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.utils.durationSincePast
import com.simapps.ui_kit.utils.getDate
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BonCommandeListPane(
    drawer: DrawerState,
    navigate: (route: Any) -> Unit,
    navDrawerViewmodel: NavigationDrawerViewModel,
    clientId: String,
    bonCommandeVM: BonCommandeViewModel,
    mainViewModel: MainViewModel,
    dataViewModel: DataViewModel,
    selectArtMobilityVM: SelectArticleCalculViewModel,
    networkViewModel: NetworkViewModel,
    printViewModel: PrintViewModel,
    bluetoothVM: BluetoothViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    wifiPrintVM: WifiPrintViewModel,
    settingVM: SettingViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    syncProcaisseViewModels: SyncProcaisseViewModels,

    ) {


   val syncBonCommandeViewModel = syncProcaisseViewModels.syncBonCommandeViewModel
    val uiWindowState = settingVM.uiWindowState

    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val sheetState = rememberModalBottomSheetState()
    val bonCommandeListstate = bonCommandeVM.bonCommandeListstate
    val listFilter = bonCommandeListstate.search
    val listOrder = bonCommandeListstate.listOrder
    val filterList = context.resources.getStringArray(R.array.bn_commande_filter)

    val exerciceCode = mainViewModel.exerciceList.firstOrNull()?.exerciceCode?: ""
    val utilisateur = mainViewModel.utilisateur
    val clientList = mainViewModel.clientList




    val listState = rememberLazyListState()
    val density = LocalDensity.current
    val bonCommandeState = getProCaisseDataViewModel.bonCommandeState
    val isLoading = bonCommandeState.loading
    val sessionCaisse = navDrawerViewmodel.sessionCaisse
    val searchTextState = bonCommandeVM.searchTextState
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val showSearchView = bonCommandeVM.showSearchView
    val clientByCode = clientList.firstOrNull { it.cLICode ==  clientId}?: Client() //mainViewModel.clientByCode
    val proCaisseAuthorization = getProCaisseDataViewModel.authorizationList


    val haveBCtoBLAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.BC_TO_BL }
    val haveBLAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.BL }

    val haveClotureSessionAutoAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.CLOT_SESSION_AUTO }

    val closeSessionCaisse = haveClotureSessionAutoAuthorisation && durationSincePast(dateInPast = sessionCaisse.sCDateHeureCrea ?: "2023-05-15 01:00:00") > 0

    val hasPromo = mainViewModel.hasPromo(mainViewModel.stationList.firstOrNull { it.sTATCode == utilisateur.Station })


    val selectedBonCommandeWithClient = bonCommandeVM.selectedBonCommandeWithClient
    val isLoadingFromLocalDb = bonCommandeVM.isLoadingFromLocalDb

    val selectedListLgBonCommandeWithArticle = bonCommandeVM.selectedListLgBonCommandeWithArticle

    val selectedBonCommande = selectedBonCommandeWithClient.bonCommande ?: BonCommande()
    val isConnected = networkViewModel.isConnected

    val printParams = dataViewModel.printData
    val firstTimeConnected = printViewModel.firstTimeConnected



    val canTransfromBCToBL = selectedBonCommande.dEVObservation.isNullOrBlank() &&
            haveBLAuthorisation &&
            haveBCtoBLAuthorisation &&
            selectedBonCommande.status == ItemStatus.SELECTED.status &&
            !mainViewModel.listTicket.any { it.tIKNumTicketM == selectedBonCommande.dEVNum } // To Prevent Transform the same BC to Bl multiple times

    LaunchedEffect(
        key1 = searchTextState.text,
        key2 = bonCommandeListstate.lists,
        // key4=state.filterByStationSource
    ) {
        bonCommandeVM.filterBonCommande(bonCommandeFilterListState = bonCommandeListstate, utilisateur = utilisateur)
    }

    LaunchedEffect(key1 = clientByCode) {
        if(clientByCode == Client()) return@LaunchedEffect

        bonCommandeVM.onSearchValueChange(TextFieldValue(clientByCode.cLICode))
        bonCommandeVM.onEvent(
            event = ListEvent.ListSearch(ListSearch.ThirdSearch()),
            utilisateur = utilisateur,
        )
    }

    LaunchedEffect(key1 = firstTimeConnected) {
        if (!firstTimeConnected) return@LaunchedEffect


        printViewModel.awaitPrint(
            context = context,
            toPrint = {
                printViewModel.printBonCommande(
                    context = context,
                    bonCommandeWithClient = selectedBonCommandeWithClient,
                    lgBonCommandeWithArticle = selectedListLgBonCommandeWithArticle,
                    utilisateur = utilisateur,
                    printParams = printParams
                )
            },
        )

        bonCommandeVM.onShowCustomModalBottomSheetChange(false)

        printViewModel.onFirstTimeConnectedChange(firstConnect = false)
    }


    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingVM.isDarkTheme)


    val isBlueToothAvailable =
        com.asmtunis.procaisseinventory.core.connectivity.bluetooth.BluetoothUtil.checkBluetoothAvailability(
            bluetoothVM = bluetoothVM,
            context = context
        )

    val enableBluetoothLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            // Bluetooth enabled successfully
        } else {
            // Handle Bluetooth enabling failure
        }
    }


    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestMultiplePermissions()
    ) { perms ->
        val canEnableBluetooth = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            perms[Manifest.permission.BLUETOOTH_CONNECT] == true
        } else true

        if (canEnableBluetooth && !bluetoothVM.isBluetoothEnabled && isBlueToothAvailable) {
            enableBluetooth(enableBluetoothLauncher = enableBluetoothLauncher)
        } else if (!isBlueToothAvailable) {
            // Handle permission denied case
                showToast(
                    context = context,
                    toaster = toaster,
                    message = context.resources.getString(R.string.bluetooth) + "\n " + context.resources.getString(
                        R.string.bluetooth_not_supported
                    ),
                    type = ToastType.Info,
                )
        }
    }



        Scaffold(
            topBar = {
                AppBar(
                    onNavigationClick = { scope.launch { drawer.open() } },
                    showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                    title = stringResource(id = navDrawerViewmodel.proCaisseSelectedMenu.title),
                    titleVisibilty = !showSearchView && searchTextState.text.isEmpty(),
                    actions = {
                        SearchSectionComposable(
                            label = stringResource(
                                id = R.string.filter_by,
                                when (listFilter) {
                                    is ListSearch.FirstSearch -> filterList[0]
                                    is ListSearch.SecondSearch -> filterList[1]
                                    else -> filterList[2]
                                },
                            ),
                            searchVisibility = showSearchView || searchTextState.text.isNotEmpty(),
                            searchTextState = searchTextState,
                            onSearchValueChange = {
                                bonCommandeVM.onSearchValueChange(TextFieldValue(it))
                                if (it == "") {
                                    /** this bloc to handle search visibility when custom search by client*/
                                    /** this bloc to handle search visibility when custom search by client*/
                                    /** this bloc to handle search visibility when custom search by client*/
                                    /** this bloc to handle search visibility when custom search by client*/
                                   // mainViewModel.onSelectedClientChange(Client())
                                }
                            },
                            onShowSearchViewChange = {
                                bonCommandeVM.onShowSearchViewChange(it)
                                if (!it) {
                                    /** this bloc to handle search visibility when custom search by client*/
                                    /** this bloc to handle search visibility when custom search by client*/
                                    /** this bloc to handle search visibility when custom search by client*/
                                    /** this bloc to handle search visibility when custom search by client*/
                                    bonCommandeVM.onSearchValueChange(TextFieldValue(""))
                                   // mainViewModel.onSelectedClientChange(Client())
                                }
                            },
                            onShowCustomFilterChange = {
                                bonCommandeVM.onShowCustomFilterChange(it)
                            },
                        )
                    },
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                )
            },
            floatingActionButton = {
                SnapScrollingButton(
                    isScrollInProgress = listState.isScrollInProgress,
                    isVisible = remember { derivedStateOf { listState.firstVisibleItemIndex } }.value > 15,
                    density = density,
                    animateScrollToItem = {
                        listState.animateScrollToItem(index = it)
                    }
                )
            }
        ) { padding ->
            if (printViewModel.openPrintInfoDialogue) {
                BluetoothInfoDialogue(
                    printResult = printViewModel.printResult,
                    onOpenPrintInfoDialogueChange = {
                        printViewModel.onOpenPrintInfoDialogueChange(it)
                    }
                )
            }

            if (mainViewModel.addSessionCaisseState.data != null) {
                bcTobl(
                    bonCommandeWithClient = selectedBonCommandeWithClient,
                    lgBonCommandeWithArticle = selectedListLgBonCommandeWithArticle,
                    setTableList = { lgBonCommande ->
                        bonCommandeVM.setTableList(
                            hasPromo = hasPromo,
                            ligneBCWithArticle = lgBonCommande,
                            updateSelectedArticleMobilityList = {
                                selectArtMobilityVM.setSelectedArticll(it)
                                selectArtMobilityVM.updateSelectedArticleMobilityList()
                                                                },
                            getPrice = { selectArtMobilityVM.getPrice(it) },
                        )
                    },
                    setModify = {
                        mainViewModel.canModify(false)
                        mainViewModel.resetAddSessionCaisseState()
                        navigate(AddBonLivraisonRoute(clientId = clientId))
                    }
                )
            }

            if (mainViewModel.showFondCaisseDialogue) {
                FondCaisseInputView(
                    onDismissRequest = {
                        mainViewModel.setfondcaisse("")
                        mainViewModel.setFondCaisseDialogueVisibility(false)
                    },
                    onDismiss = {
                        mainViewModel.setfondcaisse("")
                        mainViewModel.setFondCaisseDialogueVisibility(false)
                    },
                    onConfirm = {
                        mainViewModel.addSessionCaisse(
                            baseConfig = mainViewModel.selectedBaseconfig,
                            utilisateur = utilisateur,
                            sessionCaisse = sessionCaisse,
                            androidId = DEVICE_ID
                        )

                        mainViewModel.setFondCaisseDialogueVisibility(false)
                    },
                    onValueChange = {
                        mainViewModel.setfondcaisse(it)
                    },
                    text = mainViewModel.fondcaisse,
                )
            }


            if (bonCommandeVM.showCustomModalBottomSheet) {
                CustomModalBottomSheet(
                    remoteResponseState = syncBonCommandeViewModel.responseAddBonCommandeState,
                    title = selectedBonCommande.dEVNum,
                    showDeleteIcon = selectedBonCommande.status == ItemStatus.WAITING.status || selectedBonCommande.status == ItemStatus.INSERTED.status,
                    status = selectedBonCommande.status,
                    canTransfromBCToBL = canTransfromBCToBL,
                    onDismissRequest = { bonCommandeVM.onShowCustomModalBottomSheetChange(false) },
                    onDeleteRequest = {
                        if (selectedBonCommande == BonCommande()) {
                            return@CustomModalBottomSheet
                        }
                        bonCommandeVM.deleteBonCommandeAndItsLines(
                            listStationStockArticl = mainViewModel.stationStockArticlMapByBarCode,
                            updateQtePerStation = {newQteStation, newSartQteDeclare, codeArticle, codeStation->
                                mainViewModel.updateQtePerStation (
                                    newQteStation = newQteStation,
                                    newSartQteDeclare = newSartQteDeclare,
                                    codeArticle = codeArticle,
                                    codeStation = codeStation
                                )
                            },
                            updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->

                                mainViewModel.updateArtQteStock(
                                    newQteAllStations = newQteAllStations,
                                    newQteStation = newQteStation,
                                    codeArticle = codeArticle
                                )
                            }
                        )
                    },
                    onPrintRequest = {
                        PrintFunctions.print(
                            context = context,
                            toaster = toaster,
                            printParams = printParams,
                            navigate = { navigate(it) },
                            printViewModel = printViewModel,
                            bluetoothVM = bluetoothVM,
                            toPrintBT = {
                                printViewModel.printBonCommande(
                                    context = context,
                                    bonCommandeWithClient = selectedBonCommandeWithClient,
                                    lgBonCommandeWithArticle = selectedListLgBonCommandeWithArticle,
                                    utilisateur = utilisateur,
                                    printParams = printParams
                                )
                            },
                            toPrintWifi = {
                                wifiPrintVM.createBCPDFFile(
                                    context = context,
                                    bonCommandeWithClient = selectedBonCommandeWithClient,
                                    lgBonCommandeWithArticle = selectedListLgBonCommandeWithArticle,
                                    listener = {}
                                )
                            }
                        )
                    },
                    onBCtoBL = {
                        if (closeSessionCaisse) {
                            mainViewModel.setFondCaisseDialogueVisibility(true)
                            return@CustomModalBottomSheet
                        }
                        selectArtMobilityVM.setControlQte(false)

                        bcTobl(
                            bonCommandeWithClient = selectedBonCommandeWithClient,
                            lgBonCommandeWithArticle = selectedListLgBonCommandeWithArticle,
                            setTableList = { lgBonCommande ->
                                bonCommandeVM.setTableList(
                                    ligneBCWithArticle = lgBonCommande,
                                    hasPromo = hasPromo,
                                    updateSelectedArticleMobilityList = {
                                        selectArtMobilityVM.setSelectedArticll(it)
                                        selectArtMobilityVM.updateSelectedArticleMobilityList()
                                      },
                                    getPrice = { selectArtMobilityVM.getPrice(it) },
                                )
                            },
                            setModify = {
                                mainViewModel.canModify(false)
                             //   NavigationDrawerViewModel.proCaisseDrawerItems.find { it.id == ID.BON_LIVRAISON_ID }?.let { navDrawerViewmodel.onSelectedMenuChange(it) }
                                navigate(AddBonLivraisonRoute(clientId = clientId))
                            }
                        )
                    },
                    onValidate = {
                        bonCommandeVM.updateBonCommandeStatus(
                            status = ItemStatus.INSERTED.status,
                            bonCommandeM = selectedBonCommande.devCodeM,
                        )


                    },
                    onSyncRequest = { syncBonCommandeViewModel.syncBonCommande(selectedBonCommande = selectedBonCommande) }
                )
            }


            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier =
                Modifier
                    .fillMaxSize()
                    .padding(padding)
                    .padding(top = 12.dp),
            ) {

                if (bonCommandeVM.showCustomFilter) {
                    FilterContainer(
                        filterList = filterList,
                        listFilter = listFilter,
                        listOrder = listOrder,
                        orderList = context.resources.getStringArray(R.array.bn_commande_order),
                        onShowCustomFilterChange = {
                            bonCommandeVM.onShowCustomFilterChange(false)
                        },
                        onEvent = {
                            bonCommandeVM.onEvent(
                                event = it,
                                utilisateur = utilisateur,
                            )
                        },
                    )
                }

                if (isLoading) {
                    LottieAnim(lotti = R.raw.loading, size = 250.dp)
                } else {
                    if (bonCommandeListstate.lists.isNotEmpty()) {
                        BonTransfertList(
                            listState = listState,
                            isLoading = false,
                            isConnected = isConnected,
                            selectedBaseconfig = selectedBaseconfig,
                            exerciceCode = exerciceCode,
                            utilisateur = utilisateur,
                            clientList = clientList,
                            getProCaisseDataViewModel = getProCaisseDataViewModel,
                            hasPromo = hasPromo,
                            selectArtMobilityVM = selectArtMobilityVM,
                            //   selectedListLgBonCommande = bonCommandeVM.selectedListLgBonCommande,
                            bonCommandeViewModel = bonCommandeVM,
                            filteredList = bonCommandeListstate.lists,
                            selectedBonCommande = selectedBonCommandeWithClient,
                            onNavigate = { navigate(it) },
                            getPrice = {
                                selectArtMobilityVM.getPrice(it)
                            },
                            getClientName = { cltName, cltCode ->
                                getClientName(cltName = cltName, cltCode = cltCode)
                            },
                            onItemClick = { bonCommande ->
                            }
                        )
                    }
                    else if(isLoadingFromLocalDb) CircularProgressIndicator()
                    else {
                        LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
                    }
                }
            }
        }

}

@Composable
fun BonTransfertList(
    listState: LazyListState,
    isConnected: Boolean,
    selectedBaseconfig: BaseConfig,
    exerciceCode: String,
    utilisateur: Utilisateur,
    clientList: List<Client>,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    onNavigate: (destination: Any) -> Unit,
    getClientName: (cltName: String?, cltCode: String?) -> String,
    getPrice: (article: Article) -> String,
    hasPromo: Boolean,
    isLoading: Boolean,

    selectedBonCommande: BonCommandeWithClient,
    selectArtMobilityVM: SelectArticleCalculViewModel,
    bonCommandeViewModel: BonCommandeViewModel,
    filteredList: Map<BonCommandeWithClient, List<LigneBonCommandeWithArticle>>,
    onItemClick: (bonCommandeWithClient: BonCommandeWithClient) -> Unit,
) {


    val bonCommandeWithClient: MutableList<BonCommandeWithClient> = arrayListOf()
    val bonCommande: MutableList<BonCommande> = arrayListOf()
   // val ligneBonCommande: MutableList<LigneBonCommande> = arrayListOf()
  //  val ligneBonCommandeWithArticle: MutableList<LigneBonCommandeWithArticle> = arrayListOf()

    filteredList.forEach { (key, value) ->
        run {
            bonCommandeWithClient.add(key)
            bonCommande.add(key.bonCommande?: BonCommande())
           // ligneBonCommande.addAll(value.map { it.ligneBonCommande!! })
          //  ligneBonCommandeWithArticle.addAll(value)
        }
    }
    val isRefreshing = isLoading|| getProCaisseDataViewModel.ligneBonCommandeState.loading || getProCaisseDataViewModel.sessionCaisseState.loading

    PullToRefreshLazyColumn(
        items = bonCommandeWithClient,
        lazyListState = listState,
        isRefreshing = isRefreshing,
        pullToRefreshEnabled = !bonCommande.any { !it.isSync } && isConnected,
        onRefresh = {
            /**
             * Get getSessionCaissesByUser not  getBon Commande and lg bc to ensure no conflict
             * also applicable for bon retour and Ticket
             */
            getProCaisseDataViewModel.getSessionCaissesByUser(
                baseConfig = selectedBaseconfig,
                utilisateur = utilisateur,
                clientList = clientList,
                exerciceCode = exerciceCode
            )
        },
        key = { bonCommandeWithClient -> bonCommandeWithClient.bonCommande?.devCodeM + bonCommandeWithClient.bonCommande?.dEVNum },
        content = { bonCommandeWithClient ->
            val bonCommande = bonCommandeWithClient.bonCommande
            val client = bonCommandeWithClient.client
            val cLINomPren = client?.cLINomPren?: getClientName(
                bonCommande?.dEVClient,
                bonCommande?.dEVCodeClient,
            )

            ListItem(
                isSelected = selectedBonCommande == bonCommandeWithClient,
                firstText = bonCommande?.dEVNum?.ifEmpty { bonCommande.devCodeM }?: "N/A",// stringResource(id = R.string.order_number_field, bonCommande?.dEVNum?.ifEmpty { bonCommande.devCodeM }?: "N/A"),//todo see why dont show dEVNum  when using stringResource !!
                secondText = cLINomPren,
                thirdText = convertStringToPriceFormat((stringToDouble(bonCommande?.dEVMntTTC) - stringToDouble(bonCommande?.dEVRemise)).toString()),
                dateText =
                getDate(
                    ddm = bonCommande?.dEVDDm?.substringBefore("."),
                    date = bonCommande?.dEVDate?.substringBefore("."),
                ),
                isSync = bonCommande?.isSync == true,
                status = bonCommande?.status?: "",
                onResetDeletedClick = {
                    // bonCommandeViewModel.restDeletedVisite(bonEntree[index])
                },
                onItemClick = {
                    // onItemClick(bonCommandeWithClient)
                    bonCommandeViewModel.restBonCommande()
                    selectArtMobilityVM.resetSelectedMobilityArticles()
                    val bonCommandeWithLignes = filteredList.filter { it.key == bonCommandeWithClient }
                    bonCommandeViewModel.onSelectedBonCommandeChange(bonCommandeWithLignes)
                    selectArtMobilityVM.setControlQte(false)

                    val client = client?: Client(cLICode = bonCommande?.dEVCodeClient?: "")

                    bonCommandeViewModel.setSelectedArticleList(
                        lgBCWithArticleList = bonCommandeWithLignes.values.flatten(),
                        getPrice = {
                            selectArtMobilityVM.getPrice(it)
                        },
                        addOneItemToSelectedArticleMobilityList = {
                            selectArtMobilityVM.addOneItemToSelectedArticleMobilityList(it)
                        }
                    )



                    val destination =
                        if (bonCommande?.status == ItemStatus.WAITING.status) {
                            AddBonCommandeRoute(clientId = client.cLICode)
                        } else {
                            BonCommandeDetailRoute(clientId = client.cLICode)
                        }

                    onNavigate(destination)
                },
                onMoreClick = {
                    bonCommandeViewModel.restBonCommande()
                    selectArtMobilityVM.resetSelectedMobilityArticles()
                    bonCommandeViewModel.onSelectedBonCommandeChange(filteredList.filter { it.key == bonCommandeWithClient })
                    bonCommandeViewModel.onShowCustomModalBottomSheetChange(true)
                }
            )
        }
    )
}




