package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.twotone.DateRange
import androidx.compose.material.icons.twotone.PersonOutline
import androidx.compose.material.icons.twotone.Print
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothInfoDialogue
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.print.PrintFunctions
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.wifi.WifiPrintViewModel
import com.asmtunis.procaisseinventory.core.utils.StringUtils.getClientName
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.view_model.BonCommandeViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.TotalPriceView
import com.asmtunis.procaisseinventory.shared_ui_components.tables.TableTextUtils
import com.asmtunis.procaisseinventory.shared_ui_components.tables.five_column.FiveColumnTable
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.custom_cards.ItemDetail
import com.simapps.ui_kit.utils.getDate


@Composable
fun BonCommandeDetailPane(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    settingViewModel: SettingViewModel,
    bonCommandeViewModel: BonCommandeViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    mainViewModel: MainViewModel,
    selectArtMobilityVM: SelectArticleCalculViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    printViewModel: PrintViewModel,
    wifiPrintVM: WifiPrintViewModel,
    bluetoothVM: BluetoothViewModel,

    ) {
    val context = LocalContext.current
    val selectedBonCommandeWithClient = bonCommandeViewModel.selectedBonCommandeWithClient
    val bonCommande = selectedBonCommandeWithClient.bonCommande!!

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val selectedListLgBonCommandeWithArticle = bonCommandeViewModel.selectedListLgBonCommandeWithArticle
    val utilisateur = mainViewModel.utilisateur
    val listLigneBonCommande = bonCommandeViewModel.selectedListLgBonCommande

    val selectedArtList = selectArtMobilityVM.selectedArticleList
    val printParams = dataViewModel.printData

    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val tvaList = mainViewModel.tvaList

    val totWithDiscount = stringToDouble(bonCommande.dEVMntTTC!!) -  stringToDouble(bonCommande.dEVRemise)

    val showPriceWithDiscount = totWithDiscount != stringToDouble(bonCommande.dEVMntTTC)
    val firstTimeConnected = printViewModel.firstTimeConnected

    LaunchedEffect(key1 = firstTimeConnected){
        if(!firstTimeConnected) return@LaunchedEffect

        printViewModel.awaitPrint(
            context = context,
            toPrint = {
                printViewModel.printBonCommande(
                    context = context,
                    bonCommandeWithClient = selectedBonCommandeWithClient,
                    lgBonCommandeWithArticle = selectedListLgBonCommandeWithArticle,
                    utilisateur = utilisateur,
                    printParams = printParams
                )
            }
        )

        printViewModel.onFirstTimeConnectedChange(firstConnect = false)
    }


    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

        Scaffold(
            topBar = {
                AppBar(
                    onNavigationClick = { popBackStack() },
                    navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                    title = stringResource(id = R.string.order_number_field, bonCommande.dEVNum),
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    actions = {
                        IconButton(
                            onClick = {
                                PrintFunctions.print(
                                    context = context,
                                    toaster = toaster,
                                    printParams = printParams,
                                    navigate = { navigate(it) },
                                    printViewModel = printViewModel,
                                    bluetoothVM = bluetoothVM,
                                    toPrintBT = {
                                        printViewModel.printBonCommande(
                                            context = context,
                                            bonCommandeWithClient = selectedBonCommandeWithClient,
                                            lgBonCommandeWithArticle = selectedListLgBonCommandeWithArticle,
                                            utilisateur = utilisateur,
                                            printParams = printParams
                                        )
                                    },
                                    toPrintWifi = {
                                        wifiPrintVM.createBCPDFFile(
                                            context = context,
                                            bonCommandeWithClient = selectedBonCommandeWithClient,
                                            lgBonCommandeWithArticle = selectedListLgBonCommandeWithArticle,
                                            listener = {}
                                        )
                                    }
                                )
                            }
                        ) {
                            Icon(
                                imageVector = Icons.TwoTone.Print,
                                contentDescription = stringResource(id = R.string.icn_search_clear_content_description)
                            )
                        }
                    }
                )
            }
        ) { padding ->
            if(printViewModel.openPrintInfoDialogue) {
                BluetoothInfoDialogue(
                    printResult = printViewModel.printResult,
                    onOpenPrintInfoDialogueChange = {
                        printViewModel.onOpenPrintInfoDialogueChange(it)
                    }
                )
            }

            Column(
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding)
            ) {

                Spacer(modifier = Modifier.height(12.dp))
                ItemDetail(
                    modifier = Modifier.fillMaxWidth(0.85f),
                    title = stringResource(id = R.string.client_field_title),
                    dataText = getClientName(cltName = bonCommande.dEVClientName, cltCode = bonCommande.dEVCodeClient),
                    icon = Icons.TwoTone.PersonOutline,
                    onClick = {

                    }
                )
                Spacer(modifier = Modifier.height(9.dp))
                ItemDetail(
                    modifier = Modifier.fillMaxWidth(0.85f),
                    title = stringResource(id = R.string.date),
                    dataText = getDate(ddm = bonCommande.dEVDDm, date = bonCommande.dEVDate).substringBefore("."),
                    icon = Icons.TwoTone.DateRange,

                    )
                Spacer(modifier = Modifier.height(9.dp))
                FiveColumnTable(
                  selectedListArticle = selectedArtList,
                    onPress = { selectArtMobilityVM.setSelectedArticl(article = it.article, tvaList = tvaList) },
                    onTap = { mainViewModel.setAddNewProductDialogueVisibility(true) },
                    firstColumn = { item->
                        articleMapByBarCode[item.article.aRTCode]?.aRTDesignation?: context.resources.getString(R.string.article_introvable, " ("+ item.article.aRTCode+")")
                    },
                    secondColumn = { it.prixCaisse },
                    thirdColumn = { removeTrailingZeroInDouble(it.quantity) },
                    forthColumn = { it.lTMtTTC },
                    infoText = { TableTextUtils.infoText(selectedArticle = it) }
                )


                Spacer(modifier = Modifier.weight(1f))




                TotalPriceView(
                    totalPrice = bonCommande.dEVMntTTC?:"0",
                    totalPriceAfterDiscount = totWithDiscount.toString(),
                    showPriceWithDiscount = showPriceWithDiscount
                )

                Spacer(modifier = Modifier.height(18.dp))

            }


        }


}