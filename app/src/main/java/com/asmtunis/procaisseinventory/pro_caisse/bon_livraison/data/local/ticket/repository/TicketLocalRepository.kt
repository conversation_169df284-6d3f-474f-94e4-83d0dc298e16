package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ticket.repository

import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicketWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.domaine.ClientCA
import kotlinx.coroutines.flow.Flow

interface TicketLocalRepository {

    fun upsertAll(value: List<Ticket>)

    fun upsert(value: Ticket)

    fun notSynced(): Flow<List<TicketWithFactureAndPayments>?>

    fun ticketWithLinesAndPaymentByNumTicket(numTicket: String): Flow<TicketWithFactureAndPayments>

    fun deleteAll()

    fun deleteByCodeM(
        code: String,
        exercice: String,
    )

    fun updateSyncErrorMsg(
        tikNumTicketM: String,
        errorMsg: String,
    )

    fun updateBLNumber(
        tikNumTicket: Int,
        tikNumTicketM: String,
        tikDdm: String,
    )

    fun updateTicketNumber(
        tikNumBl: String,
        tikNumTicket: Int,
        tikNumTicketM: String,
        tikDdm: String,
    )

    fun getAll(): Flow<Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>>

    fun getNTopClientsBySCaisse(
        idSCaisse: String?,
        number: Int,
    ): Flow<List<ClientCA>>

    fun getCA(idSCaisse: String): Flow<Double>

    fun getTicketNumber(idSCaisse: String): Flow<String>

    fun getMntCredit(caisse: String): Flow<String?>

    fun getByClient(
        codeClient: String,
        sCIdSCaisse: String,
    ): Flow<Map<Ticket, List<LigneTicket>>>

    fun getAllFiltred(
        isAsc: Int,
       // filterBySessionCaisse: String,
        filterByTicketEtat: String,
        filterByTicketSource: String = "",
        sortBy: String,
    ): Flow<Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>>

    fun filterByNumBL(
        searchString: String,
        filterBySessionCaisse: String,
        filterByTicketEtat: String,
        filterByTicketSource: String = "",
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>>

    fun filterByTicketNum(
        searchString: String,
        filterBySessionCaisse: String,
        filterByTicketEtat: String,
        filterByTicketSource: String = "",
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>>

    fun filterByClient(
        searchString: String,
        filterBySessionCaisse: String,
        filterByTicketEtat: String,
        filterByTicketSource: String = "",
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>>
}
