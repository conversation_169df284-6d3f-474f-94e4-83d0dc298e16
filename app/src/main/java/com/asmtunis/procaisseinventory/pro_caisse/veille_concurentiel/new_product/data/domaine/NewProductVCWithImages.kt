package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class NewProductVCWithImages (
    @Embedded
    @SerialName("NewProductVC")
    var newProductVC: NewProductVC? = null,

    @Relation(
        parentColumn = "CodeVCLanPM",
        entityColumn = "Code_TypeVC"
    )
    @SerialName("ImagePieceJoint")
    var imageList: List<ImagePieceJoint>? = null,
)