package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.bon_retour.dao.BonRetourDAO
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.bon_retour.repository.BonRetourLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.bon_retour.repository.BonRetourLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.ligne_bon_retour.dao.LigneBonRetourDAO
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.ligne_bon_retour.repository.LigneBonRetourLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.ligne_bon_retour.repository.LigneBonRetourLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class BonRetourLocalModule {

    @Provides
    @Singleton
    fun provideBonRetourDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.bonRetourDAO()

    @Provides
    @Singleton
    @Named("BonRetour")
    fun provideBonRetourRepository(
        bonRetourDAO: BonRetourDAO
    ): BonRetourLocalRepository = BonRetourLocalRepositoryImpl(bonRetourDAO = bonRetourDAO)



    @Provides
    @Singleton
    fun provideLigneBonRetourDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.ligneBonRetourDAO()

    @Provides
    @Singleton
    @Named("BonRetourDetail")
    fun provideLigneBonRetourRepository(
        ligneBonRetourDAO: LigneBonRetourDAO
    ): LigneBonRetourLocalRepository = LigneBonRetourLocalRepositoryImpl(ligneBonRetourDAO = ligneBonRetourDAO)



}