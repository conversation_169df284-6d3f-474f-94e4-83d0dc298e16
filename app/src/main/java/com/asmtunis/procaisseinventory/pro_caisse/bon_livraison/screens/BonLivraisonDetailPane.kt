package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.twotone.DateRange
import androidx.compose.material.icons.twotone.Payment
import androidx.compose.material.icons.twotone.PersonOutline
import androidx.compose.material.icons.twotone.Print
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothInfoDialogue
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.print.PrintFunctions
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.wifi.WifiPrintViewModel
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertDoubleToDoubleFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicketWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.BonLivraisonViewModel
import com.asmtunis.procaisseinventory.pro_caisse.reglement.ReglementUtils
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.TotalPriceView
import com.asmtunis.procaisseinventory.shared_ui_components.tables.TableTextUtils
import com.asmtunis.procaisseinventory.shared_ui_components.tables.five_column.FiveColumnTable
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.custom_cards.ItemDetail


@Composable
fun BonLivraisonDetailPane(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    settingViewModel: SettingViewModel,
    bonLivraisonViewModel: BonLivraisonViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    mainViewModel: MainViewModel,
    selectArtMobilityVM : SelectArticleCalculViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    printViewModel: PrintViewModel,
    wifiPrintVM: WifiPrintViewModel,
    bluetoothVM: BluetoothViewModel,
) {

    val context = LocalContext.current
    val selectedBonLivraison = bonLivraisonViewModel.selectedBonLivraisonWithFactureAndPayments
    val bonLivraison = selectedBonLivraison.ticket?: Ticket()
    val reglement = selectedBonLivraison.reglement
    val lgBnLivraisonAndArticleList = bonLivraisonViewModel.selectedListLgBonLivraisonWithArticle
    val prefixList = mainViewModel.prefixList
    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val selectedArtList = selectArtMobilityVM.selectedArticleList



    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val printParams = dataViewModel.printData

    val reglementTYPE = ReglementUtils.getReglementType(
        reglementCaisse = reglement?: ReglementCaisse(),
        listTicket = listOf(bonLivraison),
        context = context
    )

    val uiWindowState = settingViewModel.uiWindowState
    val windowSize = uiWindowState.windowSize!!//?:  WindowSizeClass(WindowWidthSizeClass.Compact, WindowHeightSizeClass.Compact)


    val selectedArticleList = selectArtMobilityVM.selectedArticleList

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val showPriceWithDiscount = stringToDouble(bonLivraison.tIKMtRemise)!=0.0
   val utilisateur = mainViewModel.utilisateur
   val firstTimeConnected = printViewModel.firstTimeConnected

    LaunchedEffect(key1 = firstTimeConnected){
        if(!firstTimeConnected) return@LaunchedEffect
        printViewModel.awaitPrint(
            context = context,
            toPrint = {
                printViewModel.printTicket(
                    context = context,
                    ticketWithFactureAndPayments = selectedBonLivraison,
                    listLigneTicket = lgBnLivraisonAndArticleList,
                    utilisateur = utilisateur,
                    printParams = printParams,
                    prefixList = prefixList
                )
            }
        )

        printViewModel.onFirstTimeConnectedChange(firstConnect = false)
    }
//
//    LaunchedEffect(key1 = bonLivraison.tIKNumTicket){
//        selectArtMobilityVM.resetSelectedMobilityArticles()
//
//            for (i in lgBnLivraisonAndArticleList.indices) {
//                val article = lgBnLivraisonAndArticleList[i].article?: Article(aRTCodeBar = lgBnLivraisonAndArticleList[i].ligneTicket!!.lTCodArt)
//
//                val lgBonLiv = lgBnLivraisonAndArticleList[i].ligneTicket
//                val selectedArticle = SelectedArticle(
//                    article = article,
//                    quantity = lgBonLiv!!.lTQte,
//                    prixCaisse = lgBonLiv.lTPrixEncaisse,
//                    discount = lgBonLiv.lTTauxRemise,
//                    mntDiscount = lgBonLiv.lTRemise,
//                    lTMtTTC = lgBonLiv.lTMtTTC,
//                    lTMtBrutHT = lgBonLiv.lTMtHT,
//                    prixVente = lgBonLiv.lTPrixVente,
//                    tva = Tva(),
//                    mntTva = ""
//                )
//                selectArtMobilityVM.setConsultationSelectedArticleMobilityList(selectedArticle)
//
//
//            }
//    }

        Scaffold(
            topBar = {
                AppBar(
                    onNavigationClick = { popBackStack() },
                    navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                    title = stringResource(R.string.ticket_number_field, bonLivraison.tIKNumTicket) + if(bonLivraison.tIKNumeroBL!=null) " (${stringResource(R.string.fact, bonLivraison.tIKNumeroBL)})" else "",
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    actions = {
                        IconButton(
                            onClick = {
                                PrintFunctions.print(
                                    context = context,
                                    toaster = toaster,
                                    printParams = printParams,
                                    navigate = { navigate(it) },
                                    printViewModel = printViewModel,
                                    bluetoothVM = bluetoothVM,
                                    toPrintBT = {
                                        printViewModel.printTicket(
                                            context = context,
                                            ticketWithFactureAndPayments = selectedBonLivraison,
                                            listLigneTicket = lgBnLivraisonAndArticleList,
                                            utilisateur = utilisateur,
                                            printParams = printParams,
                                            prefixList = prefixList
                                        )
                                    },
                                    toPrintWifi = {
                                        wifiPrintVM.createTicketPDFFile(
                                            context = context,
                                            listLigneTicketWithArticle = lgBnLivraisonAndArticleList,
                                            ticketWithFactureAndPayments = selectedBonLivraison,
                                            listener = {}
                                        )
                                    }
                                )
                        }
                        ) {
                            Icon(
                                imageVector = Icons.TwoTone.Print,
                                contentDescription = stringResource(id = R.string.icn_search_clear_content_description)
                            )
                        }
                    }
                )
            }
        ) { padding ->
            if(printViewModel.openPrintInfoDialogue) {
                BluetoothInfoDialogue(
                    printResult = printViewModel.printResult,
                    onOpenPrintInfoDialogueChange = {
                        printViewModel.onOpenPrintInfoDialogueChange(it)
                    }
                )
            }
            BLColumnView(
                padding = padding,
                reglementTYPE = reglementTYPE,
                selectedBonLivraison = selectedBonLivraison,
                selectedArticleList = selectedArticleList,
                articleMapByBarCode = articleMapByBarCode,
                lgBnLivraisonAndArticleList = lgBnLivraisonAndArticleList,
                showPriceWithDiscount = showPriceWithDiscount
            )
//            when (windowSize.widthSizeClass) {
//                WindowWidthSizeClass.Compact -> {
//
//                    BLColumnView(
//                        padding = padding,
//                        reglementTYPE = reglementTYPE,
//                        selectedBonLivraison = selectedBonLivraison,
//                        selectedArticleList = selectedArticleList,
//                    articleList = articleList,
//                    lgBnLivraisonAndArticleList = lgBnLivraisonAndArticleList,
//                    showPriceWithDiscount = showPriceWithDiscount
//                    )
//
//
//
//
//                }
//
//                WindowWidthSizeClass.Expanded,
//                WindowWidthSizeClass.Medium -> {
//                    BLRowView(
//                        padding = padding,
//                        reglementTYPE = reglementTYPE,
//                        selectedBonLivraison = selectedBonLivraison,
//                        selectedArticleList = selectedArticleList,
//                        articleList = articleList,
//                        lgBnLivraisonAndArticleList = lgBnLivraisonAndArticleList,
//                        showPriceWithDiscount = showPriceWithDiscount
//                    )
//
//                }
//                else -> {
//
//                }
//            }



        }


}

@Composable
fun BLColumnView(
    padding: PaddingValues,
    reglementTYPE: String,
    selectedBonLivraison: TicketWithFactureAndPayments,
    selectedArticleList: List<SelectedArticle>,
    articleMapByBarCode: Map<String, Article>,
    lgBnLivraisonAndArticleList: List<LigneTicketWithArticle>,
    showPriceWithDiscount: Boolean
) {
    val context = LocalContext.current

    val scrollState = rememberScrollState()
    val reglement = selectedBonLivraison.reglement
    val bonLivraison = selectedBonLivraison.ticket?: Ticket()

    Column(
        verticalArrangement = Arrangement.Top,
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
            .padding(padding)
    ) {
        Spacer(modifier = Modifier.height(12.dp))

        if(reglementTYPE == stringResource(id = R.string.paiement_Partiel)) {
            ItemDetail(
                modifier = Modifier.fillMaxWidth(0.85f),
                title = stringResource(id = R.string.paiement_Partiel),
                dataText = StringUtils.convertStringToPriceFormat(reglement?.rEGCMontant.toString()),
                icon = Icons.TwoTone.Payment,
                tint = MaterialTheme.colorScheme.error
            )
            Spacer(modifier = Modifier.height(12.dp))
        }

        ItemDetail(
            modifier = Modifier.fillMaxWidth(0.95f),
            title = stringResource(id = R.string.client_field_title),
            dataText = bonLivraison.tIKNomClient,
            icon = Icons.TwoTone.PersonOutline
        )
        Spacer(modifier = Modifier.height(9.dp))

        ItemDetail(
            modifier = Modifier.fillMaxWidth(0.95f),
            title = stringResource(id = R.string.date),
            dataText = (if(bonLivraison.syncErrorMsg.isNotEmpty()) bonLivraison.syncErrorMsg
            else if(bonLivraison.tIKDDmFormatted != "empty") bonLivraison.tIKDDmFormatted
            else bonLivraison.tIKDateHeureTicket).substringBefore("."),
            icon = Icons.TwoTone.DateRange,

            )
        Spacer(modifier = Modifier.height(9.dp))

        FiveColumnTable(
            selectedListArticle = selectedArticleList,
            onPress = {
                // selectArtMobilityVM.setSelectedArticl(it.article)
            },
            onTap = {
                // mainViewModel.setAddNewProductDialogueVisibility(true)
            },
            firstColumn = { item->
                articleMapByBarCode[item.article.aRTCode]?.aRTDesignation?:
                context.resources.getString(R.string.article_introvable, " ("+ item.article.aRTCode+")")
            },
            secondColumn = { removeTrailingZeroInDouble(it.prixCaisse) },
            thirdColumn = {
                removeTrailingZeroInDouble(it.quantity)
            },
            forthColumn = { convertDoubleToDoubleFormat(stringToDouble(it.lTMtTTC)) },
            infoText = { TableTextUtils.infoText(selectedArticle = it) }
        )

        Spacer(modifier = Modifier.weight(1f))

        TotalPriceView(
            //todo add mn discount to bonLivraison.tIKMtTTC for totalPrice
            totalPrice = (lgBnLivraisonAndArticleList.sumOf { stringToDouble(it.ligneTicket?.lTPrixVente) * stringToDouble(it.ligneTicket?.lTQte) } + stringToDouble(bonLivraison.tIKTimbre) + (bonLivraison.mntRevImp?: 0.0)).toString(),
            totalPriceAfterDiscount = bonLivraison.tIKMtTTC,
            showPriceWithDiscount = showPriceWithDiscount
        )

        Spacer(modifier = Modifier.height(18.dp))


    }
}



@Composable
fun BLRowView(
    padding: PaddingValues,
    reglementTYPE: String,
    selectedBonLivraison: TicketWithFactureAndPayments,
    selectedArticleList: List<SelectedArticle>,
    articleList: List<Article>,
    lgBnLivraisonAndArticleList: List<LigneTicketWithArticle>,
    showPriceWithDiscount: Boolean
) {
    val context = LocalContext.current

    val scrollState = rememberScrollState()
    val reglement = selectedBonLivraison.reglement
    val bonLivraison = selectedBonLivraison.ticket?: Ticket()

    Row(
        horizontalArrangement = Arrangement.Center,
    verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .fillMaxSize()
          //  .verticalScroll(scrollState)
            .padding(padding)
    ) {
        Spacer(modifier = Modifier.height(12.dp))

        if(reglementTYPE == stringResource(id = R.string.paiement_Partiel)) {
            ItemDetail(
                modifier = Modifier.fillMaxWidth(0.85f),
                title = stringResource(id = R.string.paiement_Partiel),
                dataText = StringUtils.convertStringToPriceFormat(reglement?.rEGCMontant.toString()),
                icon = Icons.TwoTone.Payment,
                tint = MaterialTheme.colorScheme.error
            )
            Spacer(modifier = Modifier.height(12.dp))
        }

        ItemDetail(
            modifier = Modifier.fillMaxWidth(0.85f),
            title = stringResource(id = R.string.client_field_title),
            dataText = bonLivraison.tIKNomClient,
            icon = Icons.TwoTone.PersonOutline
        )
        Spacer(modifier = Modifier.height(9.dp))

        ItemDetail(
            modifier = Modifier.fillMaxWidth(0.85f),
            title = stringResource(id = R.string.date),
            dataText = (if(bonLivraison.syncErrorMsg.isNotEmpty()) bonLivraison.syncErrorMsg
            else if(bonLivraison.tIKDDmFormatted != "empty") bonLivraison.tIKDDmFormatted
            else bonLivraison.tIKDateHeureTicket).substringBefore("."),
            icon = Icons.TwoTone.DateRange,

            )
        Spacer(modifier = Modifier.height(9.dp))

        FiveColumnTable(
            selectedListArticle = selectedArticleList,
            onPress = {
                // selectArtMobilityVM.setSelectedArticl(it.article)
            },

            onTap = {
                // mainViewModel.setAddNewProductDialogueVisibility(true)
            },
            firstColumn = { item->
                articleList.firstOrNull { it.aRTCodeBar == item.article.aRTCodeBar }?.aRTDesignation?:
                context.resources.getString(R.string.article_introvable, " ("+ item.article.aRTCodeBar+")")
            },
            secondColumn = {
                removeTrailingZeroInDouble(it.prixCaisse)
            },
            thirdColumn = {
                removeTrailingZeroInDouble(it.quantity)
            },
            forthColumn = {
                //  removeTrailingZeroInDouble(it.lTMtTTC)
                convertDoubleToDoubleFormat(stringToDouble(it.lTMtTTC))

            },
            infoText = {
                TableTextUtils.infoText(selectedArticle = it)
            }
        )
        Spacer(modifier = Modifier.weight(1f))

        TotalPriceView(
            totalPrice = (lgBnLivraisonAndArticleList.sumOf { stringToDouble(it.ligneTicket?.lTPrixVente) * stringToDouble(it.ligneTicket?.lTQte) } + stringToDouble(bonLivraison.tIKTimbre)).toString(),
            totalPriceAfterDiscount = bonLivraison.tIKMtTTC,
            showPriceWithDiscount = showPriceWithDiscount
        )
        Spacer(modifier = Modifier.height(18.dp))


    }
}
