package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.famille.dao.FamilleDnDAO
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.famille.repository.FamilleDnLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.famille.repository.FamilleDnLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.superficie.dao.SuperficieDAO
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.superficie.repository.SuperficieLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.superficie.repository.SuperficieLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typepointvente.dao.TypePointVenteDAO
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typepointvente.repository.TypePointVenteLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typepointvente.repository.TypePointVenteLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typeservice.dao.TypeServicesDAO
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typeservice.repository.TypeServicesLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typeservice.repository.TypeServicesLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.visite.dao.LigneVisiteDAO
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.visite.dao.VisiteDAO
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.visite.repository.VisiteLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.visite.repository.VisiteLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class DistributionNumeriqueLocalModule {

    @Provides
    @Singleton
    fun provideTypeServiceDao(
        typeServiceProCaisseDataBase: ProCaisseDataBase
    ) = typeServiceProCaisseDataBase.typeServicesDAO()

    @Provides
    @Singleton
    @Named("TypeService")
    fun provideTypeServiceRepository(
        typeServicesDAO: TypeServicesDAO
    ): TypeServicesLocalRepository = TypeServicesLocalRepositoryImpl(typeServicesDAO = typeServicesDAO)






    @Provides
    @Singleton
    fun provideSuperficieDao(
        superficieProCaisseDataBase: ProCaisseDataBase
    ) = superficieProCaisseDataBase.superficieDAO()

    @Provides
    @Singleton
    @Named("Superficie")
    fun provideSuperficieRepository(
        superficieDAO: SuperficieDAO
    ): SuperficieLocalRepository = SuperficieLocalRepositoryImpl(superficieDAO = superficieDAO)




    @Provides
    @Singleton
    fun provideTypePointVenteDao(
        typePointVenteProCaisseDataBase: ProCaisseDataBase
    ) = typePointVenteProCaisseDataBase.TypePointVenteDAO()

    @Provides
    @Singleton
    @Named("TypePointVente")
    fun provideTypePointVenteRepository(
        typePointVenteDAO: TypePointVenteDAO
    ): TypePointVenteLocalRepository = TypePointVenteLocalRepositoryImpl(typePointVenteDAO = typePointVenteDAO)


    @Provides
    @Singleton
    fun provideFamilleeDao(
        familleProCaisseDataBase: ProCaisseDataBase
    ) = familleProCaisseDataBase.familleDnDAO()

    @Provides
    @Singleton
    @Named("FamilleDn")
    fun provideFamilleRepository(
        familleDnDAO: FamilleDnDAO
    ): FamilleDnLocalRepository = FamilleDnLocalRepositoryImpl(familleDnDAO = familleDnDAO)





    @Provides
    @Singleton
    fun provideVisitesDao(
        visitesProCaisseDataBase: ProCaisseDataBase
    ) = visitesProCaisseDataBase.VisiteDAO()

    @Provides
    @Singleton
    fun provideLigneVisitesDao(
        lignesVisitesProCaisseDataBase: ProCaisseDataBase
    ) = lignesVisitesProCaisseDataBase.LigneVisiteDAO()

    @Provides
    @Singleton
    @Named("Visites")
    fun provideVisitesRepository(
        visitesDAO: VisiteDAO,
        lignesVisitesDAO : LigneVisiteDAO
    ): VisiteLocalRepository = VisiteLocalRepositoryImpl(visiteDAO = visitesDAO,
        ligneVisiteDAO = lignesVisitesDAO)

}