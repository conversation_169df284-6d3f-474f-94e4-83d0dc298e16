package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.famille.repository


import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.FamilleDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.famille.dao.FamilleDnDAO
import kotlinx.coroutines.flow.Flow



class FamilleDnLocalRepositoryImpl(
    private val familleDnDAO: FamilleDnDAO
) : FamilleDnLocalRepository {
    override fun upsert(value: FamilleDn) = familleDnDAO.insert(value)

    override fun upsertAll(value: List<FamilleDn>) = familleDnDAO.insertAll(value)


    override fun deleteAll() = familleDnDAO.deleteAll()

    override fun getAll(): Flow<List<FamilleDn>> = familleDnDAO.all

}