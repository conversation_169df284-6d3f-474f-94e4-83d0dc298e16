package com.asmtunis.procaisseinventory.pro_caisse

import NavDrawer
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import kotlinx.coroutines.launch

@Composable
fun ArchiveScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    printViewModel: PrintViewModel,
    mainViewModel: MainViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,
    dataViewModel: DataViewModel,
    settingViewModel: SettingViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,
) {
    val uiWindowState = settingViewModel.uiWindowState

    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)

    val scope = rememberCoroutineScope()

    val selectedBaseconfig: BaseConfig =  dataViewModel.selectedBaseConfig


    NavDrawer(
       navigate = { navigate(it) },
       drawer =  drawer,
        navDrawerViewmodel =  navigationDrawerViewModel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        mainViewModel = mainViewModel,
        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingViewModel
    ) {
        Scaffold(
            topBar = {
                AppBar(
                    showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                    onNavigationClick = { scope.launch { drawer.open() } },
                    title = stringResource(id = navigationDrawerViewModel.proCaisseSelectedMenu.title),
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected
                )
            },
            //    containerColor = colorResource(id = R.color.black),
            floatingActionButton = {
                FloatingActionButton(onClick = navigationDrawerViewModel::onHeartCheckedChange) {
                    Icon(
                        imageVector = if (navigationDrawerViewModel.heartChecked) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                        contentDescription = stringResource(id = R.string.cd_favorite_button)
                    )
                }
            }
        ) { padding ->
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding)
            ) {
                Text(text = stringResource(id = navigationDrawerViewModel.proCaisseSelectedMenu.title), color = Color.Red)
            }
        }
    }
}


