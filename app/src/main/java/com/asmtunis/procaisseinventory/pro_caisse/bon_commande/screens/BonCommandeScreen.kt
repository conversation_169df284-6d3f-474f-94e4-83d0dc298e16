@file:OptIn(ExperimentalMaterial3AdaptiveApi::class)

package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.screens

import NavDrawer
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.adaptive.ExperimentalMaterial3AdaptiveApi
import androidx.compose.material3.adaptive.layout.AnimatedPane
import androidx.compose.material3.adaptive.layout.ListDetailPaneScaffoldRole
import androidx.compose.material3.adaptive.navigation.NavigableListDetailPaneScaffold
import androidx.compose.material3.adaptive.navigation.rememberListDetailPaneScaffoldNavigator
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AddBonCommandeRoute
import com.asmtunis.procaisseinventory.core.navigation.AddBonLivraisonRoute
import com.asmtunis.procaisseinventory.core.navigation.BluetoothConnectRoute
import com.asmtunis.procaisseinventory.core.navigation.BonCommandeDetailRoute
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.wifi.WifiPrintViewModel
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.view_model.BonCommandeViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import kotlinx.coroutines.launch


@Composable
    fun BonCommandeScreen(
    navigate: (route: Any) -> Unit,
    navDrawerViewmodel: NavigationDrawerViewModel,
    clientId: String,
    bonCommandeVM: BonCommandeViewModel,
    mainViewModel: MainViewModel,
    dataViewModel: DataViewModel,
    selectArtMobilityVM: SelectArticleCalculViewModel,
    networkViewModel: NetworkViewModel,
    printViewModel: PrintViewModel,
    bluetoothVM: BluetoothViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    wifiPrintVM: WifiPrintViewModel,
    settingVM: SettingViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    syncInventoryViewModel: SyncInventoryViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    ) {
    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)
    val uiWindowState = settingVM.uiWindowState
    val scope = rememberCoroutineScope()
    val navigator = rememberListDetailPaneScaffoldNavigator<Any>()
    NavDrawer(
        navigate = { navigate(it) },
        drawer = drawer,
        mainViewModel = mainViewModel,
        navDrawerViewmodel = navDrawerViewmodel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingVM
    ) {

    NavigableListDetailPaneScaffold(
        // modifier = Modifier.fillMaxSize().padding(padding),
        navigator = navigator,
        listPane = {
            BonCommandeListPane(
                drawer = drawer,
                navigate = {
                    //navigate(it)


                    when (it) {
                        is AddBonCommandeRoute -> {
                            navigate(AddBonCommandeRoute(it.clientId))
                        }
                        is AddBonLivraisonRoute -> {
                            navigate(AddBonLivraisonRoute(it.clientId))
                        }
                        is BonCommandeDetailRoute -> {
                            scope.launch {
                                navigator.navigateTo(pane = ListDetailPaneScaffoldRole.Detail)
                            }

                           /* navigator.navigateTo(
                                pane = ListDetailPaneScaffoldRole.Detail,
                                content = it.clientId
                            )*/
                        }
                        is BluetoothConnectRoute-> {
                            navigate(BluetoothConnectRoute)
                        }
                        else -> {
                            // Handle other cases or raise an error if needed
                            println("Unknown argument type")
                        }
                    }
                },
                clientId = clientId,
                navDrawerViewmodel = navDrawerViewmodel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                bonCommandeVM = bonCommandeVM,
                mainViewModel = mainViewModel,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                selectArtMobilityVM = selectArtMobilityVM,
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                wifiPrintVM = wifiPrintVM,
                settingVM = settingVM,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                        syncProcaisseViewModels = syncProcaisseViewModels
            )

        },
        detailPane = {

            //     val content = navigator.currentDestination?.content as Client?



             /*      if(uiWindowState.navigationType != ReplyNavigationType.PERMANENT_NAVIGATION_DRAWER) {
                       bonCommandeVM.restBonCommande()
                   }
*/

            AnimatedPane {
                if (bonCommandeVM.selectedListLgBonCommande.isEmpty())
                    LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
                else
                    BonCommandeDetailPane(
                        navigate = { navigate(it) },
                        popBackStack = {
                            // popBackStack()
                            scope.launch {
                                navigator.navigateBack()
                            }
                        },
                        navigationDrawerViewModel = navDrawerViewmodel,
                        bonCommandeViewModel = bonCommandeVM,
                        mainViewModel = mainViewModel,
                        dataViewModel = dataViewModel,
                        networkViewModel = networkViewModel,
                        selectArtMobilityVM = selectArtMobilityVM,
                        printViewModel = printViewModel,
                        wifiPrintVM = wifiPrintVM,
                        bluetoothVM = bluetoothVM,
                        settingViewModel = settingVM
                    )
            }
        },
        /*  extraPane = {

                }*/
    )

}
}