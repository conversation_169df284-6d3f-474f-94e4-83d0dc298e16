
import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.DrawerState
import androidx.compose.material3.ModalNavigationDrawer
import androidx.compose.material3.PermanentNavigationDrawer
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.nav_components.nav_drawer.ui.DrawerContent
import com.asmtunis.procaisseinventory.nav_components.nav_rail.ReplyNavigationRail
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import kotlinx.coroutines.launch

@Composable
fun NavDrawer(
    navigate: (route: Any) -> Unit,
    drawer: DrawerState,
    mainViewModel: MainViewModel,
    navDrawerViewmodel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,
    dataViewModel: DataViewModel,
    syncInventoryViewModel: SyncInventoryViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    gesturesEnabled: Boolean = true,
    settingViewModel: SettingViewModel,
    content: @Composable () -> Unit
) {
    val menus =
        if (navDrawerViewmodel.isProInventory) navDrawerViewmodel.proInventoryListMenu
        else navDrawerViewmodel.proCaisseListMenu

    val uiWindowState = settingViewModel.uiWindowState

   val scope = rememberCoroutineScope()

     if(uiWindowState.navigationType == ReplyNavigationType.PERMANENT_NAVIGATION_DRAWER) {
         Log.d("rffccxxddd","1" )
        PermanentNavigationDrawer(
            drawerContent = {
                DrawerContent(
                    navigateTo = { navigate(it) },
                    onMenuClick = { menu ->
                        //  scope.launch { drawer.close() }
                        navDrawerViewmodel.onSelectedMenuChange(menu)
                        //  barCodeViewModel.onBarCodeInfo(BareCode())
                        navigate(menu.route)
                    },
                    menus = menus,
                    networkViewModel = networkViewModel,
                    navDrawerVM = navDrawerViewmodel,
                    dataViewModel = dataViewModel,
                    mainViewModel = mainViewModel,
                    getProCaisseDataViewModel = getProCaisseDataViewModel,
                    getProInventoryDataViewModel = getProInventoryDataViewModel,
                    getSharedDataViewModel = getSharedDataViewModel,

                    syncInventoryViewModel = syncInventoryViewModel,
                    syncSharedViewModels = syncSharedViewModels,
                    syncProcaisseViewModels = syncProcaisseViewModels,
                    settingViewModel = settingViewModel
                )
            }) {

            content()
        }
         }
         else {
         Log.d("rffccxxddd","2" )
             ModalNavigationDrawer(
                 modifier = Modifier,
                 drawerState = drawer,
                 gesturesEnabled = gesturesEnabled,
                 drawerContent = {
                     DrawerContent(

                         navigateTo = { navigate(it) },
                         onMenuClick = { menu ->
                             //  scope.launch { drawer.close() }
                             navDrawerViewmodel.onSelectedMenuChange(menu)
                             //  barCodeViewModel.onBarCodeInfo(BareCode())
                             navigate(menu.route)
                         },
                         menus = menus,
                         mainViewModel = mainViewModel,
                         networkViewModel = networkViewModel,
                         navDrawerVM = navDrawerViewmodel,
                         dataViewModel = dataViewModel,
                         syncInventoryViewModel = syncInventoryViewModel,
                         syncSharedViewModels = syncSharedViewModels,
                         syncProcaisseViewModels = syncProcaisseViewModels,
                         getProCaisseDataViewModel = getProCaisseDataViewModel,
                         getProInventoryDataViewModel = getProInventoryDataViewModel,
                         getSharedDataViewModel = getSharedDataViewModel,
                         settingViewModel = settingViewModel
                     )
                 }
             ) {
                 Row(modifier = Modifier.fillMaxSize()) {
                     AnimatedVisibility(visible = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_RAIL) {
                         ReplyNavigationRail(
                             navDrawerVM = navDrawerViewmodel,
                             mainViewModel = mainViewModel,
                             getSharedDataViewModel = getSharedDataViewModel,
                             menus = menus,
                             onMenuClick = { menu ->
                                 //  scope.launch { drawer.close() }
                                 navDrawerViewmodel.onSelectedMenuChange(menu)
                                 //  barCodeViewModel.onBarCodeInfo(BareCode())
                                 navigate(menu.route)
                             },
                             getProCaisseDataViewModel = getProCaisseDataViewModel,
                             getProInventoryDataViewModel = getProInventoryDataViewModel,
                             navigationContentPosition = uiWindowState.navigationContentPosition,
                             onDrawerClicked = {
                                 scope.launch { drawer.open() }
                             },
                         )
                     }
                     content()
                 }
             }
   }
}
