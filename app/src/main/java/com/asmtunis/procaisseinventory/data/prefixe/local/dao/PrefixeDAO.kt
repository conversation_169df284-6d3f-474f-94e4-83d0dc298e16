package com.asmtunis.procaisseinventory.data.prefixe.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.PREFIX_TABLE
import com.asmtunis.procaisseinventory.data.prefixe.domaine.Prefixe
import kotlinx.coroutines.flow.Flow

@Dao
interface PrefixeDAO {
    @get:Query("SELECT * FROM $PREFIX_TABLE")
    val all: Flow<List<Prefixe>>

    @Query("SELECT * FROM $PREFIX_TABLE WHERE lower(PRE_Id_table) = lower(:code) ")
    fun getOneById(code: String?): Flow<Prefixe>

    @Query("SELECT COUNT(*) FROM $PREFIX_TABLE")
    fun count(): Int

    @get:Query("SELECT * FROM $PREFIX_TABLE LIMIT 1")
    val one: Prefixe?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: Prefixe)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<Prefixe>)

    @Query("DELETE FROM $PREFIX_TABLE")
    fun deleteAll()
}
