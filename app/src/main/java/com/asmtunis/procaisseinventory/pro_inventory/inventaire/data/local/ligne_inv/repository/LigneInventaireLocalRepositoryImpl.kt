package com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.ligne_inv.repository

import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.ligne_inv.dao.LigneInventaireDAO
import kotlinx.coroutines.flow.Flow


class LigneInventaireLocalRepositoryImpl(
        private val ligneInventaireDAO: LigneInventaireDAO
    ) : LigneInventaireLocalRepository {
    override fun setToInserted(codeM: String) = ligneInventaireDAO.setToInserted(codeM)

    override fun upsertAll(value: List<LigneInventaire>) =
        ligneInventaireDAO.insertAll(value)

    override fun upsert(value: LigneInventaire)  =
        ligneInventaireDAO.insert(value)

    override fun updateStatus(code: String, codeLocal : String, etat: String) =
        ligneInventaireDAO.updateStatus(code = code, codeLocal = codeLocal, etat = etat)

    override fun getByCode(code: String): Flow<List<LigneInventaire>> =
        ligneInventaireDAO.getByCode(code)

    override fun deleteAll()  =
        ligneInventaireDAO.deleteAll()

    override fun deleteList(ligneInventaireList: List<LigneInventaire>) =
        ligneInventaireDAO.deleteList(ligneInventaireList = ligneInventaireList)

    override fun deleteByCodeAndCodeArticle(num: String, codeArticle: String)  =
        ligneInventaireDAO.deleteByCodeAndCodeArticle(num, codeArticle)

    override fun getAll(): Flow<List<LigneInventaire>>  =
        ligneInventaireDAO.all

}