package com.asmtunis.procaisseinventory.pro_inventory.achat.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Print
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Scaffold
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothInfoDialogue
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.print.PrintFunctions
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToDoubleFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_inventory.achat.AchatViewModel
import com.asmtunis.procaisseinventory.pro_inventory.text_validation.InventoryFormEvent
import com.asmtunis.procaisseinventory.pro_inventory.text_validation.InventoryTextValidationViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.tables.TableHeader
import com.asmtunis.procaisseinventory.shared_ui_components.tables.TableTextUtils
import com.asmtunis.procaisseinventory.shared_ui_components.tables.five_column.FiveColumnTable
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.date_time_picker.DatePickerView
import com.simapps.ui_kit.date_time_picker.TimePickerView
import com.simapps.ui_kit.edit_text.EditTextField
import kotlinx.coroutines.launch
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AchatDetailPane(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    settingViewModel: SettingViewModel,
    achatViewModel: AchatViewModel,
    printViewModel: PrintViewModel,
    bluetoothVM: BluetoothViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    mainViewModel: MainViewModel,
    selectArtCalculVM: SelectArticleCalculViewModel,
    inventoryTextValidationViewModel: InventoryTextValidationViewModel = hiltViewModel(),
    navigationDrawerViewModel: NavigationDrawerViewModel,
) {

    val context = LocalContext.current
    val scrollState = rememberScrollState()
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val scope = rememberCoroutineScope()

    val fiterValue = achatViewModel.fiterValue
    val showBottomSheet = achatViewModel.showBottomSheet

    val stateAddNew = inventoryTextValidationViewModel.stateAddNew
    val selectedBonEntree = achatViewModel.selectedBonEntree
    val selectedListLgBonEntree = achatViewModel.selectedListLgBonEntree

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val printParams = dataViewModel.printData
    val utilisateur = mainViewModel.utilisateur
    val stationList = mainViewModel.stationList
    val fournisseurList = mainViewModel.fournisseurList

    val selectedArticle = selectArtCalculVM.selectedArticle

    val selectedArtList = selectArtCalculVM.selectedArticleList

    val articleMapByBarCode = mainViewModel.articleMapByBarCode

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)




    LaunchedEffect(key1 = Unit) {
        if(printViewModel.proccedPrinting && printViewModel.deviceAddress.isNotEmpty()) {

            PrintFunctions.print(
                context = context,
                toaster = toaster,
                navigate = { navigate(it) },
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                printParams = printParams,
                toPrintBT = {
                    printViewModel.printBonAchat(
                        context = context,
                        lgBonEntree = selectedListLgBonEntree,
                        bonEntree = selectedBonEntree,
                        articleMapByBarCode = articleMapByBarCode,
                        printParams = printParams,
                        station = stationList.first { it.sTATCode == selectedBonEntree.bONENTStationEntree },
                        fournisseur = fournisseurList.first { it.fRSCodef == selectedBonEntree.bONENTCodeFrs }
                    )
                }
            )
        }



        inventoryTextValidationViewModel.onAddNewEvent(InventoryFormEvent.selectedListArticleChanged(selectedArtList))




    }

    LaunchedEffect(key1 = selectedArticle) {
        inventoryTextValidationViewModel.onAddNewEvent(
            InventoryFormEvent.selectedArticleChanged(selectedArticle.article)
        )
    }



    LaunchedEffect(key1 = Unit, key2 = selectedArtList) {

        inventoryTextValidationViewModel.onAddNewEvent(InventoryFormEvent.typePieceChanged(selectedBonEntree.bONENTTypePiece?:""))
        inventoryTextValidationViewModel.onAddNewEvent(InventoryFormEvent.numPieceChanged(selectedBonEntree.bONENTNumPiece?:""))


        inventoryTextValidationViewModel.onAddNewEvent(
            InventoryFormEvent.fournisseurChanged(fournisseurList.firstOrNull { it.fRSCodef == selectedBonEntree.bONENTCodeFrs }?: com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.domaine.Fournisseur())
        )
        inventoryTextValidationViewModel.onAddNewEvent(
            InventoryFormEvent.stationChanged(stationList.firstOrNull { it.sTATCode == selectedBonEntree.bONENTStationEntree }?: Station())
        )


    }





    if (mainViewModel.showDatePicker) {
        DatePickerView(
            setDateVisibility = {
                mainViewModel.onShowDatePickerChange(it)
            },
            onSelectedDateChange = {
                mainViewModel.onSelectedDateChange(it)
                mainViewModel.onShowTimePickerChange(true)
            },
            confirmText = stringResource(R.string.confirm),
            cancelText = stringResource(R.string.cancel)
        )
    }

    if (mainViewModel.showTimePicker)
        TimePickerView(
            setTimeVisibility = {
                mainViewModel.onShowTimePickerChange(it)
            },
            onSelectedTimeChange = {
                mainViewModel.onSelectedTimeChange(it)
            },
            confirmText = stringResource(R.string.confirm),
            cancelText = stringResource(R.string.cancel)
        )



    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = {
                    popBackStack()
                },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = selectedBonEntree.bONENTNum,
                actions = {

                    IconButton(
                        onClick = {
                            PrintFunctions.print(
                                context = context,
                                toaster = toaster,
                                navigate = { navigate(it) },
                                printViewModel = printViewModel,
                                bluetoothVM = bluetoothVM,
                                printParams = printParams,
                                toPrintBT = {
                                    printViewModel.printBonAchat(
                                        context = context,
                                        lgBonEntree = selectedListLgBonEntree,
                                        bonEntree = selectedBonEntree,
                                        articleMapByBarCode = articleMapByBarCode,
                                        printParams = printParams,
                                        station = stationList.first { it.sTATCode == selectedBonEntree.bONENTStationEntree },
                                        fournisseur = fournisseurList.first { it.fRSCodef == selectedBonEntree.bONENTCodeFrs }
                                    )
                                }
                            )

                        }) {
                        Icon(
                            imageVector = Icons.Default.Print,
                            contentDescription = stringResource(id = R.string.icn_search_clear_content_description)
                        )
                    }
                }
            )
        },
    ) { padding ->

        if(showBottomSheet) {
            ModalBottomSheet(
                sheetState = sheetState,
                onDismissRequest = {
                    scope.launch {
                        sheetState.hide()
                    }
                    achatViewModel.onShowBottomSheetChange(false)
                },
            ){
                Row(
                    modifier = Modifier.fillMaxWidth().padding(start = 12.dp, end = 12.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {



                    EditTextField(
                        modifier = Modifier.fillMaxWidth(0.45f),
                        showLeadingIcon = false,
                        text = stateAddNew.typePiece,
                        errorValue = stateAddNew.typePieceError?.asString(),
                        label = stringResource(R.string.type_piece),
                        onValueChange = {
                            inventoryTextValidationViewModel.onAddNewEvent(InventoryFormEvent.numPieceChanged(it))
                        },
                        readOnly = true,
                        enabled = true,
                        leadingIcon = Icons.Default.Person,
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    )




                    EditTextField(
                        modifier = Modifier.fillMaxWidth(),
                        showLeadingIcon = false,
                        text = stateAddNew.numPiece,
                        errorValue = stateAddNew.numPieceError?.asString(),
                        label = stringResource(R.string.num_piece),
                        onValueChange = {
                            inventoryTextValidationViewModel.onAddNewEvent(InventoryFormEvent.numPieceChanged(it))
                        },
                        readOnly = true,
                        enabled = true,
                        leadingIcon = Icons.Default.Person,
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))
                Row(
                    modifier = Modifier.fillMaxWidth().padding(start = 12.dp, end = 12.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    EditTextField(
                        modifier = Modifier.fillMaxWidth(0.45f),
                        showLeadingIcon = false,
                        text = stateAddNew.fournisseur.fRSNomf,
                        errorValue = stateAddNew.fournisseurError?.asString(),
                        label = stringResource(R.string.fournisseur),
                        onValueChange = {
                            inventoryTextValidationViewModel.onAddNewEvent(InventoryFormEvent.numPieceChanged(it))
                        },
                        readOnly = true,
                        enabled = true,
                        leadingIcon = Icons.Default.Person,
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    )

                    EditTextField(
                        modifier = Modifier.fillMaxWidth(),
                        showLeadingIcon = false,
                        text = stateAddNew.station.sTATDesg,
                        errorValue = stateAddNew.stationError?.asString(),
                        label = stringResource(R.string.station),
                        onValueChange = {
                            inventoryTextValidationViewModel.onAddNewEvent(InventoryFormEvent.numPieceChanged(it))
                        },
                        readOnly = true,
                        enabled = true,
                        leadingIcon = Icons.Default.Person,
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    )




                }


                Spacer(modifier = Modifier.height(24.dp))
            }
        }

        if(printViewModel.openPrintInfoDialogue) {
            BluetoothInfoDialogue(
                printResult = printViewModel.printResult,
                onOpenPrintInfoDialogueChange = {
                    printViewModel.onOpenPrintInfoDialogueChange(it)
                }
            )
        }
        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(padding)

        ) {






            TableHeader(
                date = selectedBonEntree.bONENTDateFormatted,
                selectedDateTime = mainViewModel.getSelectedDateTime(),
                isExpanded = showBottomSheet,
                showExpanIcon = true,
                onExpand = {
                    achatViewModel.onShowBottomSheetChange(true)
                }
            )


            FiveColumnTable(
                showFilterLine = selectedArtList.size>9 && achatViewModel.showFilterLine,
                onShowFilterLineChange = { achatViewModel.onShowFilterLineChange(it) },
                fiterValue = fiterValue,
                onFilterValueChange = { achatViewModel.onFilterValueChange(it) },
                rowTitls  = context.resources.getStringArray(R.array.fourColumnTable_array).toList(),
                // client = bonCommandeVM.selectedBonCommandeWithClient.client?.cLINomPren?:bonCommandeVM.selectedBonCommandeWithClient.bonCommande?.dEVClientName?:mainViewModel.selectedClient.cLINomPren,//mainViewModel.selectedClient.cLINomPren,
                selectedListArticle = if(fiterValue.isNotEmpty()) selectedArtList.filter { it.article.aRTCode.lowercase(
                    Locale.ROOT).contains(fiterValue.lowercase(Locale.ROOT))  || it.article.aRTDesignation.lowercase(
                    Locale.ROOT).contains(fiterValue.lowercase(Locale.ROOT)) } else selectedArtList,
                onPress = {
                   // selectArtCalculVM.setSelectedArticl(it.article)

                },
                onLongPress = {
                //    selectArtCalculVM.setSelectedArticl(it.article)
                  //  mainViewModel.onShowAlertDialogChange(true)

                },
                onTap = {
                  //  selectArtCalculVM.onShowSetArticleChange(true)

                },
                firstColumn = { item->

                    if(item.article.aRTDesignation.isNotEmpty()) {
                        item.article.aRTDesignation + " ("+item. article.aRTCode+")"
                    }
                    else context.resources.getString(R.string.article_introvable, item.article.aRTCode)
                },
                secondColumn = {
                  //  convertStringToPriceFormat(
                   // convertStringToDoubleFormat(it.lTMtBrutHT)
                    convertStringToDoubleFormat(it.prixVente)
                },
                thirdColumn = {
                    removeTrailingZeroInDouble(it.quantity)
                },
                forthColumn = {
                 //   convertStringToPriceFormat(
                    removeTrailingZeroInDouble(convertStringToDoubleFormat(it.lTMtTTC))
                },
                infoText = {

                    TableTextUtils.infoText(selectedArticle = it, isAchat = true)
                }
            )

            Spacer(modifier = Modifier.weight(1F))
            //if(selectedArtList.isNotEmpty())
            EditTextField(
                modifier = Modifier.fillMaxWidth(0.85f),
                text = convertStringToPriceFormat(selectedBonEntree.bONENTMntTTC),
                errorValue = null,
                label = context.getString(R.string.total),
                onValueChange = {
                    //  onTotalPriceWithDiscountChange(it)
                },
                readOnly = true,
                enabled = true,
                showTrailingIcon = false,
                keyboardType = KeyboardType.Decimal,
                imeAction = ImeAction.Next
            )

            Spacer(modifier = Modifier.height(6.dp))

        }

    }

}
























