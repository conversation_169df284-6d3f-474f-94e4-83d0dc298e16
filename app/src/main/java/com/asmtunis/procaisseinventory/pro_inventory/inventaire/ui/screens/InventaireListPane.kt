package com.asmtunis.procaisseinventory.pro_inventory.inventaire.ui.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.Add
import androidx.compose.material.icons.twotone.Close
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DrawerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_no_calcul.SelectArticleNoCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothInfoDialogue
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.InventoryInventaireAddRoute
import com.asmtunis.procaisseinventory.core.navigation.InventoryInventaireDetailRoute
import com.asmtunis.procaisseinventory.core.print.PrintFunctions
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.Inventaire
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.ui.InventaireViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.bottom_sheet.CustomModalBottomSheet
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.floating_button.SnapScrollingButton
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.ListItem
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyColumn
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.dokar.sonner.ToasterState
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.ModifiersUtils.floatingBtnIsVisible
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InventaireListPane(
    navigate: (route: Any) -> Unit,
    drawer: DrawerState,
    navDrawerViewmodel: NavigationDrawerViewModel,
    inventaireViewModel: InventaireViewModel,
    barCodeViewModel: BarCodeViewModel,
    mainViewModel : MainViewModel,
    selectArtInventoryVM : SelectArticleNoCalculViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    printViewModel: PrintViewModel,
    bluetoothVM: BluetoothViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,
    settingViewModel: SettingViewModel
) {


    val uiWindowState = settingViewModel.uiWindowState

    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val printParams = dataViewModel.printData
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val stationStockArticlMapByBarCode = mainViewModel.stationStockArticlMapByBarCode

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val utilisateur = mainViewModel.utilisateur
    val stationList = mainViewModel.stationList
    val isLoadingFromDb = inventaireViewModel.isLoadingFromDb

    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val inventaireListstate = inventaireViewModel.inventaireListstate
    val inventaireList = inventaireListstate.lists
    val selectedInventaire = inventaireViewModel.selectedInventaire
    val listFilter = inventaireListstate.search
    val listOrder = inventaireListstate.listOrder
    val filterList = context.resources.getStringArray(R.array.inventory_filterd)

    val ligneInventaireState = getProInventoryDataViewModel.ligneInventaireState
    val isConnected = networkViewModel.isConnected
    val selectedListLgInventaire = inventaireViewModel.selectedListLgInventaire
    val searchTextState = inventaireViewModel.searchTextState
    val showSearchView = inventaireViewModel.showSearchView

    val listState = rememberLazyListState()
    val isVisible = floatingBtnIsVisible(
        listeSize = inventaireList.size,
        canScrollForward = listState.canScrollForward
    )


    LaunchedEffect(key1 = searchTextState.text, key2 = inventaireList, key3 = inventaireListstate.filterByEtatInventaire) {
        inventaireViewModel.filterInventaire(inventaireListstate)
    }
    LaunchedEffect(key1 = Unit) {
        if(printViewModel.proccedPrinting && printViewModel.deviceAddress.isNotEmpty()) {

            // Use PrintFunctions to handle all printing methods consistently
            PrintFunctions.print(
                context = context,
                toaster = toaster,
                navigate = { navigate(it) },
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                sunmiPrintManager = null, // TODO: Add SunmiPrintManager parameter
                printParams = printParams,
                toPrintBT = {
                    printViewModel.printInventaire(
                        context = context,
                        lgInventaire = selectedListLgInventaire,
                        inventaire = selectedInventaire,
                        printParams = printParams,
                        station = stationList.first { it.sTATCode == selectedInventaire.iNVCodeStation },
                        articleMapByBarCode = articleMapByBarCode
                    )
                },
                toPrintWifi = {
                    // TODO: Add WiFi printing for Inventaire
                },
                toPrintSunmi = {
                    // TODO: Add Sunmi printing for Inventaire
                }
            )
        }

    }



        Scaffold(
            topBar = {
                AppBar(
                    showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    onNavigationClick = { scope.launch { drawer.open() } },
                    title = stringResource(id = navDrawerViewmodel.proInventorySelectedMenu.title),
                    titleVisibilty = !showSearchView,
                    actions = {
                        SearchSectionComposable(
                            label = stringResource(id = R.string.filter_by,
                                when (listFilter) {
                                    is ListSearch.FirstSearch -> filterList[0]
                                    is ListSearch.SecondSearch -> filterList[1]
                                    else -> filterList[2]}),
                            searchVisibility = showSearchView,
                            searchTextState = searchTextState,
                            onSearchValueChange = {
                                inventaireViewModel.onSearchValueChange(TextFieldValue(it))
                            },
                            onShowSearchViewChange = {
                                inventaireViewModel.onShowSearchViewChange(it)
                            },
                            onShowCustomFilterChange = {
                                inventaireViewModel.onShowCustomFilterChange(it)
                            }
                        )


                    }
                )

            },
            //    containerColor = colorResource(id = R.color.black),
            floatingActionButton = {
                val density = LocalDensity.current
                Column(
                    verticalArrangement = Arrangement.spacedBy(12.dp) ,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    AnimatedVisibility(
                        visible = isVisible && articleMapByBarCode.isNotEmpty(),
                        enter = slideInVertically {
                            with(density) { 40.dp.roundToPx() }
                        } + fadeIn(),
                        exit = fadeOut(
                            animationSpec = keyframes {
                                this.durationMillis = 120
                            }
                        )
                    ) {
                        FloatingActionButton(
                            onClick = {
                                mainViewModel.canModify(true)
                                selectArtInventoryVM.resetSelectedInventoryArticles()
                                barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                                inventaireViewModel.restInventaire()
                                val prefixe = mainViewModel.prefixList.firstOrNull { it.pREIdTable == "Inventaire" }?.pREPrefixe ?: "InvD_"

                                mainViewModel.generateCodeM(
                                    utilisateur = utilisateur,
                                    prefix = prefixe
                                )

                                navigate(InventoryInventaireAddRoute)

                            }
                        ) {
                            Icon(
                                imageVector = Icons.TwoTone.Add,
                                contentDescription = stringResource(id = R.string.cd_achat_button)
                            )
                        }
                    }

                    SnapScrollingButton(
                        isScrollInProgress = listState.isScrollInProgress,
                        isVisible = remember { derivedStateOf { listState.firstVisibleItemIndex } }.value > 15 && isVisible,
                        density = density,
                        animateScrollToItem = {
                            listState.animateScrollToItem(index = it)
                        }
                    )
                }
            }
        ) { padding ->
            if(printViewModel.openPrintInfoDialogue) {
                BluetoothInfoDialogue(
                    printResult = printViewModel.printResult,
                    onOpenPrintInfoDialogueChange = {
                        printViewModel.onOpenPrintInfoDialogueChange(it)
                    }
                )
            }
            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding)
            ) {
                if (inventaireViewModel.showCustomModalBottomSheet) {
                    CustomModalBottomSheet(
                        title = selectedInventaire.iNVCode,
                        status = selectedInventaire.status,
                        remoteResponseState = syncInventoryViewModel.responseAddInventaireState,
                        showDeleteIcon = !selectedInventaire.isSync,
                        onValidate = {
                            inventaireViewModel.setInventaireToInserted()
                        },
                        onDismissRequest= {
                            inventaireViewModel.onShowCustomModalBottomSheetChange(false)
                        },
                        onDeleteRequest= {
                            inventaireViewModel.deleteInventaire(
                                inventaire = selectedInventaire,
                                listLgInventaire = selectedListLgInventaire
                            )

                        },
                        onPrintRequest= {
                            PrintFunctions.print(
                                context = context,
                                toaster = toaster,
                                navigate = { navigate(it) },
                                printViewModel = printViewModel,
                                bluetoothVM = bluetoothVM,
                                printParams = printParams,
                                toPrintBT = {
                                    printViewModel.printInventaire(
                                        context = context,
                                        lgInventaire = selectedListLgInventaire,
                                        inventaire = selectedInventaire,
                                        printParams = printParams,
                                        station = stationList.first { it.sTATCode == selectedInventaire.iNVCodeStation },
                                        articleMapByBarCode = articleMapByBarCode
                                    )
                                }
                            )

                        },
                        onSyncRequest = {
                            syncInventoryViewModel.syncInventaire(selectedInv = selectedInventaire)
                        }

                    )
                }

                if (inventaireViewModel.showCustomFilter) {
                    FilterContainer(
                        filterList = filterList,
                        listFilter = listFilter,
                        listOrder = listOrder,
                        orderList = stringArrayResource(id = R.array.inventaire_order),
                        onShowCustomFilterChange  = {
                            inventaireViewModel.onShowCustomFilterChange(false)
                        },
                        onEvent = {
                            inventaireViewModel.onEvent(it)
                        },
                        customFilterContent= {
                            val etatInventaireList = listOf("En cours","UPDATED")

                            FilterSectionComposable (
                                title  = stringResource(id = R.string.filter_by_etat_inventaire),
                                currentFilterLable =  inventaireListstate.filterByEtatInventaire,
                                onAllEvent = {
                                    inventaireViewModel.onEvent(ListEvent.FirstCustomFilter(""))
                                },
                                onEvent = {
                                    inventaireViewModel.onEvent(ListEvent.FirstCustomFilter(etatInventaireList[it]))
                                },

                                filterCount = etatInventaireList.size,
                                customFilterCode  = {
                                    etatInventaireList[it]
                                },
                                filterLabel  = {
                                    etatInventaireList[it]
                                }
                            )
                        }
                    )
                }

                if(ligneInventaireState.error != null) {

                    Text(text = ligneInventaireState.error, maxLines = 9)
                    Spacer(modifier = Modifier.height(12.dp))
                    IconButton(onClick = {
                      getProInventoryDataViewModel.resetligneInventaireState()
                    }) {
                        Icon(
                            imageVector = Icons.TwoTone.Close,
                            tint = MaterialTheme.colorScheme.error,
                            contentDescription = stringResource(
                                id = R.string.cd_toggle_drawer

                            )
                        )
                    }
                }
                if(ligneInventaireState.loading)
                    LottieAnim(lotti = R.raw.loading, size = 250.dp)
                else {
                    if(isLoadingFromDb) CircularProgressIndicator()

                    if (inventaireList.isNotEmpty()) {
                        InventaireList(
                            navigate = { navigate(it) },
                            toaster = toaster,
                            isConnected = isConnected,
                            articleMapByBarCode = articleMapByBarCode,
                            selectedInventaire = selectedInventaire,
                            selectedBaseconfig = selectedBaseconfig,
                            inventaireViewModel = inventaireViewModel,
                            barCodeViewModel = barCodeViewModel,
                            stationList = stationList,
                            filteredList = inventaireList,
                            selectArtInventoryVM = selectArtInventoryVM,
                            stationStockArticlMapByBarCode = stationStockArticlMapByBarCode,
                            listState = listState,
                            getProInventoryDataViewModel = getProInventoryDataViewModel,
                            setCodM = { mainViewModel.setCodM(it) },
                        )
                    } else {
                        LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
                    }
                }


            }
        }

}


@Composable
fun InventaireList(
    navigate: (route: Any) -> Unit,
    toaster: ToasterState,
    isConnected: Boolean,
    selectedBaseconfig: BaseConfig,
    selectedInventaire: Inventaire,
    inventaireViewModel: InventaireViewModel,
    barCodeViewModel: BarCodeViewModel,
    articleMapByBarCode: Map<String, Article>,
    stationList: List<Station>,
    stationStockArticlMapByBarCode: Map<String, StationStockArticle>,
     selectArtInventoryVM : SelectArticleNoCalculViewModel,
    filteredList: Map<Inventaire, List<LigneInventaire>>,
    listState: LazyListState,
    setCodM: (String)-> Unit,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
) {

    val context = LocalContext.current

    val inventaire: MutableList<Inventaire> = arrayListOf()
    val ligneInventaire: MutableList<LigneInventaire> = arrayListOf()
    filteredList.forEach { (key, value) ->
        run {
            inventaire.add(key)
            ligneInventaire.addAll(value.map { it })
        }
    }

    val isRefreshing  = getProInventoryDataViewModel.inventaireState.loading || getProInventoryDataViewModel.ligneInventaireState.loading

    PullToRefreshLazyColumn(
        items = inventaire,
        lazyListState = listState,
        isRefreshing = isRefreshing,
        pullToRefreshEnabled = !inventaire.any { !it.isSync } && isConnected,
        onRefresh = { getProInventoryDataViewModel.getInventaire(baseConfig = selectedBaseconfig) },
        key = { inventair -> inventair.id },
        content = { inventair ->
            ListItem(
                isSelected = inventair == selectedInventaire,
                firstText = inventair.iNVCode,
                secondText = stringResource(id = R.string.station_value, stationList.firstOrNull { it.sTATCode == inventair.iNVCodeStation }?.sTATDesg?: inventair.iNVCodeStation!!),
                // thirdText =  " : " + bonEntree[index].,
                // forthText = " : " + bonEntree[index].,
                dateText =  inventair.iNVDateFormatted,
                dateTextRemarque = StringUtils.stringPlural(
                    nbr = filteredList[inventair]?.size?:0,
                    single = stringResource(id = R.string.article_title),
                    plural = stringResource(id = R.string.article_field_title)
                ),
                isSync = inventair.isSync,
                status = inventair.status,
                onItemClick = {
                    inventaireViewModel.restInventaire()
                    selectArtInventoryVM.resetSelectedInventoryArticles()
                    barCodeViewModel.onBarCodeInfo(barCode = BareCode())



                    val listLigneInventaire = ligneInventaire.filter { it.lGINVCodeInv == inventair.iNVCode }

                    if (inventair.status == ItemStatus.WAITING.status) {
                        inventair.invCodeM?.let { setCodM(it) }

                        val station = stationList.firstOrNull { it.sTATCode == inventair.iNVCodeStation }?: Station(sTATCode = inventair.iNVCodeStation?: "N/A")

                        inventaireViewModel.onSelectedStationChange(station)

                        selectArtInventoryVM.setSelectedList(
                            listLigneInventaire = listLigneInventaire,
                            articleMapByBarCode = articleMapByBarCode,
                            stationStockArticl = stationStockArticlMapByBarCode
                        )


                        navigate(InventoryInventaireAddRoute)
                    }
                    else {
                        if((filteredList[inventair]?.size?:0) == 0) {
                            //  val encodedPart = URLEncoder.encode(inventair.iNVCode, StandardCharsets.UTF_8.toString())

                            getProInventoryDataViewModel.getLigneInventaires(baseConfig = selectedBaseconfig, code = inventair.iNVCode)
                            inventaireViewModel.onSelectedInventaireChange(inventair)
                        }
                        else inventaireViewModel.onSelectedInventaireWithLineChange(filteredList.filter { it.key == inventair })

                        selectArtInventoryVM.setSelectedList(
                            listLigneInventaire = listLigneInventaire,
                            articleMapByBarCode = articleMapByBarCode,
                            stationStockArticl = stationStockArticlMapByBarCode
                        )

                        navigate(InventoryInventaireDetailRoute)
                    }

                },
                onMoreClick = {
                    if((filteredList[inventair]?.size?:0) == 0)  {
                        getProInventoryDataViewModel.getLigneInventaires(
                            baseConfig = selectedBaseconfig,
                           code = inventair.iNVCode
                        )
                    } else {
                        inventaireViewModel.onSelectedInventaireWithLineChange(filteredList.filter { it.key == inventair })
                        inventaireViewModel.onShowCustomModalBottomSheetChange(true)
                    }

                }
            )
        },
    )

}

