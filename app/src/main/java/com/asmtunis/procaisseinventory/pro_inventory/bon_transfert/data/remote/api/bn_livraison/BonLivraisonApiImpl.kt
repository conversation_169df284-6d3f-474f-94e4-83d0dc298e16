package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.remote.api.bn_livraison

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.AddBatchBonLivraisonResponse
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.BonLivraison
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class BonLivraisonApiImpl(private val client: HttpClient) : BonLivraisonApi {
    override suspend fun getBonLivraison(baseConfig: String): Flow<DataResult<List<BonLivraison>>> = flow {

        val result = executePostApiCall<List<BonLivraison>>(
            client = client,
            endpoint = Urls.GET_BON_LIVRAISON,
            baseConfig = baseConfig,
        )

        emitAll(result)
    }

    override suspend fun addBatchBonLivraisonWithLines(baseConfig: String): Flow<DataResult<List<AddBatchBonLivraisonResponse>>> = flow {

        val result = executePostApiCall<List<AddBatchBonLivraisonResponse>>(
            client = client,
            endpoint = Urls.ADD_BATCH_BON_LIVRAISON_WITH_LINES,
            baseConfig = baseConfig,
        )

        emitAll(result)
    }
    }