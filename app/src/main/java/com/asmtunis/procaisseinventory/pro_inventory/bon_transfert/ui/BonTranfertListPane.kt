package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.DrawerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_no_calcul.SelectArticleNoCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothInfoDialogue
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.enum_classes.EtatBonTransfert
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.InventoryBonTransfertAddRoute
import com.asmtunis.procaisseinventory.core.navigation.InventoryBonTransfertDetailRoute
import com.asmtunis.procaisseinventory.core.print.PrintFunctions
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.BonTransfertViewModel
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.BonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraisonWithArticle
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.bottom_sheet.CustomModalBottomSheet
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.floating_button.SnapScrollingButton
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.ListItem
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyColumn
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.ModifiersUtils.floatingBtnIsVisible
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BonTranfertPane (
    navigate: (route: Any) -> Unit,
    drawer: DrawerState,
    navDrawerViewmodel: NavigationDrawerViewModel,
    bonTransfertViewModel: BonTransfertViewModel,
    barCodeViewModel: BarCodeViewModel,
    mainViewModel: MainViewModel,
    selectArtInventoryVM: SelectArticleNoCalculViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    printViewModel: PrintViewModel,
    bluetoothVM: BluetoothViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    settingViewModel: SettingViewModel,
    syncInventoryViewModel: SyncInventoryViewModel
) {
    val uiWindowState = settingViewModel.uiWindowState
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val isConnected = networkViewModel.isConnected
    val state = bonTransfertViewModel.bonTransfertListstate
    val listFilter = state.search
    val listOrder = state.listOrder
    val filterList = context.resources.getStringArray(R.array.bn_transfert_filter)

    val selectedBonTransfert = bonTransfertViewModel.selectedBonTransfert
    val selectedListLgBonTransfert = bonTransfertViewModel.selectedListLgBonTransfert

    val tvaList = mainViewModel.tvaList
   val stationList = mainViewModel.stationList

    val ligneBonLivraisonState = getProInventoryDataViewModel.ligneBonLivraisonState
    val bonLivraisonState = getProInventoryDataViewModel.bonLivraisonState

    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val utilisateur = mainViewModel.utilisateur

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)



    val listState = rememberLazyListState()
    val isVisible = floatingBtnIsVisible(
            listeSize = state.lists.size,
            canScrollForward = listState.canScrollForward
        )

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val printParams = dataViewModel.printData

    LaunchedEffect(
        key1 = bonTransfertViewModel.searchTextState.text,
        key2 = state.lists,
        key3 = state.filterByStationDestination//,
        // key4=state.filterByStationSource
    ) {
        bonTransfertViewModel.filterBonTransfert(state)
    }

    LaunchedEffect(key1 = Unit) {
        if(printViewModel.proccedPrinting && printViewModel.deviceAddress.isNotEmpty()) {

            PrintFunctions.print(
                context = context,
                toaster = toaster,
                navigate = { navigate(it) },
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                printParams = printParams,
                toPrintBT = {
                    printViewModel.printBonTransfert(
                        context = context,
                        lgBonTransfert = selectedListLgBonTransfert,
                        bonTransfert = selectedBonTransfert,
                        articleMapByBarCode = articleMapByBarCode,
                        printParams = printParams,
                        stationSource = stationList.first { it.sTATCode == selectedBonTransfert.bONTransStatSource },
                        stationDestination = stationList.first { it.sTATCode == selectedBonTransfert.bONTransStatDest }
                    )
                }
            )
        }
    }

    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = {
                    scope.launch { drawer.open() }
                },
                showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                actions = {
                    SearchSectionComposable(
                        label = stringResource(
                            R.string.filter_by,
                            when (listFilter) {
                                is ListSearch.FirstSearch -> filterList[0]
                                is ListSearch.SecondSearch -> filterList[1]
                                else -> filterList[2]
                            }
                        ),
                        searchVisibility = bonTransfertViewModel.showSearchView,
                        searchTextState = bonTransfertViewModel.searchTextState,
                        onSearchValueChange = {
                            bonTransfertViewModel.onSearchValueChange(TextFieldValue(it))
                        },
                        onShowSearchViewChange = {
                            bonTransfertViewModel.onShowSearchViewChange(it)
                        },
                        onShowCustomFilterChange = {
                            bonTransfertViewModel.onShowCustomFilterChange(it)
                        }
                    )


                },

                title = stringResource(id = navDrawerViewmodel.proInventorySelectedMenu.title),
                titleVisibilty = !bonTransfertViewModel.showSearchView,
            )
        },

        floatingActionButton = {
            val density = LocalDensity.current
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp) ,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                AnimatedVisibility(
                    visible = isVisible && articleMapByBarCode.any { stringToDouble(it.value.aRTQteStock) > 0 },
                    enter = slideInVertically {
                        with(density) { 40.dp.roundToPx() }
                    } + fadeIn(),
                    exit = fadeOut(
                        animationSpec = keyframes {
                            this.durationMillis = 120
                        }
                    )
                ) {
                    FloatingActionButton(
                        onClick = {
                            bonTransfertViewModel.restBonTransfert()
                            selectArtInventoryVM.resetSelectedInventoryArticles()
                            barCodeViewModel.onBarCodeInfo(barCode = BareCode())

                            val prefixe = mainViewModel.prefixList.firstOrNull { it.pREIdTable == "bon_transfert" }?.pREPrefixe ?: "InvD_"
                            mainViewModel.generateCodeM(
                                utilisateur = utilisateur,
                                prefix = prefixe
                            )

                            navigate(InventoryBonTransfertAddRoute)

                        }) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = stringResource(id = R.string.cd_achat_button)
                        )
                    }
                }


                SnapScrollingButton(
                    isScrollInProgress = listState.isScrollInProgress,
                    isVisible = remember { derivedStateOf { listState.firstVisibleItemIndex } }.value > 15 && isVisible,
                    density = density,
                    animateScrollToItem = {
                        listState.animateScrollToItem(index = it)
                    }
                )
            }
        }
    ) { padding ->

        if (printViewModel.openPrintInfoDialogue) {
            BluetoothInfoDialogue(
                printResult = printViewModel.printResult,
                onOpenPrintInfoDialogueChange = {
                    printViewModel.onOpenPrintInfoDialogueChange(it)
                },
            )
        }

        if (bonTransfertViewModel.showCustomModalBottomSheet) {
            CustomModalBottomSheet(
                title = selectedBonTransfert.bONTransNum,
                status = selectedBonTransfert.status,
                remoteResponseState = syncInventoryViewModel.responseAddBonTransfertState,
                showDeleteIcon = !selectedBonTransfert.isSync,
                onDismissRequest = {
                    bonTransfertViewModel.onShowCustomModalBottomSheetChange(false)
                },
                onDeleteRequest = {
                    bonTransfertViewModel.deleteBonTransfert(
                        bonLivraison = selectedBonTransfert,
                        listLgBonLivraisonWithArticle = bonTransfertViewModel.selectedListLgBonTransfertWithArticle,
                        updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation->
                            mainViewModel.updateQtePerStation(newQteStation = newQteStation, newSartQteDeclare = newSartQteDeclare, codeArticle=codeArticle,codeStation=codeStation)
                        },
                        insertStationStockArticle = {stationStockArticl ->
                            mainViewModel.insertStationStockArticle(stationStockArticl)
                        },
                        updateArtQteStock = {  newQteAllStations,newQteStation,codeArticle ->
                            mainViewModel.updateArtQteStock(
                                newQteAllStations = newQteAllStations,
                                newQteStation= newQteStation,
                                codeArticle=codeArticle
                            )
                        }
                    )

                },
                onPrintRequest = {
                    PrintFunctions.print(
                        context = context,
                        toaster = toaster,
                        navigate = { navigate(it) },
                        printViewModel = printViewModel,
                        bluetoothVM = bluetoothVM,
                        printParams = printParams,
                        toPrintBT = {
                            printViewModel.printBonTransfert(
                                context = context,
                                lgBonTransfert = selectedListLgBonTransfert,
                                bonTransfert = selectedBonTransfert,
                                articleMapByBarCode = articleMapByBarCode,
                                printParams = printParams,
                                stationSource = stationList.first { it.sTATCode == selectedBonTransfert.bONTransStatSource },
                                stationDestination = stationList.first { it.sTATCode == selectedBonTransfert.bONTransStatDest }
                            )
                        }
                    )
                },
                onSyncRequest = {
                    syncInventoryViewModel.syncBnTransfert(selectedBnTransfert = selectedBonTransfert)
                }
            )
        }


        if (bonTransfertViewModel.showCustomFilter) {
            FilterContainer(
                filterList = filterList,
                listFilter = listFilter,
                listOrder = listOrder,
                orderList = context.resources.getStringArray(R.array.bn_transfert_order),
                onShowCustomFilterChange = {
                    bonTransfertViewModel.onShowCustomFilterChange(false)
                },
                onEvent = {
                    bonTransfertViewModel.onEvent(event = it)
                },
                customFilterContent = {
                    FilterSectionComposable(
                        title = stringResource(R.string.filter_by_station),
                        currentFilterLable = state.filterByStationSource,
                        onAllEvent = {
                            bonTransfertViewModel.onEvent(ListEvent.FirstCustomFilter(""))
                        },
                        onEvent = { bonTransfertViewModel.onEvent(ListEvent.FirstCustomFilter(stationList[it].sTATCode)) },
                        filterCount = stationList.size,
                        customFilterCode = { stationList[it].sTATCode },
                        filterLabel = { stationList[it].sTATDesg }
                    )

                    FilterSectionComposable(
                        title = stringResource(R.string.filter_by_station_destination),
                        currentFilterLable = state.filterByStationDestination,
                        onAllEvent = {
                            bonTransfertViewModel.onEvent(ListEvent.SecondCustomFilter(""))
                        },
                        onEvent = {
                            bonTransfertViewModel.onEvent(ListEvent.SecondCustomFilter(stationList[it].sTATCode))
                        },

                        filterCount = stationList.size,
                        customFilterCode = { stationList[it].sTATCode },
                        filterLabel = { stationList[it].sTATDesg }
                    )

                    val etatBonTransfertList = EtatBonTransfert.entries.map { it.value }
                    FilterSectionComposable(
                        title = stringResource(R.string.etat),
                        currentFilterLable = state.filterByEtatBnTransfert,
                        onAllEvent = {
                            bonTransfertViewModel.onEvent(ListEvent.ThirdCustomFilter(""))
                        },
                        onEvent = {
                            bonTransfertViewModel.onEvent(ListEvent.ThirdCustomFilter(etatBonTransfertList[it]))
                        },

                        filterCount = etatBonTransfertList.size,
                        customFilterCode = { etatBonTransfertList[it] },
                        filterLabel = { etatBonTransfertList[it] }
                    )


                }
            )


        }

        Column(
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
            //   .padding(top = 12.dp)
        ) {

            if (bonLivraisonState.loading || ligneBonLivraisonState.loading) {
                LottieAnim(lotti = R.raw.loading, size = 250.dp)
            } else {


                if (state.lists.isNotEmpty()) {
                    BonTransfertList(
                        isConnected = isConnected,
                        isRefreshing = false,
                        stationList = stationList,
                        selectedBonTransfert = selectedBonTransfert,
                        filteredList = state.lists,
                        listState = listState,
                        onItemClick = { bonTransfer ->
                            bonTransfertViewModel.restBonTransfert()
                            barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                            selectArtInventoryVM.resetSelectedInventoryArticles()

                            val bonTransfertWithLignes = state.lists.filter { it.key == bonTransfer }
                            bonTransfertViewModel.onSelectedBonTransfertChange(bonTransfertWithLignes)


                            for (ligne in bonTransfertWithLignes.values.flatten()) {
                                val lgBnTransfert = ligne.ligneTicket?: LigneBonLivraison()
                                val tva = tvaList.firstOrNull { it.tVACode == lgBnTransfert.lGBonTransTVA}
                                val article = articleMapByBarCode[lgBnTransfert.lGBonTransCodeArt]?: Article(aRTCodeBar = lgBnTransfert.lGBonTransCodeArt, aRTCode = lgBnTransfert.lGBonTransCodeArt)

                                selectArtInventoryVM.setConsultationSelectedArticleInventoryList(
                                    article = article,
                                    tva = tva,
                                    quantity = lgBnTransfert.qteDecTransferer?: "N/A",
                                    prixCaisse = lgBnTransfert.lGBonTransPUHT?: "N/A",
                                    prixAchatHt = lgBnTransfert.lGBonTransPUHT?: "N/A",
                                    qteStationFromDB = article.aRTQteStock.ifEmpty { "0" }
                                )
                            }


                            bonTransfertViewModel.onStationDestinationChange(stationList.firstOrNull { it.sTATCode == bonTransfer.bONTransStatDest }?: Station())
                            bonTransfertViewModel.onSelectedStationChange(stationList.firstOrNull { it.sTATCode == bonTransfer.bONTransStatSource }?: Station())


                            navigate(InventoryBonTransfertDetailRoute)

                        },
                        onMoreClick = { bonTransfer->
                            bonTransfertViewModel.onSelectedBonTransfertChange(state.lists.filter { it.key == bonTransfer })
                            bonTransfertViewModel.onShowCustomModalBottomSheetChange(true)
                        },
                        onRefresh = {
                            getProInventoryDataViewModel.getBonLivraison(baseConfig = selectedBaseconfig)
                            getProInventoryDataViewModel.getLigneBonLivraison(baseConfig = selectedBaseconfig)
                        }
                    )
                }
                else LottieAnim(lotti = R.raw.emptystate, size = 250.dp)

            }
        }
    }
}

@Composable
fun BonTransfertList(
    isConnected: Boolean,
    isRefreshing: Boolean,
    stationList: List<Station>,
    selectedBonTransfert:  BonLivraison,
    filteredList: Map<BonLivraison, List<LigneBonLivraisonWithArticle>>,
    listState: LazyListState,
    onItemClick: (BonLivraison) -> Unit,
    onMoreClick: (BonLivraison) -> Unit,
    onRefresh: () -> Unit
) {


    val bonTransfert: MutableList<BonLivraison> = arrayListOf()
    val ligneBonEntree: MutableList<LigneBonLivraison> = arrayListOf()



    filteredList.forEach { (key, value) ->
        run {
            bonTransfert.add(key)
            ligneBonEntree.addAll(value.map { it.ligneTicket?: LigneBonLivraison() })
        }
    }



    PullToRefreshLazyColumn(
        items = bonTransfert,
        lazyListState = listState,
        isRefreshing = isRefreshing,
        pullToRefreshEnabled = !bonTransfert.any { !it.isSync } && isConnected,
        onRefresh = { onRefresh() },
        key = { bonTransfer ->
            bonTransfer.id
        },
        content = { bonTransfer ->
            ListItem(
                isSelected = selectedBonTransfert == bonTransfer,
                onItemClick = {
                    onItemClick(bonTransfer)
                },
                firstText = bonTransfer.bONTransNum,
                secondText = (stationList.firstOrNull { it.sTATCode == bonTransfer.bONTransStatSource }?.sTATDesg?:bonTransfer.bONTransStatSource)
                        + " --> " +
                        (stationList.firstOrNull { it.sTATCode == bonTransfer.bONTransStatDest }?.sTATDesg?:bonTransfer.bONTransStatDest),

                  thirdText = stringResource(R.string.etats, bonTransfer.bONTransEtat?: "N/A"),
                // forthText = " : " + bonEntree[index].,
                dateText = bonTransfer.bONTransDateFormatted,
                dateTextRemarque = StringUtils.stringPlural(
                    nbr = filteredList[bonTransfer]?.size?:0,
                    single = stringResource(id = R.string.article_title),
                    plural = stringResource(id = R.string.article_field_title)
                ),
                isSync = bonTransfer.isSync,
                status = bonTransfer.status,
                onResetDeletedClick = {
                    //bonTransfertViewModel.restDeletedVisite(bonEntree[index])
                },
                onMoreClick = {
                   onMoreClick(bonTransfer)
                },

                )
        },
    )

}