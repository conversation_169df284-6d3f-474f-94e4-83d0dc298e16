package com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.bn_entree.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.BON_ENTREE_TABLE
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.LIGNE_BON_ENTREE_TABLE
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.BonEntree
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.LigneBonEntree
import kotlinx.coroutines.flow.Flow


@Dao
interface BonEntreeDAO {


    @get:Query("SELECT * FROM $BON_ENTREE_TABLE  ORDER By timestamp DESC")
    val all: Flow<List<BonEntree>>



    @Query("SELECT * FROM $BON_ENTREE_TABLE WHERE isSync=0 and (Status=:status) and  BON_ENT_SYNC <> 1  ORDER By timestamp DESC")
    fun getByStatus(status: String): Flow<List<BonEntree>>

    @get:Query("SELECT * FROM  $BON_ENTREE_TABLE  where  isSync=0 and  (Status='INSERTED'  or Status='UPDATED')  ORDER By timestamp DESC")
    val nonSync: Flow<List<BonEntree>>

    @Query(
        "SELECT * FROM $BON_ENTREE_TABLE " +
                " JOIN $LIGNE_BON_ENTREE_TABLE ON ${BON_ENTREE_TABLE}.BON_ENT_Num = ${LIGNE_BON_ENTREE_TABLE}.LIG_BonEntree_NumBon" +

                " WHERE $BON_ENTREE_TABLE.IsSync=0 and  ($BON_ENTREE_TABLE.Status='INSERTED'  or ${BON_ENTREE_TABLE}.Status='UPDATED') "

    )
    fun noSynced(): Flow<Map<BonEntree, List<LigneBonEntree>>>


    @Query("SELECT * FROM $BON_ENTREE_TABLE WHERE (Status=:status)  ORDER By timestamp DESC")
    fun getByStatusForced(status: String): Flow<List<BonEntree>>

    @Query("SELECT ifnull(MAX(cast(substr(BON_ENT_Num,length(:prefix) + 1 ,length('BON_ENT_Num'))as integer)), 0) + 1 FROM $BON_ENTREE_TABLE  WHERE substr(BON_ENT_Num, 0 ,length(:prefix) + 1) = :prefix")
    fun getNewCode(prefix: String): Flow<String>




    @Query("UPDATE $BON_ENTREE_TABLE SET BON_ENT_SYNC= 1, Status = 'SELECTED', IsSync=1, BON_ENT_Num=:bonEntNum WHERE  BON_ENT_Num_M= :bonEntNumM and BON_ENT_Exer=:exercice")
    fun updateBonEntreeStatus(bonEntNum: String, bonEntNumM: String, exercice: String)

    @Query("SELECT COUNT(*) FROM $BON_ENTREE_TABLE " +
            "JOIN $LIGNE_BON_ENTREE_TABLE ON $BON_ENTREE_TABLE.BON_ENT_Num = $LIGNE_BON_ENTREE_TABLE.LIG_BonEntree_NumBon")
    fun count(): Flow<Int>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: BonEntree)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<BonEntree>)

    @Query("DELETE FROM $BON_ENTREE_TABLE")
    fun deleteAll()

    @Delete
    fun delete(bonEntree: BonEntree)

    @Query(
        "SELECT * FROM $BON_ENTREE_TABLE " +
                "JOIN $LIGNE_BON_ENTREE_TABLE ON $BON_ENTREE_TABLE.BON_ENT_Num = $LIGNE_BON_ENTREE_TABLE.LIG_BonEntree_NumBon" +
                " WHERE $BON_ENTREE_TABLE.BON_ENT_CodeFrs LIKE '%' || :searchString || '%' " +
                " and ( CASE WHEN :filterByStationEntree !=  '' THEN BON_ENT_StationEntree =:filterByStationEntree ELSE BON_ENT_StationEntree !=:filterByStationEntree END " +
                "and  CASE WHEN :filterByFournisseur !=  ''THEN BON_ENT_CodeFrs=:filterByFournisseur ELSE BON_ENT_CodeFrs !=:filterByFournisseur  END) " +


                " ORDER BY " +
                "CASE WHEN :sortBy = 'BON_ENT_Num'  AND :isAsc = 1 THEN BON_ENT_Num END ASC, " +
                "CASE WHEN :sortBy = 'BON_ENT_Num'  AND :isAsc = 2 THEN BON_ENT_Num END DESC, " +
                "CASE WHEN :sortBy = 'BON_ENT_NumPiece'  AND :isAsc = 1 THEN BON_ENT_NumPiece END ASC, " +
                "CASE WHEN :sortBy = 'BON_ENT_NumPiece'  AND :isAsc = 2 THEN BON_ENT_NumPiece END DESC, " +
                "CASE WHEN :sortBy = 'BON_ENT_Date'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',BON_ENT_Date) END ASC, " +
                "CASE WHEN :sortBy = 'BON_ENT_Date'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',BON_ENT_Date) END DESC "
    )
    fun filterByBonEntCodeFrs(searchString: String, filterByStationEntree: String, filterByFournisseur: String, sortBy: String, isAsc: Int): Flow<Map<BonEntree, List<LigneBonEntree>>>


    @Query(
        "SELECT * FROM $BON_ENTREE_TABLE " +
                "JOIN $LIGNE_BON_ENTREE_TABLE ON $BON_ENTREE_TABLE.BON_ENT_Num = $LIGNE_BON_ENTREE_TABLE.LIG_BonEntree_NumBon" +
                " WHERE $BON_ENTREE_TABLE.BON_ENT_NumPiece LIKE '%' || :searchString || '%' " +
                " and ( CASE WHEN :filterByStationEntree !=  '' THEN BON_ENT_StationEntree =:filterByStationEntree ELSE BON_ENT_StationEntree !=:filterByStationEntree END " +
                "and  CASE WHEN :filterByFournisseur !=  ''THEN BON_ENT_CodeFrs=:filterByFournisseur ELSE BON_ENT_CodeFrs !=:filterByFournisseur  END) " +


                " ORDER BY " +
                "CASE WHEN :sortBy = 'BON_ENT_Num'  AND :isAsc = 1 THEN BON_ENT_Num END ASC, " +
                "CASE WHEN :sortBy = 'BON_ENT_Num'  AND :isAsc = 2 THEN BON_ENT_Num END DESC, " +
                "CASE WHEN :sortBy = 'BON_ENT_NumPiece'  AND :isAsc = 1 THEN BON_ENT_NumPiece END ASC, " +
                "CASE WHEN :sortBy = 'BON_ENT_NumPiece'  AND :isAsc = 2 THEN BON_ENT_NumPiece END DESC, " +
                "CASE WHEN :sortBy = 'BON_ENT_Date'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',BON_ENT_Date) END ASC, " +
                "CASE WHEN :sortBy = 'BON_ENT_Date'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',BON_ENT_Date) END DESC "
    )
    fun filterByBonEntNumPiece(searchString: String, filterByStationEntree: String, filterByFournisseur: String, sortBy: String, isAsc: Int): Flow<Map<BonEntree, List<LigneBonEntree>>>


    @Query(
        "SELECT * FROM $BON_ENTREE_TABLE " +
                "JOIN $LIGNE_BON_ENTREE_TABLE ON $BON_ENTREE_TABLE.BON_ENT_Num = $LIGNE_BON_ENTREE_TABLE.LIG_BonEntree_NumBon" +
                " WHERE $BON_ENTREE_TABLE.BON_ENT_Num LIKE '%' || :searchString || '%' " +
                " and ( CASE WHEN :filterByStationEntree !=  '' THEN BON_ENT_StationEntree =:filterByStationEntree ELSE BON_ENT_StationEntree !=:filterByStationEntree END " +
                "and  CASE WHEN :filterByFournisseur !=  ''THEN BON_ENT_CodeFrs=:filterByFournisseur ELSE BON_ENT_CodeFrs !=:filterByFournisseur  END) " +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'BON_ENT_Num'  AND :isAsc = 1 THEN BON_ENT_Num END ASC, " +
                "CASE WHEN :sortBy = 'BON_ENT_Num'  AND :isAsc = 2 THEN BON_ENT_Num END DESC, " +
                "CASE WHEN :sortBy = 'BON_ENT_NumPiece'  AND :isAsc = 1 THEN BON_ENT_NumPiece END ASC, " +
                "CASE WHEN :sortBy = 'BON_ENT_NumPiece'  AND :isAsc = 2 THEN BON_ENT_NumPiece END DESC, " +
                "CASE WHEN :sortBy = 'BON_ENT_Date'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',BON_ENT_Date) END ASC, " +
                "CASE WHEN :sortBy = 'BON_ENT_Date'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',BON_ENT_Date) END DESC "
    )
    fun filterByBonEntNum(searchString: String, filterByStationEntree: String, filterByFournisseur: String, sortBy: String, isAsc: Int): Flow<Map<BonEntree, List<LigneBonEntree>>>


    @Query(
        "SELECT * FROM $BON_ENTREE_TABLE " +
                "JOIN $LIGNE_BON_ENTREE_TABLE ON ${BON_ENTREE_TABLE}.BON_ENT_Num = $LIGNE_BON_ENTREE_TABLE.LIG_BonEntree_NumBon " +
                "WHERE  CASE WHEN :filterByStationEntree !=  '' THEN BON_ENT_StationEntree =:filterByStationEntree ELSE BON_ENT_StationEntree !=:filterByStationEntree END   " +
                "and  CASE WHEN :filterByFournisseur !=  '' THEN BON_ENT_CodeFrs=:filterByFournisseur ELSE BON_ENT_CodeFrs !=:filterByFournisseur  END " +


                " ORDER BY " +
                "CASE WHEN :sortBy = 'BON_ENT_Num'  AND :isAsc = 1 THEN BON_ENT_Num END ASC, " +
                "CASE WHEN :sortBy = 'BON_ENT_Num'  AND :isAsc = 2 THEN BON_ENT_Num END DESC, " +
                "CASE WHEN :sortBy = 'BON_ENT_NumPiece'  AND :isAsc = 1 THEN BON_ENT_NumPiece END ASC, " +
                "CASE WHEN :sortBy = 'BON_ENT_NumPiece'  AND :isAsc = 2 THEN BON_ENT_NumPiece END DESC, " +
                "CASE WHEN :sortBy = 'BON_ENT_Date'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',BON_ENT_Date) END ASC, " +
                "CASE WHEN :sortBy = 'BON_ENT_Date'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',BON_ENT_Date) END DESC "
    )
    fun getAllFiltred(isAsc: Int, filterByStationEntree: String, filterByFournisseur: String, sortBy: String): Flow<Map<BonEntree, List<LigneBonEntree>>>

}
