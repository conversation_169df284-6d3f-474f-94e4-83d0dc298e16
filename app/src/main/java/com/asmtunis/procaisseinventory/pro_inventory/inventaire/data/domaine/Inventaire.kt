package com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


@Entity(tableName = ProInventoryConstants.INVENTAIRE_TABLE)
@Serializable
data class Inventaire(
    @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,
    
   // @PrimaryKey
    @ColumnInfo(name = "INV_Code")
    @SerialName("INV_Code")
    var iNVCode: String = "",

    @ColumnInfo(name = "INV_Code_M")
    @SerialName("INV_Code_M")
    var invCodeM: String? = null,

    @ColumnInfo(name = "INV_Date")
    @SerialName("INV_Date")
    var iNVDate: String? = null,

    @ColumnInfo(name = "INV_Code_Station")
    @SerialName("INV_Code_Station")
    var iNVCodeStation: String? = null,

    @ColumnInfo(name = "INV_Etat")
    @SerialName("INV_Etat")
    var iNVEtat: String? = null,

    @ColumnInfo(name = "INV_User")
    @SerialName("INV_User")
    var iNVUser: String? = null,

    @ColumnInfo(name = "timestamp")
   // @Exclude
    var timestamp: Long = 0

    ): BaseModel() {
    @Ignore
    val iNVDateFormatted = this.iNVDate?.substringBefore(".")?: "N/A"
}
