package com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.inv.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.INVENTAIRE_TABLE
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.Inventaire
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import kotlinx.coroutines.flow.Flow


@Dao
interface InventaireDAO {
    @get:Query("SELECT * FROM $INVENTAIRE_TABLE ORDER BY timestamp DESC")
    val all: Flow<List<Inventaire>>

    @Query("SELECT COUNT(*) FROM $INVENTAIRE_TABLE " +
            "JOIN ${ProInventoryConstants.LIGNE_INVENTAIRE_TABLE} ON ${INVENTAIRE_TABLE}.INV_Code = ${ProInventoryConstants.LIGNE_INVENTAIRE_TABLE}.LG_INV_Code_Inv " )
    fun count(): Flow<Int>


    @Query("SELECT * FROM $INVENTAIRE_TABLE " +
            "LEFT JOIN ${ProInventoryConstants.LIGNE_INVENTAIRE_TABLE} ON ${INVENTAIRE_TABLE}.INV_Code = ${ProInventoryConstants.LIGNE_INVENTAIRE_TABLE}.LG_INV_Code_Inv " )
    fun counts(): Flow<Map<Inventaire, List<LigneInventaire>>>



    @get:Query("SELECT * FROM $INVENTAIRE_TABLE ORDER BY timestamp DESC")
    val allLive: Flow<List<Inventaire>>

    @get:Query("SELECT * FROM $INVENTAIRE_TABLE where INV_Etat = 'En cours' ORDER BY timestamp DESC")
    val inProgress: Flow<List<Inventaire>>

    @Query("SELECT * FROM $INVENTAIRE_TABLE where INV_Code_Station =:station ORDER BY timestamp DESC")
    fun getByStation(station: String): Flow<List<Inventaire>>

    @Query("SELECT * FROM $INVENTAIRE_TABLE where INV_Code =:code")
    fun getByCode(code: String): Flow<Inventaire>

    @Query("SELECT * FROM $INVENTAIRE_TABLE WHERE isSync=0 and  (Status=:status) ORDER BY timestamp DESC")
    fun getByStatus(status: String): Flow<List<Inventaire>>

    @get:Query("SELECT * FROM $INVENTAIRE_TABLE WHERE isSync=0 ORDER BY timestamp DESC")
    val nonSync: Flow<List<Inventaire>>


    @Query(
        "SELECT * FROM $INVENTAIRE_TABLE " +
                " JOIN ${ProInventoryConstants.LIGNE_INVENTAIRE_TABLE} ON $INVENTAIRE_TABLE.INV_Code = ${ProInventoryConstants.LIGNE_INVENTAIRE_TABLE}.LG_INV_Code_Inv" +

                " WHERE $INVENTAIRE_TABLE.IsSync=0 and  ($INVENTAIRE_TABLE.Status='INSERTED'  or $INVENTAIRE_TABLE.Status='UPDATED') "

    )
    fun noSynced(): Flow<Map<Inventaire, List<LigneInventaire>>>


    @Query("SELECT * FROM Inventaire WHERE (Status=:status) ORDER BY timestamp DESC")
    fun getByStatusForced(status: String): Flow<List<Inventaire>>

   // @Query("UPDATE $INVENTAIRE_TABLE SET Status = 'SELECTED' and INV_Etat = 'SELECTED' and IsSync=1 WHERE  INV_Code= :newId")
  //  fun updateInventaireStatus(newId: String)


    @Query("UPDATE $INVENTAIRE_TABLE SET INV_Code = :code , Status = 'SELECTED' , IsSync = 1 ,INV_Etat = :etat where INV_Code_M = :codeLocal")
    fun updateNumAndEtat(code: String, codeLocal : String, etat: String)


    @Query("UPDATE $INVENTAIRE_TABLE SET Status= 'INSERTED' where INV_Code_M = :codeM")
    fun setToInserted(codeM: String)

    @Query("SELECT ifnull(MAX(cast(substr(INV_Code,length(:prefix) + 1 ,length('INV_Code'))as integer)),0)+1 FROM   $INVENTAIRE_TABLE WHERE substr(INV_Code, 0 ,length(:prefix)+1) = :prefix")
    fun getNewCode(prefix: String): Flow<String>



    @get:Query("SELECT COUNT(*) FROM $INVENTAIRE_TABLE  where  isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val nonSyncCount: Flow<Int>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: Inventaire)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<Inventaire>)

    @Query("DELETE FROM Inventaire")
    fun deleteAll()

    @Query("DELETE FROM $INVENTAIRE_TABLE where isSync=1")
    fun deleteOnlyOnline()

    @Delete
    fun delete(inventaire: Inventaire)



    @Query(
        "SELECT * FROM $INVENTAIRE_TABLE " +
                "LEFT JOIN ${ProInventoryConstants.LIGNE_INVENTAIRE_TABLE} ON ${INVENTAIRE_TABLE}.INV_Code = ${ProInventoryConstants.LIGNE_INVENTAIRE_TABLE}.LG_INV_Code_Inv" +
                " WHERE ${INVENTAIRE_TABLE}.INV_Code LIKE '%' || :searchString || '%' " +
                " and  CASE WHEN :filterByEtatInv !=  '' THEN INV_Etat =:filterByEtatInv ELSE INV_Etat !=:filterByEtatInv END " +
                

                " ORDER BY " +
                "CASE WHEN :sortBy = 'INV_Code'  AND :isAsc = 1 THEN INV_Code END ASC, " +
                "CASE WHEN :sortBy = 'INV_Code'  AND :isAsc = 2 THEN INV_Code END DESC, "+
                "CASE WHEN :sortBy = 'INV_Date'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',INV_Date) END ASC, " +
                "CASE WHEN :sortBy = 'INV_Date'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',INV_Date) END DESC "
    )
    fun filterByInvCode(searchString: String, filterByEtatInv: String, sortBy: String, isAsc: Int): Flow<Map<Inventaire, List<LigneInventaire>>>


    @Query(
        "SELECT * FROM $INVENTAIRE_TABLE " +
               "LEFT JOIN ${ProInventoryConstants.LIGNE_INVENTAIRE_TABLE} ON ${INVENTAIRE_TABLE}.INV_Code = ${ProInventoryConstants.LIGNE_INVENTAIRE_TABLE}.LG_INV_Code_Inv " +
                " WHERE  CASE WHEN :filterByEtatInv !=  '' THEN INV_Etat =:filterByEtatInv ELSE INV_Etat !=:filterByEtatInv END " +


                " ORDER BY " +
                "CASE WHEN :sortBy = 'INV_Code'  AND :isAsc = 1 THEN INV_Code END ASC, " +
                "CASE WHEN :sortBy = 'INV_Code'  AND :isAsc = 2 THEN INV_Code END DESC, "+
                "CASE WHEN :sortBy = 'INV_Date'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',INV_Date) END ASC, " +
                "CASE WHEN :sortBy = 'INV_Date'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',INV_Date) END DESC "
    )
    fun getAllFiltred(isAsc: Int, filterByEtatInv: String, sortBy: String): Flow<Map<Inventaire, List<LigneInventaire>>>

}
