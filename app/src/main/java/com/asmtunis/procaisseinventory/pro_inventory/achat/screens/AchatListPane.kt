package com.asmtunis.procaisseinventory.pro_inventory.achat.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Done
import androidx.compose.material.icons.twotone.ArrowDropDown
import androidx.compose.material.icons.twotone.ArrowDropUp
import androidx.compose.material3.AssistChipDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DrawerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals.THIS_MONTH
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothInfoDialogue
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.InventoryAchatDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.InventoryAddAchatRoute
import com.asmtunis.procaisseinventory.core.print.PrintFunctions
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_inventory.achat.AchatViewModel
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.BonEntree
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.LigneBonEntree
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.bottom_sheet.CustomModalBottomSheet
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.floating_button.SnapScrollingButton
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.ListItem
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyColumn
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.ToasterState
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.ModifiersUtils.floatingBtnIsVisible
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AchatListPane(
    navigate: (route: Any) -> Unit,
    drawer: DrawerState,
    navDrawerViewmodel: NavigationDrawerViewModel,
    achatViewModel: AchatViewModel,
    barCodeViewModel: BarCodeViewModel,
    mainViewModel: MainViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    printViewModel: PrintViewModel,
    bluetoothVM: BluetoothViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    selectArtCalculVM: SelectArticleCalculViewModel,
    settingViewModel: SettingViewModel,
    syncInventoryViewModel: SyncInventoryViewModel
) {
    val uiWindowState = settingViewModel.uiWindowState

    val isLoadingFromLocalDb = achatViewModel.isLoadingFromLocalDb
    val selectedBonEntree = achatViewModel.selectedBonEntree
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val achatListstate = achatViewModel.achatListstate
    val listFilter = achatListstate.search
    val listOrder = achatListstate.listOrder
    val filterList = context.resources.getStringArray(R.array.achat_filter)
    val bonEntreeState = getProInventoryDataViewModel.bonEntreeState
    val ligneBonEntreeState = getProInventoryDataViewModel.ligneBonEntreeState
    val utilisateur = mainViewModel.utilisateur
    val isConnected = networkViewModel.isConnected
    val stationList = mainViewModel.stationList

    val articleMapByBarCode = mainViewModel.articleMapByBarCode

    val printParams = dataViewModel.printData

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)


    val showSearchView = achatViewModel.showSearchView
    val listState = rememberLazyListState()
    val selectedMonthNumber = if(achatViewModel.selectedMonthNumber == THIS_MONTH) "1" else achatViewModel.selectedMonthNumber
    val isVisible = floatingBtnIsVisible(
        listeSize = achatListstate.lists.size,
        canScrollForward = listState.canScrollForward
    )

//    LaunchedEffect(
//        key1 = achatViewModel.searchTextState.text,
//        key2 = achatListstate.lists,
//        key3 = achatListstate.filterByStationEntree
//    ) {
//        achatViewModel.filterAchat(achatListstate)
//    }

    LaunchedEffect(
        key1 = Unit
    ) {
        achatViewModel.filterAchat(achatListstate)

        if(printViewModel.proccedPrinting && printViewModel.deviceAddress.isNotEmpty()) {
            PrintFunctions.print(
                context = context,
                toaster = toaster,
                navigate = { navigate(it) },
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                printParams = printParams,
                toPrintBT = {
                    printViewModel.printBonAchat(
                        context = context,
                        lgBonEntree = achatViewModel.selectedListLgBonEntree,
                        bonEntree = selectedBonEntree,
                        articleMapByBarCode = articleMapByBarCode,
                        printParams = printParams,
                        station = mainViewModel.stationList.first { it.sTATCode == selectedBonEntree.bONENTStationEntree },
                        fournisseur = mainViewModel.fournisseurList.first { it.fRSCodef == selectedBonEntree.bONENTCodeFrs }
                    )
                }
            )
        }
        achatViewModel.onSelectedMonthsNbrChange(dataViewModel.getInventoryBnEntreSelectedMonthsNbr(default = THIS_MONTH))
    }

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = { scope.launch { drawer.open() } },
                showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                title = stringResource(id = navDrawerViewmodel.proInventorySelectedMenu.title),
                titleVisibilty = !showSearchView,
                actions = {
                    SearchSectionComposable(
                        label = context.getString(
                            R.string.filter_by,
                            when (listFilter) {
                                is ListSearch.FirstSearch -> filterList[0]
                                is ListSearch.SecondSearch -> filterList[1]
                                else -> filterList[2]
                            }
                        ),
                        searchVisibility = showSearchView,
                        searchTextState = achatViewModel.searchTextState,
                        onSearchValueChange = {
                            achatViewModel.onSearchValueChange(TextFieldValue(it))
                        },
                        onShowSearchViewChange = {
                            achatViewModel.onShowSearchViewChange(it)
                        },
                        onShowCustomFilterChange = {
                            achatViewModel.onShowCustomFilterChange(it)
                        }
                    )

                },
            )
        },

        floatingActionButton = {
            val density = LocalDensity.current
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp) ,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                AnimatedVisibility(
                    visible = isVisible && articleMapByBarCode.isNotEmpty(),
                    enter = slideInVertically {
                        with(density) { 40.dp.roundToPx() }
                    } + fadeIn(),
                    exit = fadeOut(
                        animationSpec = keyframes {
                            this.durationMillis = 120
                        }
                    )
                ) {
                    FloatingActionButton(
                        onClick = {
                            val prefix = mainViewModel.prefixList.firstOrNull { it.pREIdTable == "bon_entree" }?.pREPrefixe ?: "BonAchat_M_"
                            achatViewModel.generateBonEntreeNum(prefix)


                            mainViewModel.generateCodeM(
                                utilisateur = utilisateur,
                                prefix = prefix
                            )

                            selectArtCalculVM.onShowTvaChange(true)
                            selectArtCalculVM.setControlQte(false)


                            //  inventoryTextValidationViewModel.resetVariable()
                            achatViewModel.restBonEntree()
                            barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                            selectArtCalculVM.resetSelectedMobilityArticles()

                            navigate(InventoryAddAchatRoute)

                        }) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = stringResource(id = R.string.cd_achat_button)
                        )
                    }
                }


                SnapScrollingButton(
                    isScrollInProgress = listState.isScrollInProgress,
                    isVisible = remember { derivedStateOf { listState.firstVisibleItemIndex } }.value > 15 && isVisible,
                    density = density,
                    animateScrollToItem = {
                        listState.animateScrollToItem(index = it)
                    }
                )
            }

        }
    ) { padding ->
        if(printViewModel.openPrintInfoDialogue) {
            BluetoothInfoDialogue(
                printResult = printViewModel.printResult,
                onOpenPrintInfoDialogueChange = {
                    printViewModel.onOpenPrintInfoDialogueChange(it)
                }
            )
        }


        if (achatViewModel.showCustomModalBottomSheet) {
            CustomModalBottomSheet(
                title = selectedBonEntree.bONENTNum,
                status = selectedBonEntree.status,
                remoteResponseState = syncInventoryViewModel.responseAddAchatState,
                showDeleteIcon = !selectedBonEntree.isSync,
                onDismissRequest = {
                    achatViewModel.onShowCustomModalBottomSheetChange(false)
                },
                onDeleteRequest = {

                    achatViewModel.deleteBonEntree(
                        bonEntree = selectedBonEntree,
                        listLgBonEntree = achatViewModel.selectedListLgBonEntree,
                        articleMapByBarCode = articleMapByBarCode,
                        allStationStockArticle = mainViewModel.stationStockArticlMapByBarCode,
                        updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                            mainViewModel.updateQtePerStation(newQteStation = newQteStation, newSartQteDeclare = newSartQteDeclare, codeArticle = codeArticle, codeStation = codeStation)
                        },
                        updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                            mainViewModel.updateArtQteStock(
                                newQteAllStations = newQteAllStations,
                                newQteStation = newQteStation,
                                codeArticle = codeArticle
                            )
                        }
                    )
                },
                onPrintRequest = {

                    PrintFunctions.print(
                        context = context,
                        toaster = toaster,
                        navigate = { navigate(it) },
                        printViewModel = printViewModel,
                        bluetoothVM = bluetoothVM,
                        printParams = printParams,
                        toPrintBT = {
                            printViewModel.printBonAchat(
                                context = context,
                                lgBonEntree = achatViewModel.selectedListLgBonEntree,
                                bonEntree = selectedBonEntree,
                                articleMapByBarCode = articleMapByBarCode,
                                printParams = printParams,
                                station = mainViewModel.stationList.first { it.sTATCode == selectedBonEntree.bONENTStationEntree },
                                fournisseur = mainViewModel.fournisseurList.first { it.fRSCodef == selectedBonEntree.bONENTCodeFrs }
                            )
                        }
                    )

                },
                onSyncRequest = {
                    syncInventoryViewModel.syncAchat(selectedBnEntree = selectedBonEntree)
                }
            )
        }

        if (achatViewModel.showCustomFilter) {
            FilterContainer(
                filterList = filterList,
                listFilter = listFilter,
                listOrder = listOrder,
                orderList = context.resources.getStringArray(R.array.achat_order),
                onShowCustomFilterChange = {
                    achatViewModel.onShowCustomFilterChange(false)
                },
                onEvent = {
                    achatViewModel.onEvent(event = it)
                },
                customFilterContent = {
                    FilterSectionComposable(
                        title = stringResource(R.string.filter_by_station),
                        currentFilterLable = achatListstate.filterByStationEntree,
                        onAllEvent = {
                            achatViewModel.onEvent(ListEvent.FirstCustomFilter(""))
                        },
                        onEvent = {
                            achatViewModel.onEvent(ListEvent.FirstCustomFilter(mainViewModel.stationList[it].sTATCode))
                        },

                        filterCount = mainViewModel.stationList.size,
                        customFilterCode = {
                            mainViewModel.stationList[it].sTATCode
                        },
                        filterLabel = {
                            mainViewModel.stationList[it].sTATDesg
                        }
                    )

                    FilterSectionComposable(
                        title = stringResource(R.string.filter_by_fournisseur),
                        currentFilterLable = achatListstate.filterByFournisseur,
                        onAllEvent = {
                            achatViewModel.onEvent(ListEvent.SecondCustomFilter(""))
                        },
                        onEvent = {
                            achatViewModel.onEvent(
                                ListEvent.SecondCustomFilter(
                                    mainViewModel.fournisseurList[it].fRSCodef
                                )
                            )
                        },

                        filterCount = mainViewModel.fournisseurList.size,
                        customFilterCode = {
                            mainViewModel.fournisseurList[it].fRSCodef
                        },
                        filterLabel = {
                            mainViewModel.fournisseurList[it].fRSNomf
                        }
                    )

                    var showNombreMoisSection by rememberSaveable { mutableStateOf(false) }
                    val numbers = listOf(THIS_MONTH) + (1..12).map { it.toString() }.toList()

                    Spacer(modifier = Modifier.height(18.dp))


                    Row(
                        modifier = Modifier.fillMaxWidth().clickable {
                            showNombreMoisSection = !showNombreMoisSection
                        },
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = stringResource(R.string.nombreDesMois, selectedMonthNumber),
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.outline,
                            maxLines = 1
                        )

                        Spacer(modifier = Modifier.weight(1F))
                        Icon(
                            imageVector = if(showNombreMoisSection) Icons.TwoTone.ArrowDropUp else Icons.TwoTone.ArrowDropDown,
                            contentDescription = stringResource(
                                id = R.string.cd_toggle_drawer

                            )
                        )
                        Spacer(modifier = Modifier.padding(end = 12.dp))
                    }

                    val density = LocalDensity.current
                    AnimatedVisibility(
                        visible = showNombreMoisSection,
                        enter = slideInVertically { with(density) { 40.dp.roundToPx() } } + fadeIn(),
                        exit = fadeOut(animationSpec = keyframes { this.durationMillis = 120 })
                    ) {
                        val listStat = rememberLazyListState(
                            initialFirstVisibleItemIndex = numbers.indexOf(selectedMonthNumber),
                            initialFirstVisibleItemScrollOffset = 0
                        )

                        /*   BoxWithConstraints(
                               modifier = Modifier.fillMaxWidth()
                           ) {*/
                        //  val halfRowWidth = constraints.maxWidth / 2
                        LazyRow (
                            state = listStat
                        ) {
                            items( count = numbers.size,
                                key = {
                                    numbers[it]
                                }) { index ->


                                /*   val opacity by remember {
                                        derivedStateOf {
                                            val currentItemInfo = listStat.layoutInfo.visibleItemsInfo
                                                .firstOrNull { it.index == index }
                                                ?: return@derivedStateOf 0.5f
                                            val itemHalfSize = currentItemInfo.size / 2
                                            (1f - minOf(1f, abs(currentItemInfo.offset + itemHalfSize - halfRowWidth).toFloat() / halfRowWidth) * 0.5f)
                                        }
                                    }*/




                                FilterChip(
                                    //  modifier = Modifier
                                    // .scale(opacity)
                                    // .alpha(opacity),
                                    selected = selectedMonthNumber == numbers[index],
                                    onClick = {
                                        achatViewModel.onShowCustomFilterChange(false)
                                        achatViewModel.onSelectedMonthsNbrChange(numbers[index])
                                        dataViewModel.saveInventoryBnEntreSelectedMonthsNbr(numbers[index])



                                        getProInventoryDataViewModel.getBonEntree(
                                            baseConfig= selectedBaseconfig,
                                            nbrMois = if(numbers[index] == THIS_MONTH) "" else numbers[index]
                                        )

                                        getProInventoryDataViewModel.getLigneBonEntree(
                                            baseConfig= selectedBaseconfig,
                                            nbrMois = if(numbers[index] == THIS_MONTH) "" else numbers[index]
                                        )
                                    },
                                    label = { Text(numbers[index]) },
                                    leadingIcon = {
                                        AnimatedVisibility(visible = selectedMonthNumber == numbers[index]) {
                                            Icon(
                                                Icons.Filled.Done,
                                                contentDescription = numbers[index],
                                                Modifier.size(AssistChipDefaults.IconSize)
                                            )
                                        }
                                    }
                                )
                                Spacer(modifier = Modifier.width(12.dp)
                                    //   .scale(opacity)
                                    // .alpha(opacity)
                                )


                            }
                        }
                        // }

                    }

                }
            )
        }

        Column(
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {


            if(bonEntreeState.loading || ligneBonEntreeState.loading) {
                LottieAnim(lotti = R.raw.loading, size = 250.dp)
            }
            else if(bonEntreeState.error != null || ligneBonEntreeState.error != null) {
                LottieAnim(lotti = R.raw.network_error, size = 250.dp)
                Spacer(modifier = Modifier.height(12.dp))
                Text(text = bonEntreeState.error?: "Bon Entree error")
                Text(text = ligneBonEntreeState.error?: "Ligne Bon Entree error")
                Spacer(modifier = Modifier.height(12.dp))
                OutlinedButton(onClick = {  getProInventoryDataViewModel.getBonEntree(baseConfig = selectedBaseconfig, nbrMois = selectedMonthNumber)
                    getProInventoryDataViewModel.getLigneBonEntree(baseConfig = selectedBaseconfig, nbrMois = selectedMonthNumber)
                }) {
                    Text(text = "Retry")
                }
            }
            else {
                if (achatListstate.lists.isNotEmpty()) {
                    AchatList(
                        navigate = { navigate(it) },
                        toaster = toaster,
                        isConnected = isConnected,
                        selectedBaseconfig = selectedBaseconfig,
                        selectedMonthNumber = selectedMonthNumber,
                        selectedBonEntree = selectedBonEntree,
                        achatViewModel = achatViewModel,
                        mainViewModel = mainViewModel,
                        barCodeViewModel = barCodeViewModel,
                        filteredList = achatListstate.lists,
                        listState = listState,
                        selectArtCalculVM = selectArtCalculVM,
                        getProInventoryDataViewModel = getProInventoryDataViewModel
                    )
                }
                else if(isLoadingFromLocalDb) CircularProgressIndicator()
                else {
                    LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
                    Spacer(modifier = Modifier.height(12.dp))
                    OutlinedButton(onClick = {  getProInventoryDataViewModel.getBonEntree(baseConfig = selectedBaseconfig, nbrMois = selectedMonthNumber)
                        getProInventoryDataViewModel.getLigneBonEntree(baseConfig = selectedBaseconfig, nbrMois = selectedMonthNumber)
                    }) {
                        Text(text = "Retry")
                    }
                }
            }


        }
    }
}

@Composable
fun AchatList(
    navigate: (route: Any) -> Unit,
    toaster: ToasterState,
    isConnected: Boolean,
    selectedBaseconfig: BaseConfig,
    selectedMonthNumber: String,
    selectedBonEntree: BonEntree,
    achatViewModel: AchatViewModel,
    barCodeViewModel: BarCodeViewModel,
    listState: LazyListState,
    mainViewModel: MainViewModel,
    selectArtCalculVM: SelectArticleCalculViewModel,
    filteredList: Map<BonEntree, List<LigneBonEntree>>,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
) {
    val context = LocalContext.current
    val fournisseurList = mainViewModel.fournisseurList

    val bonEntree: MutableList<BonEntree> = arrayListOf()
    val ligneBonEntree: MutableList<LigneBonEntree> = arrayListOf()
    filteredList.forEach { (key, value) ->
        run {
            bonEntree.add(key)
            ligneBonEntree.addAll(value)
        }
    }

    val isRefreshing  = getProInventoryDataViewModel.bonEntreeState.loading || getProInventoryDataViewModel.ligneBonEntreeState.loading

    PullToRefreshLazyColumn(
        items = bonEntree,
        lazyListState = listState,
        isRefreshing = isRefreshing,
        pullToRefreshEnabled = !bonEntree.any { !it.isSync } && isConnected,
        onRefresh = {
            getProInventoryDataViewModel.getBonEntree(baseConfig = selectedBaseconfig, nbrMois = selectedMonthNumber)
            getProInventoryDataViewModel.getLigneBonEntree(baseConfig = selectedBaseconfig, nbrMois = selectedMonthNumber)

        },
        key = {bonEntre ->
            bonEntre.id
        },
        content = { bonEntre ->
            ListItem(
                isSelected = selectedBonEntree == bonEntre,
                onItemClick = {
                    achatViewModel.restBonEntree()
                    barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                    //  inventoryTextValidationViewModel.resetVariable()
                    selectArtCalculVM.resetSelectedMobilityArticles()
                    selectArtCalculVM.onShowTvaChange(true)

                    val bonEntreeWithLignes = filteredList.filter { it.key == bonEntre }
                    achatViewModel.onSelectedBonEntreeChange(bonEntreeWithLignes)


                    achatViewModel.setSelectedArticleList(
                        listLigneBonEntree = bonEntreeWithLignes.values.flatten(),
                        tvaList = mainViewModel.tvaList,
                        articleMap = mainViewModel.articleMapByBarCode,
                        setConsultationSelectedArticleMobilityList = {
                            selectArtCalculVM.setConsultationSelectedArticleMobilityList(
                                selectedArticle = it,
                               /* article = it.article,
                                tva = it.tva,
                                mntTva = it.mntTva,
                                quantity = it.quantity,
                                prixCaisse = it.prixCaisse,
                                discount = it.discount,
                                mntDiscount = it.mntDiscount,
                                lTMtTTC = it.lTMtTTC,
                                lTMtHT = it.lTMtBrutHT,
                                prixVente = it.prixVente*/
                            )
                        }
                    )
                    navigate(InventoryAchatDetailRoute)

                },
                firstText = bonEntre.bONENTNum,
                secondText = stringResource(R.string.fournisseur_value, fournisseurList.firstOrNull { it.fRSCodef == bonEntre.bONENTCodeFrs }?.fRSNomf?: bonEntre.bONENTCodeFrs),
                thirdText =  convertStringToPriceFormat(bonEntre.bONENTMntTTC),
                // forthText = " : " + bonEntree[index].,
                dateText = bonEntre.bONENTDateFormatted,
                dateTextRemarque = StringUtils.stringPlural(
                    nbr = filteredList[bonEntre]?.size?:0,
                    single = stringResource(id = R.string.article_title),
                    plural = stringResource(id = R.string.article_field_title)
                ),
                isSync = bonEntre.isSync,
                status = bonEntre.status,
                onMoreClick = {
                    achatViewModel.onSelectedBonEntreeChange(filteredList.filter { it.key == bonEntre })
                    achatViewModel.onShowCustomModalBottomSheetChange(true)
                }
            )
        },
    )


}