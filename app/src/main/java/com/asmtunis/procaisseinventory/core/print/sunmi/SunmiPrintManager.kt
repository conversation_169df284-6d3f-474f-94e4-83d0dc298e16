package com.asmtunis.procaisseinventory.core.print.sunmi

import android.content.Context
import android.graphics.BitmapFactory
import android.util.Base64
import android.util.Log
import android.widget.Toast
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.print.PrintingData
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.etablisement.domaine.Etablisement
import com.asmtunis.procaisseinventory.data.parametrages.domaine.Parametrages
import com.asmtunis.procaisseinventory.data.prefixe.domaine.Prefixe
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommandeWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommandeWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicketWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetourWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetourWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisseWithTicketAndClient
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.google.android.datatransport.BuildConfig
import com.sunmi.printerx.PrinterSdk
import com.sunmi.printerx.enums.Align
import com.sunmi.printerx.enums.DividingLine
import com.sunmi.printerx.style.BaseStyle
import com.sunmi.printerx.style.TextStyle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SunmiPrintManager @Inject constructor(
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val proInventoryLocalDb: ProInventoryLocalDb
) : ViewModel() {

    private val _printerStatus = MutableStateFlow<String>("")
    val printerStatus: StateFlow<String> = _printerStatus.asStateFlow()

    private val _isPrinterReady = MutableStateFlow<Boolean>(false)
    val isPrinterReady: StateFlow<Boolean> = _isPrinterReady.asStateFlow()

    // Properties for establishment and logo information
    private var etablisement: Etablisement = Etablisement()
    private var parametrage: Parametrages = Parametrages()
    private var logo: String = ""

    private var printer: PrinterSdk.Printer? = null

    // Variable to store the ticket note
    private var _ticketNote: String = ""

    init {
        // Load establishment and parametrage data
        loadEtablisementData()
        loadParametrageData()
    }

    private fun loadEtablisementData() {
        viewModelScope.launch {
            try {
                val etablisements = proCaisseLocalDb.etablisement.getAll().firstOrNull()
                if (!etablisements.isNullOrEmpty()) {
                    etablisement = etablisements.first()
                    Log.d("SunmiPrintManager", "Loaded establishment data: ${etablisement.desgEt}")
                }
            } catch (e: Exception) {
                Log.e("SunmiPrintManager", "Error loading establishment data: ${e.message}")
            }
        }
    }

    private fun loadParametrageData() {
        viewModelScope.launch {
            try {
                val params = proInventoryLocalDb.parametrage.getOne().firstOrNull()
                if (params != null) {
                    parametrage = params
                    logo = parametrage.pARAMLogo ?: ""
                    Log.d("SunmiPrintManager", "Loaded parametrage data, logo size: ${logo.length}")
                }
            } catch (e: Exception) {
                Log.e("SunmiPrintManager", "Error loading parametrage data: ${e.message}")
            }
        }
    }

    // Global variable to store the printer instance
    companion object {
        var selectPrinter: PrinterSdk.Printer? = null
        const val PAPER_WIDTH_80MM = "80mm"
        const val PAPER_WIDTH_58MM = "58mm"
        var defaultPaperWidth = PAPER_WIDTH_80MM // Default to 80mm
    }

    // We're using the SDK's DividingLine enum directly
    // import com.sunmi.printerx.enums.DividingLine

    // Method to set the ticket note
    fun setTicketNote(note: String) {
        _ticketNote = note
        Log.d("SunmiPrintManager", "Note set: '$note'")
    }

    fun initPrinter(context: Context, onReady: (() -> Unit)? = null) {
        Log.d("SunmiPrintManager", "Initializing Sunmi printer with context: $context")

        try {
            // Enable SDK logging for debugging
            PrinterSdk.getInstance().log(true, null)
            Log.d("SunmiPrintManager", "SDK logging enabled")

            // Initialize the Sunmi Printer SDK
            PrinterSdk.getInstance().getPrinter(context, object : PrinterSdk.PrinterListen {
                override fun onDefPrinter(printer: PrinterSdk.Printer?) {
                    Log.d("SunmiPrintManager", "onDefPrinter called with printer: $printer")
                    <EMAIL> = printer
                    selectPrinter = printer  // Set the global printer instance
                    _isPrinterReady.value = printer != null

                    if (printer != null) {
                        Log.d("SunmiPrintManager", "Printer connected successfully")
                        viewModelScope.launch(Dispatchers.IO) {
                            try {
                                val status = printer.queryApi().status.name
                                Log.d("SunmiPrintManager", "Printer status: $status")
                                _printerStatus.value = "Status: $status"
                                val name = printer.queryApi().getInfo(com.sunmi.printerx.enums.PrinterInfo.NAME)
                                val type = printer.queryApi().getInfo(com.sunmi.printerx.enums.PrinterInfo.TYPE)
                                val paper = printer.queryApi().getInfo(com.sunmi.printerx.enums.PrinterInfo.PAPER)

                                Log.d("SunmiPrintManager", "Printer status: $status")
                                Log.d("SunmiPrintManager", "Printer name: $name")
                                Log.d("SunmiPrintManager", "Printer type: $type")
                                Log.d("SunmiPrintManager", "Printer paper: $paper")

                                // Set the paper width to 80mm
                                setPaperWidth(printer, PAPER_WIDTH_80MM)

                                _printerStatus.value = "Connected to $name ($type)"
                            } catch (e: Exception) {
                                Log.e("SunmiPrintManager", "Error getting printer info: ${e.message}")
                                _printerStatus.value = "Error: ${e.message}"
                            }
                        }
                        Log.d("SunmiPrintManager", "Calling onReady callback")
                        onReady?.invoke()
                    } else {
                        Log.e("SunmiPrintManager", "No printer found")
                        _printerStatus.value = "No printer found"
                        // Still call onReady even if printer is null
                        Log.d("SunmiPrintManager", "Calling onReady callback even though printer is null")
                        onReady?.invoke()
                    }
                }

                override fun onPrinters(printers: MutableList<PrinterSdk.Printer>?) {
                    Log.d("SunmiPrintManager", "onPrinters called with printers: ${printers?.size ?: 0}")
                    printers?.forEach { p ->
                        viewModelScope.launch(Dispatchers.IO) {
                            try {
                                val name = p.queryApi().getInfo(com.sunmi.printerx.enums.PrinterInfo.NAME)
                                Log.d("SunmiPrintManager", "Printer: $name")
                            } catch (e: Exception) {
                                Log.e("SunmiPrintManager", "Error getting printer name: ${e.message}")
                            }
                        }
                    }
                }
            })
            Log.d("SunmiPrintManager", "getPrinter called successfully")
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error initializing printer: ${e.message}")
            e.printStackTrace()
            // Still call onReady even if there's an error
            Log.d("SunmiPrintManager", "Calling onReady callback after error")
            onReady?.invoke()
        }
    }

    suspend fun ensurePrinterReady(context: Context): Boolean {
        if (_isPrinterReady.value) return true
        var ready = false
        val lock = java.lang.Object()
        initPrinter(context) {
            synchronized(lock) {
                ready = true
                lock.notify()
            }
        }
        synchronized(lock) {
            if (!ready) lock.wait(3000) // wait up to 3 seconds
        }
        return _isPrinterReady.value
    }

    fun printTicket(
        context: Context,
        ticketWithFactureAndPayments: TicketWithFactureAndPayments,
        listLigneTicket: List<LigneTicketWithArticle>,
        utilisateur: Utilisateur,
        printParams: PrintingData,
        prefixList: List<Prefixe>
    ) {
        Log.d("SunmiPrintManager", "Printing ticket using Sunmi printer with context: $context")
        Log.d("SunmiPrintManager", "Current printer: $printer, selectPrinter: $selectPrinter")
        Log.d("SunmiPrintManager", "printTicket called with note: '$_ticketNote', enableNotes: ${printParams.enableNotes}")

        // If printer is not ready, initialize and retry after ready
        if (printer == null && selectPrinter == null) {
            Log.e("SunmiPrintManager", "Printer not initialized, initializing now...")
            Toast.makeText(context, "Initializing Sunmi printer...", Toast.LENGTH_SHORT).show()
            initPrinter(context) {
                Log.d("SunmiPrintManager", "Printer initialized in callback, now printing ticket")
                Log.d("SunmiPrintManager", "Printer initialization callback, retrying print")
                // Retry print after printer is ready
                printTicket(context, ticketWithFactureAndPayments, listLigneTicket, utilisateur, printParams, prefixList)
            }
            return
        }

        viewModelScope.launch(Dispatchers.IO) {
            try {
                // Use the global printer instance if available
                val printerToUse = selectPrinter ?: printer
                Log.d("SunmiPrintManager", "Using printer: $printerToUse")

                if (printerToUse == null) {
                    Log.e("SunmiPrintManager", "No printer available after initialization")
                    return@launch
                }

                // Check printer status before printing
                try {
                    val status = printerToUse.queryApi().status.name
                    Log.d("SunmiPrintManager", "Printer status before printing: $status")
                } catch (e: Exception) {
                    Log.e("SunmiPrintManager", "Error getting printer status: ${e.message}")
                }

                // Enable transaction mode for monitoring print results
                try {
                    printerToUse.lineApi()?.enableTransMode(true)
                    Log.d("SunmiPrintManager", "Transaction mode enabled")
                } catch (e: Exception) {
                    Log.e("SunmiPrintManager", "Error enabling transaction mode: ${e.message}")
                }

                // Get the printer's line API
                val lineApi = printerToUse.lineApi()
                if (lineApi == null) {
                    Log.e("SunmiPrintManager", "Line API not available")
                    return@launch
                }
                Log.d("SunmiPrintManager", "Line API obtained successfully")

                val ticket = ticketWithFactureAndPayments.ticket
                val client = ticketWithFactureAndPayments.client

                // Set paper width to 80mm
                setPaperWidth(printerToUse, PAPER_WIDTH_80MM)

                // Print header with larger text for 80mm paper
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.addText("******", TextStyle.getStyle())
                lineApi.addText("BON LIVRAISON", TextStyle.getStyle().enableBold(true).setTextWidthRatio(1).setTextHeightRatio(1))
                lineApi.printText("******", TextStyle.getStyle())

                // Print ticket number
                val prefixeBl = prefixList.firstOrNull { it.pREIdTable == "Bon_livraison" }?.pREPrefixe ?: "BL_M_"
                var numFact = ""
                if (!ticket?.tIKNumeroBL.isNullOrBlank() || !ticket?.tIKNumFact.isNullOrEmpty()) {
                    if (!ticket?.tIKNumeroBL.isNullOrBlank() &&
                        !ticket.tIKNumeroBL.contains("BL_M") &&
                        !ticket.tIKNumeroBL.contains(prefixeBl)) {
                        numFact = ticket.tIKNumeroBL
                    }
                }

                if (numFact.isNotEmpty()) {
                    lineApi.printText("Num: $numFact", TextStyle.getStyle().setTextSize(21))
                } else {
                    lineApi.printText("Num: ${ticket?.tIKNumTicket ?: ""}", TextStyle.getStyle().setTextSize(21))
                }

                // Print date centered
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("Date: ${ticket?.tIKDateHeureTicket?.substringBefore(".") ?: ""}", TextStyle.getStyle().setTextSize(21))

                // Print logo
                printLogo(lineApi, printParams)

                // Print establishment info
                printEstablishmentInfo(lineApi, printParams)

                // Print client info
                Log.d("SunmiPrintManager", "Calling printClientInfo with note: '$_ticketNote'")
                printClientInfo(lineApi, client, ticket?.tIKNomClient, ticket?.tIKCodClt, ticket?.tIKEmplacementMariage ?: "", _ticketNote, printParams)

                // Print operator info
                printOperatorInfo(lineApi, ticket?.tIKIdSCaisse, utilisateur)

                // Print table header
                lineApi.initLine(BaseStyle.getStyle())
                val headerTextStyle = TextStyle.getStyle().enableBold(true)

                // Three column header without TOTAL HT
                lineApi.printTexts(
                    arrayOf("QTE", "P.U", "TOTAL"),
                    intArrayOf(1, 1, 1),
                    arrayOf(
                        headerTextStyle.setAlign(Align.LEFT),
                        headerTextStyle.setAlign(Align.CENTER),
                        headerTextStyle.setAlign(Align.RIGHT)
                    )
                )

                // Print dividing line
                printDividingLine(lineApi, DividingLine.SOLID, 1)

                // Print items with larger text and more details for 80mm paper
                for (item in listLigneTicket) {
                    // Get discount information
                    val tauxRemise = formatQuantity(item.ligneTicket?.lTRemise ?: "0")
                    val hasDiscount = stringToDouble(tauxRemise) > 0.0

                    // Print item designation with discount info if applicable
                    val designation = "${item.article?.aRTDesignation ?: ""}" +
                                     (if (hasDiscount) " (-$tauxRemise%)" else "")

                    lineApi.printText(designation, TextStyle.getStyle().setTextSize(21).enableBold(true))

                    // Prepare item details
                    val qte = formatQuantity(item.ligneTicket?.lTQte ?: "")
                    val prix = formatPrice(item.ligneTicket?.lTPrixVente ?: "")
                    val total = formatPrice(item.ligneTicket?.lTMtTTC ?: "")
                    val textStyle = TextStyle.getStyle().setTextSize(19)

                    // Print item details with bold TOTAL
                    lineApi.printTexts(
                        arrayOf(qte, prix, total),
                        intArrayOf(1, 1, 1),
                        arrayOf(
                            textStyle.setAlign(Align.LEFT),
                            textStyle.setAlign(Align.CENTER),
                            textStyle.setAlign(Align.RIGHT).enableBold(true)  // Make TOTAL bold
                        )
                    )

                    // Add a small separator between items
                    lineApi.printText("-", TextStyle.getStyle().setTextSize(17))
                }

                printDividingLine(lineApi,DividingLine.EMPTY, 30)
                printDividingLine(lineApi,DividingLine.DOTTED, 2)
                printDividingLine(lineApi,DividingLine.EMPTY, 30)

                // Calculate tax and total amounts
                val facture = ticketWithFactureAndPayments.facture
                val timbValue = if(facture?.factTimbre != null && facture.factTimbre != "0") {
                    facture.factTimbre
                } else {
                    if (ticketWithFactureAndPayments.timbre == null)
                        "0"
                    else ticketWithFactureAndPayments.timbre?.tIMBValue ?: "0"
                }

                // Calculate total HT (excluding tax)
                val ttHt = stringToDouble(ticket?.tIKMtTTC) + stringToDouble(ticket?.tIKMtRemise) - stringToDouble(timbValue) - stringToDouble(ticket?.tIKMtTVA)

                // Calculate net TTC (including tax)
                val netTTC = if(facture != null) stringToDouble(facture.factMntTTC)
                else stringToDouble(ticket?.tIKMtTTC) + stringToDouble(timbValue)

                // Print totals section
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))

                // Print total articles count
                lineApi.printText("Total Articles: ${listLigneTicket.size}",
                                 TextStyle.getStyle().setTextSize(21).enableBold(true))

                // Print total HT
                lineApi.printText("Montant HT: ${formatPrice(ttHt.toString())}",
                                 TextStyle.getStyle().setTextSize(21))

                // Print TVA amount
                lineApi.printText("Montant TVA: ${formatPrice(ticket?.tIKMtTVA)}",
                                 TextStyle.getStyle().setTextSize(21))

                // Print fiscal stamp
                lineApi.printText("Timbre Fiscal: ${formatPrice(timbValue)}",
                                 TextStyle.getStyle().setTextSize(21))

                // Print total TTC
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("Net à Payer: ${formatPrice(ticket?.tIKMtTTC)}",
                                 TextStyle.getStyle().setTextSize(20).enableBold(true))

                // Print payment details if available
                val mntEspece = stringToDouble(ticket?.tIKMtEspece ?: "0")
                val mntCheque = stringToDouble(ticket?.tIKMtCheque ?: "0")
                val mntTraite = stringToDouble(ticket?.tIKMtrecue ?: "0")

                if (mntEspece > 0 || mntCheque > 0 || mntTraite > 0) {
                    printTicketPaymentDetails(lineApi, mntEspece, mntCheque, mntTraite)
                }

                if (mntEspece > 0 || mntCheque > 0 || mntTraite > 0) {
                    lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                    lineApi.printText("- - - -", TextStyle.getStyle().setTextSize(21))

                    lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))
                    if (mntEspece > 0) {
                        lineApi.printText("Espece: ${formatPrice(mntEspece.toString())}", TextStyle.getStyle().setTextSize(21))
                    }

                    if (mntCheque > 0) {
                        lineApi.printText("Cheque: ${formatPrice(mntCheque.toString())}", TextStyle.getStyle().setTextSize(21))
                    }

                    if (mntTraite > 0) {
                        lineApi.printText("Tickets Restaurant: ${formatPrice(mntTraite.toString())}", TextStyle.getStyle().setTextSize(21))
                    }
                }

                // Print footer with client balance and signature boxes
                printFooter(lineApi, client, printParams)

                // Cut paper and output
                lineApi.autoOut()

                // Monitor print results
                printerToUse.lineApi()?.printTrans(object : com.sunmi.printerx.api.PrintResult() {
                    override fun onResult(resultCode: Int, message: String?) {
                        if (resultCode == 0) {
                            Log.d("SunmiPrintManager", "Ticket printed successfully")
                            _printerStatus.value = "Print Success"
                        } else {
                            Log.e("SunmiPrintManager", "Error printing ticket: $message")
                            _printerStatus.value = "Print Error: $message"
                        }
                    }
                })

                // Disable transaction mode
                printerToUse.lineApi()?.enableTransMode(false)

            } catch (e: Exception) {
                Log.e("SunmiPrintManager", "Error printing ticket: ${e.message}")
                _printerStatus.value = "Print Error: ${e.message}"
            }
        }
    }

    fun printBonCommande(
        context: Context,
        bonCommandeWithClient: BonCommandeWithClient,
        lgBonCommandeWithArticle: List<LigneBonCommandeWithArticle>,
        utilisateur: Utilisateur,
        printParams: PrintingData
    ) {
        Log.d("SunmiPrintManager", "Printing bon commande using Sunmi printer")
        Log.d("SunmiPrintManager", "Current printer: $printer, selectPrinter: $selectPrinter")

        // If printer is not ready, initialize and retry after ready
        if (printer == null && selectPrinter == null) {
            Log.e("SunmiPrintManager", "Printer not initialized, initializing now...")
            Toast.makeText(context, "Initializing Sunmi printer...", Toast.LENGTH_SHORT).show()
            initPrinter(context) {
                Log.d("SunmiPrintManager", "Printer initialized in callback, now printing bon commande")
                // Retry print after printer is ready
                printBonCommande(context, bonCommandeWithClient, lgBonCommandeWithArticle, utilisateur, printParams)
            }
            return
        }

        viewModelScope.launch(Dispatchers.IO) {
            try {
                // Use the global printer instance if available
                val printerToUse = selectPrinter ?: printer
                if (printerToUse == null) {
                    Log.e("SunmiPrintManager", "No printer available")
                    return@launch
                }

                // Get the printer's line API
                val lineApi = printerToUse.lineApi()
                if (lineApi == null) {
                    Log.e("SunmiPrintManager", "Line API not available")
                    return@launch
                }

                val bonCommande = bonCommandeWithClient.bonCommande
                val client = bonCommandeWithClient.client

                // Set paper width to 80mm
                setPaperWidth(printerToUse, PAPER_WIDTH_80MM)

                // Print header with larger text for 80mm paper
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.addText("******", TextStyle.getStyle())
                lineApi.addText("BON COMMANDE", TextStyle.getStyle().enableBold(true).setTextWidthRatio(1).setTextHeightRatio(1))
                lineApi.printText("******", TextStyle.getStyle())

                // Print bon commande number
                var num = bonCommande?.dEVNum ?: ""
                if (num.contains("_")) {
                    val numTicket: List<String> = num.split("_")
                    if (numTicket.size >= 6) {
                        num = "DEV_M_" + numTicket[2] + "_" + numTicket[3] + "_" + numTicket[5]
                    }
                }
                lineApi.printText("Num: $num", TextStyle.getStyle().setTextSize(21))

                // Print date centered
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("Date: ${bonCommande?.dEVDate?.substringBefore(".") ?: ""}", TextStyle.getStyle().setTextSize(21))

                // Print logo
                printLogo(lineApi, printParams)

                // Print establishment info
                printEstablishmentInfo(lineApi, printParams)

                // Print client info
                printClientInfo(lineApi, client, bonCommande?.dEVClientName, bonCommande?.dEVCodeClient, "", _ticketNote, printParams)

                // Print operator info
                printOperatorInfo(lineApi, "", utilisateur)

                // Print table header
                lineApi.initLine(BaseStyle.getStyle())
                val headerTextStyle = TextStyle.getStyle().enableBold(true)

                // Three column header without TOTAL HT
                lineApi.printTexts(
                    arrayOf("QTE", "P.U", "TOTAL"),
                    intArrayOf(1, 1, 1),
                    arrayOf(
                        headerTextStyle.setAlign(Align.LEFT),
                        headerTextStyle.setAlign(Align.CENTER),
                        headerTextStyle.setAlign(Align.RIGHT)
                    )
                )

                // Print dividing line
                printDividingLine(lineApi, DividingLine.SOLID, 1)

                // Print items with larger text and more details for 80mm paper
                for (item in lgBonCommandeWithArticle) {
                    // Get discount information
                    val tauxRemise = formatQuantity(item.ligneBonCommande?.lGDEVRemise ?: "0")
                    val hasDiscount = stringToDouble(tauxRemise) > 0.0

                    // Print item designation with discount info if applicable
                    val designation = "${item.article?.aRTDesignation ?: ""}" +
                                     (if (hasDiscount) " (-$tauxRemise%)" else "")

                    lineApi.printText(designation, TextStyle.getStyle().setTextSize(21).enableBold(true))

                    // Prepare item details
                    val qte = formatQuantity(item.ligneBonCommande?.lGDEVQte ?: "")
                    val prix = formatPrice(item.ligneBonCommande?.lGDEVPUTTC ?: "")
                    val total = formatPrice(item.ligneBonCommande?.lGDEVMntTTC ?: "")
                    val textStyle = TextStyle.getStyle().setTextSize(19)

                    // Print item details with bold TOTAL
                    lineApi.printTexts(
                        arrayOf(qte, prix, total),
                        intArrayOf(1, 1, 1),
                        arrayOf(
                            textStyle.setAlign(Align.LEFT),
                            textStyle.setAlign(Align.CENTER),
                            textStyle.setAlign(Align.RIGHT).enableBold(true)  // Make TOTAL bold
                        )
                    )

                    // Add a small separator between items
                    lineApi.printText("-", TextStyle.getStyle().setTextSize(17))
                }
                printDividingLine(lineApi,DividingLine.EMPTY, 30)
                printDividingLine(lineApi,DividingLine.DOTTED, 2)
                printDividingLine(lineApi,DividingLine.EMPTY, 30)

                // Calculate tax and total amounts
                val totalHT = stringToDouble(bonCommande?.dEVMntht ?: "0")
                val totalTVA = stringToDouble(bonCommande?.dEVMntTva ?: "0")
                val totalTTC = stringToDouble(bonCommande?.dEVMntTTC ?: "0")

                // Print totals section
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))

                // Print total articles count
                lineApi.printText("Total Articles: ${lgBonCommandeWithArticle.size}",
                                 TextStyle.getStyle().setTextSize(21).enableBold(true))

                // Print total HT
                lineApi.printText("Montant HT: ${formatPrice(totalHT.toString())}",
                                 TextStyle.getStyle().setTextSize(21))

                // Print TVA amount
                lineApi.printText("Montant TVA: ${formatPrice(totalTVA.toString())}",
                                 TextStyle.getStyle().setTextSize(21))

                // Check if there's a remise (discount)
                val remise = stringToDouble(bonCommande?.dEVRemise ?: "0")
                if (remise > 0) {
                    lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))
                    lineApi.printText("Total Remise: ${formatPrice(bonCommande?.dEVRemise)}",
                                     TextStyle.getStyle().setTextSize(21))
                }

                // Print total TTC
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("Net à Payer: ${formatPrice((totalTTC - remise).toString())}",
                                 TextStyle.getStyle().setTextSize(20).enableBold(true))

                // Print footer with client balance and signature boxes
                printFooter(lineApi, client, printParams)

                // Cut paper and output
                lineApi.autoOut()

                Log.d("SunmiPrintManager", "Bon commande printed successfully")
            } catch (e: Exception) {
                Log.e("SunmiPrintManager", "Error printing bon commande: ${e.message}")
                _printerStatus.value = "Print Error: ${e.message}"
            }
        }
    }

    fun printBonRetour(
        context: Context,
        bonRetourWithClient: BonRetourWithClient,
        lgBonRetourWithArticle: List<LigneBonRetourWithArticle>,
        utilisateur: Utilisateur,
        printParams: PrintingData
    ) {
        Log.d("SunmiPrintManager", "Printing bon retour using Sunmi printer")
        Log.d("SunmiPrintManager", "Current printer: $printer, selectPrinter: $selectPrinter")

        // If printer is not ready, initialize and retry after ready
        if (printer == null && selectPrinter == null) {
            Log.e("SunmiPrintManager", "Printer not initialized, initializing now...")
            Toast.makeText(context, "Initializing Sunmi printer...", Toast.LENGTH_SHORT).show()
            initPrinter(context) {
                Log.d("SunmiPrintManager", "Printer initialized in callback, now printing bon retour")
                // Retry print after printer is ready
                printBonRetour(context, bonRetourWithClient, lgBonRetourWithArticle, utilisateur, printParams)
            }
            return
        }

        viewModelScope.launch(Dispatchers.IO) {
            try {
                // Use the global printer instance if available
                val printerToUse = printer ?: selectPrinter
                if (printerToUse == null) {
                    Log.e("SunmiPrintManager", "No printer available")
                    _printerStatus.value = "No printer available"
                    return@launch
                }

                val bonRetour = bonRetourWithClient.bonRetour ?: return@launch
                val client = bonRetourWithClient.client

                // Set paper width to 80mm
                setPaperWidth(printerToUse, PAPER_WIDTH_80MM)

                // Enable transaction mode for better performance
                printerToUse.lineApi()?.enableTransMode(true)

                // Get the line API for printing
                val lineApi = printerToUse.lineApi() ?: return@launch

                // Print header with larger text for 80mm paper
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.addText("******", TextStyle.getStyle())
                lineApi.addText("BON RETOUR", TextStyle.getStyle().enableBold(true).setTextWidthRatio(1).setTextHeightRatio(1))
                lineApi.printText("******", TextStyle.getStyle())

                // Print bon retour number
                lineApi.printText("Num: ${bonRetour.bORNumero}", TextStyle.getStyle().setTextSize(21))

                // Print date centered
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("Date: ${bonRetour.bORDateFormatted}", TextStyle.getStyle().setTextSize(21))

                // Print logo
                printLogo(lineApi, printParams)

                // Print establishment info
                printEstablishmentInfo(lineApi, printParams)

                // Print client info
                printClientInfo(lineApi, client, bonRetour.bORNomfrs, bonRetour.bORCodefrs, "", _ticketNote, printParams)

                // Print operator info
                printOperatorInfo(lineApi, "", utilisateur)

                // Print table header
                lineApi.initLine(BaseStyle.getStyle())
                val headerTextStyle = TextStyle.getStyle().enableBold(true)
                lineApi.printTexts(
                    arrayOf("ARTICLE", "QTE", "PRIX", "TOTAL"),
                    intArrayOf(200, 80, 80, 80),
                    arrayOf(headerTextStyle, headerTextStyle, headerTextStyle, headerTextStyle)
                )

                // Print separator line
                printDividingLine(lineApi, DividingLine.DOTTED, 2)

                // Print items
                var totalAmount = 0.0
                for (ligne in lgBonRetourWithArticle) {
                    val ligneBonRetour = ligne.ligneBonRetour ?: continue
                    val article = ligne.article
                    val articleName = article?.aRTDesignation ?: "Article introuvable (${ligneBonRetour.lIGBonEntreeCodeArt})"
                    val quantity = ligneBonRetour.lIGBonEntreeQte ?: "0"
                    val price = formatPrice(ligneBonRetour.lIGBonEntreePUTTC ?: "0")
                    val total = formatPrice(ligneBonRetour.lIGBonEntreeMntTTC ?: "0")

                    totalAmount += (ligneBonRetour.lIGBonEntreeMntTTC?.toDoubleOrNull() ?: 0.0)

                    // Print article name
                    lineApi.initLine(BaseStyle.getStyle())
                    lineApi.printText(articleName, TextStyle.getStyle().setTextSize(18))

                    // Print article code
                    lineApi.initLine(BaseStyle.getStyle())
                    lineApi.printText("Code: ${article?.aRTCode ?: ligneBonRetour.lIGBonEntreeCodeArt}", TextStyle.getStyle().setTextSize(16))

                    // Print quantity, price and total
                    lineApi.initLine(BaseStyle.getStyle())
                    lineApi.printTexts(
                        arrayOf("Qte: $quantity", "Prix: $price", "Total: $total"),
                        intArrayOf(1, 1, 1),
                        arrayOf(
                            TextStyle.getStyle().setTextSize(18),
                            TextStyle.getStyle().setTextSize(18),
                            TextStyle.getStyle().setTextSize(18).enableBold(true)
                        )
                    )

                    // Print separator line
                    printDividingLine(lineApi, DividingLine.SOLID, 1)
                }

                // Print total amount
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.printTexts(
                    arrayOf("TOTAL:", formatPrice(Math.abs(totalAmount).toString())),
                    intArrayOf(1, 1),
                    arrayOf(
                        TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(23).enableBold(true),
                        TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(23).enableBold(true)
                    )
                )

                // Print separator line
                printDividingLine(lineApi, DividingLine.SOLID, 2)

                // Print app version
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                if (printParams.printAppVersion) {
                    lineApi.printText("Version: ${BuildConfig.VERSION_NAME}", TextStyle.getStyle().setTextSize(18))
                }

                // Cut paper and output
                lineApi.autoOut()

                // Monitor print results
                printerToUse.lineApi()?.printTrans(object : com.sunmi.printerx.api.PrintResult() {
                    override fun onResult(resultCode: Int, message: String?) {
                        if (resultCode == 0) {
                            Log.d("SunmiPrintManager", "Bon retour printed successfully")
                            _printerStatus.value = "Print Success"
                        } else {
                            Log.e("SunmiPrintManager", "Error printing bon retour: $message")
                            _printerStatus.value = "Print Error: $message"
                        }
                    }
                })

                // Disable transaction mode
                printerToUse.lineApi()?.enableTransMode(false)

            } catch (e: Exception) {
                Log.e("SunmiPrintManager", "Error printing bon retour: ${e.message}")
                _printerStatus.value = "Print Error: ${e.message}"
            }
        }
    }

    /**
     * Print a Bon Transfert using the SUNMI printer
     */
    fun printBonTransfert(
        context: Context,
        bonTransfert: com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.BonLivraison,
        lgBonTransfert: List<com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraison>,
        articleMapByBarCode: Map<String, com.asmtunis.procaisseinventory.articles.data.article.domaine.Article>,
        stationSource: com.asmtunis.procaisseinventory.data.station.domaine.Station,
        stationDestination: com.asmtunis.procaisseinventory.data.station.domaine.Station,
        utilisateur: Utilisateur,
        printParams: PrintingData
    ) {
        Log.d("SunmiPrintManager", "Printing bon transfert using Sunmi printer")
        Log.d("SunmiPrintManager", "Current printer: $printer, selectPrinter: $selectPrinter")

        // If printer is not ready, initialize and retry after ready
        if (printer == null && selectPrinter == null) {
            Log.e("SunmiPrintManager", "Printer not initialized, initializing now...")
            Toast.makeText(context, "Initializing Sunmi printer...", Toast.LENGTH_SHORT).show()
            initPrinter(context) {
                Log.d("SunmiPrintManager", "Printer initialized in callback, now printing bon transfert")
                // Retry print after printer is ready
                printBonTransfert(context, bonTransfert, lgBonTransfert, articleMapByBarCode, stationSource, stationDestination, utilisateur, printParams)
            }
            return
        }

        viewModelScope.launch(Dispatchers.IO) {
            try {
                // Use the global printer instance if available
                val printerToUse = printer ?: selectPrinter
                if (printerToUse == null) {
                    Log.e("SunmiPrintManager", "No printer available")
                    _printerStatus.value = "No printer available"
                    return@launch
                }

                // Set paper width to 80mm
                setPaperWidth(printerToUse, PAPER_WIDTH_80MM)

                // Enable transaction mode for better performance
                printerToUse.lineApi()?.enableTransMode(true)

                // Get the line API for printing
                val lineApi = printerToUse.lineApi() ?: return@launch

                // Print header with larger text for 80mm paper
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.addText("******", TextStyle.getStyle())
                lineApi.addText("BON TRANSFERT", TextStyle.getStyle().enableBold(true).setTextWidthRatio(1).setTextHeightRatio(1))
                lineApi.printText("******", TextStyle.getStyle())

                // Print bon transfert number
                lineApi.printText("Num: ${bonTransfert.bONTransNum}", TextStyle.getStyle().setTextSize(21))

                // Print bon transfert state if available
                if (!bonTransfert.bONTransEtat.isNullOrBlank()) {
                    lineApi.printText(bonTransfert.bONTransEtat ?: "", TextStyle.getStyle().enableBold(true).setTextSize(21))
                }

                // Print date centered
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("Date: ${bonTransfert.bONTransDateFormatted}", TextStyle.getStyle().setTextSize(21))

                // Print logo
                printLogo(lineApi, printParams)

                // Print establishment info
                printEstablishmentInfo(lineApi, printParams)

                // Print station information
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.addText("Station Source: ", TextStyle.getStyle().enableBold(true))
                lineApi.printText(stationSource.sTATDesg, TextStyle.getStyle())

                lineApi.initLine(BaseStyle.getStyle())
                lineApi.addText("Station Destination: ", TextStyle.getStyle().enableBold(true))
                lineApi.printText(stationDestination.sTATDesg, TextStyle.getStyle())

                // Print operator info
                printOperatorInfo(lineApi, "", utilisateur)

                // Print table header
                lineApi.initLine(BaseStyle.getStyle())
                val headerTextStyle = TextStyle.getStyle().enableBold(true)
                lineApi.printTexts(
                    arrayOf("ARTICLE", "QTE"),
                    intArrayOf(300, 100),
                    arrayOf(headerTextStyle, headerTextStyle)
                )

                // Print separator line
                lineApi.initLine(BaseStyle.getStyle())
            printDividingLine(lineApi,DividingLine.EMPTY, 30)
            printDividingLine(lineApi,DividingLine.DOTTED, 2)
            printDividingLine(lineApi,DividingLine.EMPTY, 30)
                // Print items
                for (ligne in lgBonTransfert) {
                    val article = articleMapByBarCode[ligne.lGBonTransCodeArt] ?: continue
                    val qte = ligne.qteDecTransferer ?: "0"

                    // Print article name
                    lineApi.initLine(BaseStyle.getStyle())
                    lineApi.printText(article.aRTDesignation, TextStyle.getStyle())

                    // Print article code
                    lineApi.initLine(BaseStyle.getStyle())
                    lineApi.printText("Code: ${article.aRTCode}", TextStyle.getStyle().setTextSize(18))

                    // Print quantity and price
                    lineApi.initLine(BaseStyle.getStyle())
                    lineApi.printTexts(
                        arrayOf("Quantité", qte),
                        intArrayOf(300, 100),
                        arrayOf(TextStyle.getStyle(), TextStyle.getStyle().enableBold(true))
                    )

                    // Print separator line
                    printDividingLine(lineApi, DividingLine.SOLID, 1)
                }

                // Print total articles
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.printTexts(
                    arrayOf("Total Articles", lgBonTransfert.size.toString()),
                    intArrayOf(300, 100),
                    arrayOf(TextStyle.getStyle().enableBold(true), TextStyle.getStyle().enableBold(true))
                )

                // Print separator line
                printDividingLine(lineApi, DividingLine.SOLID, 1)

                // Print app version
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                if (printParams.printAppVersion) {
                    lineApi.printText("Version: ${BuildConfig.VERSION_NAME}", TextStyle.getStyle().setTextSize(18))
                }

                // Cut paper and output
                lineApi.autoOut()

                // Monitor print results
                printerToUse.lineApi()?.printTrans(object : com.sunmi.printerx.api.PrintResult() {
                    override fun onResult(resultCode: Int, message: String?) {
                        if (resultCode == 0) {
                            Log.d("SunmiPrintManager", "Bon transfert printed successfully")
                            _printerStatus.value = "Print Success"
                        } else {
                            Log.e("SunmiPrintManager", "Error printing bon transfert: $message")
                            _printerStatus.value = "Print Error: $message"
                        }
                    }
                })

                // Disable transaction mode
                printerToUse.lineApi()?.enableTransMode(false)

            } catch (e: Exception) {
                Log.e("SunmiPrintManager", "Error printing bon transfert: ${e.message}")
                _printerStatus.value = "Print Error: ${e.message}"
            }
        }
    }

    /**
     * Print a Bon Achat (Purchase Order) using the SUNMI printer
     */
    fun printBonAchat(
        context: Context,
        lgBonEntree: List<com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.LigneBonEntree>,
        bonEntree: com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.BonEntree,
        articleMapByBarCode: Map<String, com.asmtunis.procaisseinventory.articles.data.article.domaine.Article>,
        station: com.asmtunis.procaisseinventory.data.station.domaine.Station,
        fournisseur: com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.domaine.Fournisseur,
        printParams: PrintingData
    ) {
        Log.d("SunmiPrintManager", "Printing bon achat using Sunmi printer")
        Log.d("SunmiPrintManager", "Current printer: $printer, selectPrinter: $selectPrinter")

        // If printer is not ready, initialize and retry after ready
        if (printer == null && selectPrinter == null) {
            Log.e("SunmiPrintManager", "Printer not initialized, initializing now...")
            Toast.makeText(context, "Initializing Sunmi printer...", Toast.LENGTH_SHORT).show()
            initPrinter(context) {
                Log.d("SunmiPrintManager", "Printer initialized in callback, now printing bon achat")
                // Retry print after printer is ready
                printBonAchat(context, lgBonEntree, bonEntree, articleMapByBarCode, station, fournisseur, printParams)
            }
            return
        }

        val printerToUse = printer ?: selectPrinter
        if (printerToUse == null) {
            Log.e("SunmiPrintManager", "No printer available for printing")
            _printerStatus.value = "No printer available"
            return
        }

        printerToUse.lineApi()?.let { lineApi ->
            try {
                // Enable transaction mode for better performance
                lineApi.enableTransMode(true)

                // Print header
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("Bon Achat", TextStyle.getStyle().setTextSize(28).enableBold(true))

                // Print document number
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("Num: ${bonEntree.bONENTNum}", TextStyle.getStyle().setTextSize(21))

                // Print type if it's a facture
                if (bonEntree.bONENTTypePiece == "FACTURE") {
                    lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                    lineApi.printText(bonEntree.bONENTTypePiece, TextStyle.getStyle().setTextSize(24).enableBold(true))
                }

                // Print date
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("Date: ${bonEntree.bONENTDateFormatted}", TextStyle.getStyle().setTextSize(21))

                // Print logo
                printLogo(lineApi, printParams)

                // Print establishment info
                printEstablishmentInfo(lineApi, printParams)

                // Print station info
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.printTexts(
                    arrayOf("Station:", station.sTATDesg),
                    intArrayOf(1, 1),
                    arrayOf(
                        TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(21),
                        TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(21)
                    )
                )

                // Print supplier info
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.printTexts(
                    arrayOf("Fournisseur:", fournisseur.fRSNomf),
                    intArrayOf(1, 1),
                    arrayOf(
                        TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(21),
                        TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(21)
                    )
                )

                // Print separator line
                printDividingLine(lineApi, DividingLine.SOLID, 1)

                // Print table header
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.printTexts(
                    arrayOf("Article", "Qte", "Prix", "Total"),
                    intArrayOf(2, 1, 1, 1),
                    arrayOf(
                        TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(18).enableBold(true),
                        TextStyle.getStyle().setAlign(Align.CENTER).setTextSize(18).enableBold(true),
                        TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(18).enableBold(true),
                        TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(18).enableBold(true)
                    )
                )

                printDividingLine(lineApi, DividingLine.DOTTED, 1)

                // Print items
                var totalAmount = 0.0
                lgBonEntree.forEach { ligne ->
                    val article = articleMapByBarCode[ligne.lIGBonEntreeCodeArt]
                    val articleName = article?.aRTDesignation ?: "Article introuvable (${ligne.lIGBonEntreeCodeArt})"
                    val quantity = ligne.lIGBonEntreeQte ?: "0"
                    val price = formatPrice(ligne.lIGBonEntreeMntTTC ?: "0")
                    val total = formatPrice((quantity.toDoubleOrNull() ?: 0.0 * (ligne.lIGBonEntreeMntTTC?.toDoubleOrNull() ?: 0.0)).toString())

                    totalAmount += (quantity.toDoubleOrNull() ?: 0.0) * (ligne.lIGBonEntreeMntTTC?.toDoubleOrNull() ?: 0.0)

                    lineApi.initLine(BaseStyle.getStyle())
                    lineApi.printTexts(
                        arrayOf(articleName, quantity, price, total),
                        intArrayOf(2, 1, 1, 1),
                        arrayOf(
                            TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(18),
                            TextStyle.getStyle().setAlign(Align.CENTER).setTextSize(18),
                            TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(18),
                            TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(18)
                        )
                    )
                }

                // Print separator line
                printDividingLine(lineApi, DividingLine.SOLID, 1)

                // Print total
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.printTexts(
                    arrayOf("Total:", formatPrice(totalAmount.toString())),
                    intArrayOf(1, 1),
                    arrayOf(
                        TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(21).enableBold(true),
                        TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(21).enableBold(true)
                    )
                )

                // Print separator line
                printDividingLine(lineApi, DividingLine.SOLID, 1)

                // Print app version
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                if (printParams.printAppVersion) {
                    lineApi.printText("Version: ${BuildConfig.VERSION_NAME}", TextStyle.getStyle().setTextSize(18))
                }

                // Cut paper and output
                lineApi.autoOut()

                Log.d("SunmiPrintManager", "Bon achat printed successfully")
            } catch (e: Exception) {
                Log.e("SunmiPrintManager", "Error printing bon achat: ${e.message}")
                _printerStatus.value = "Print Error: ${e.message}"
            }
        }
    }

    /**
     * Prints a payment receipt (Reglement) using the Sunmi printer
     *
     * @param context The application context
     * @param reglementCaisse The payment data with associated ticket and client information
     * @param utilisateur The user/operator information
     * @param printParams Printing configuration parameters
     * @param prefixList List of prefixes for document numbering
     */
    fun printReglement(
        context: Context,
        reglementCaisse: ReglementCaisseWithTicketAndClient,
        utilisateur: Utilisateur,
        printParams: PrintingData,
        prefixList: List<Prefixe>
    ) {
        Log.d("SunmiPrintManager", "Printing reglement using Sunmi printer")
        Log.d("SunmiPrintManager", "Current printer: $printer, selectPrinter: $selectPrinter")

        // If printer is not ready, initialize and retry after ready
        if (printer == null && selectPrinter == null) {
            Log.e("SunmiPrintManager", "Printer not initialized, initializing now...")
            Toast.makeText(context, "Initializing Sunmi printer...", Toast.LENGTH_SHORT).show()
            initPrinter(context) {
                Log.d("SunmiPrintManager", "Printer initialized in callback, now printing reglement")
                // Retry print after printer is ready
                printReglement(context, reglementCaisse, utilisateur, printParams, prefixList)
            }
            return
        }

        viewModelScope.launch(Dispatchers.IO) {
            try {
                // Use the global printer instance if available
                val printerToUse = selectPrinter ?: printer
                if (printerToUse == null) {
                    Log.e("SunmiPrintManager", "No printer available")
                    return@launch
                }

                // Get the printer's line API
                val lineApi = printerToUse.lineApi()
                if (lineApi == null) {
                    Log.e("SunmiPrintManager", "Line API not available")
                    return@launch
                }

                val reglement = reglementCaisse.reglementCaisse
                val client = reglementCaisse.client
                val ticket = reglementCaisse.ticket

                // Set paper width to 80mm
                setPaperWidth(printerToUse, PAPER_WIDTH_80MM)

                // Print header with larger text for 80mm paper
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.addText("******", TextStyle.getStyle())
                lineApi.addText("REGLEMENT", TextStyle.getStyle().enableBold(true).setTextWidthRatio(1).setTextHeightRatio(1))
                lineApi.printText("******", TextStyle.getStyle())

                // Get reglement number
                val numTicket = getReglementNumber(reglement, ticket, prefixList)

                // Check if this is a partial payment
                if (reglement?.rEGNumTicketPart != null && reglement.rEGNumTicketPart.isNotEmpty() && reglement.rEGNumTicketPart != "0") {
                    lineApi.printText("(Paiement Partiel)", TextStyle.getStyle().setTextSize(21))
                }

                lineApi.printText("Num: $numTicket", TextStyle.getStyle().setTextSize(21))

                // Print date centered
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("Date: ${reglement?.rEGCDateReg?.substringBefore(".") ?: ""}", TextStyle.getStyle().setTextSize(21))

                // Print logo/
                printLogo(lineApi, printParams)

                // Print establishment info
                printEstablishmentInfo(lineApi, printParams)

                // Print client info
                printClientInfo(lineApi, client, reglement?.rEGCNomPrenom, reglement?.rEGCCodeClient, "", _ticketNote, printParams)

                // Print operator info
                printOperatorInfo(lineApi, reglement?.rEGCIdCaisse, utilisateur)

                // Print reglement details with larger text
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))
                lineApi.printText("Montant: ${formatPrice(reglement?.rEGCMontant?.toString())}", TextStyle.getStyle().setTextSize(23).enableBold(true))
                lineApi.printText("Mode: ${reglement?.rEGCModeReg ?: ""}", TextStyle.getStyle().setTextSize(21))

                // Print reference information if available
                if (!reglement?.rEGCRemarque.isNullOrEmpty()) {
                    lineApi.printText("Référence: ${reglement?.rEGCRemarque}", TextStyle.getStyle().setTextSize(21))
                }

                // Print payment details
                val mntEspece = reglement?.rEGCMntEspece ?: 0.0
                val mntCheque = reglement?.rEGCMntCheque ?: 0.0
                val mntTraite = reglement?.rEGCMntTraite ?: 0.0
                val mntCarte = reglement?.rEGCMntCarteBancaire ?: 0.0
                val mntVirement = 0.0 // Not available in ReglementCaisse class
                val total = mntEspece + mntCheque + mntTraite + mntCarte + mntVirement
                printDividingLine(lineApi,DividingLine.EMPTY, 30)
                printDividingLine(lineApi,DividingLine.DOTTED, 2)
                printDividingLine(lineApi,DividingLine.EMPTY, 30)

                // Print detailed payment breakdown
                if (mntEspece > 0) {
                    lineApi.initLine(BaseStyle.getStyle())
                    lineApi.printTexts(
                        arrayOf("Espèce:", formatPrice(mntEspece.toString())),
                        intArrayOf(1, 1),
                        arrayOf(
                            TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(21),
                            TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(21)
                        )
                    )
                }

                if (mntCheque > 0) {
                    lineApi.initLine(BaseStyle.getStyle())
                    lineApi.printTexts(
                        arrayOf("Chèque:", formatPrice(mntCheque.toString())),
                        intArrayOf(1, 1),
                        arrayOf(
                            TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(21),
                            TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(21)
                        )
                    )
                }

                if (mntTraite > 0) {
                    lineApi.initLine(BaseStyle.getStyle())
                    lineApi.printTexts(
                        arrayOf("Traite:", formatPrice(mntTraite.toString())),
                        intArrayOf(1, 1),
                        arrayOf(
                            TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(21),
                            TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(21)
                        )
                    )
                }

                if (mntCarte > 0) {
                    lineApi.initLine(BaseStyle.getStyle())
                    lineApi.printTexts(
                        arrayOf("Carte:", formatPrice(mntCarte.toString())),
                        intArrayOf(1, 1),
                        arrayOf(
                            TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(21),
                            TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(21)
                        )
                    )
                }

                // Virement is not available in ReglementCaisse class

                            printDividingLine(lineApi,DividingLine.EMPTY, 30)
            printDividingLine(lineApi,DividingLine.DOTTED, 2)
            printDividingLine(lineApi,DividingLine.EMPTY, 30)

                // Print total
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.printTexts(
                    arrayOf("Total:", formatPrice(total.toString())),
                    intArrayOf(1, 1),
                    arrayOf(
                        TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(21).enableBold(true),
                        TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(21).enableBold(true)
                    )
                )

                // Print amount to return if applicable
                val mntReste = reglement?.made ?: 0.0
                if (mntReste > 0.0) {
                    lineApi.initLine(BaseStyle.getStyle())
                    lineApi.printTexts(
                        arrayOf("A Rendre:", formatPrice(mntReste.toString())),
                        intArrayOf(1, 1),
                        arrayOf(
                            TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(21).enableBold(true),
                            TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(21).enableBold(true)
                        )
                    )
                }

                // Print footer with client balance and signature boxes
                printFooter(lineApi, client, printParams)

                // Cut paper and output
                lineApi.autoOut()

                Log.d("SunmiPrintManager", "Reglement printed successfully")
            } catch (e: Exception) {
                Log.e("SunmiPrintManager", "Error printing reglement: ${e.message}")
                _printerStatus.value = "Print Error: ${e.message}"
            }
        }
    }

    fun releasePrinter() {
        try {
            PrinterSdk.getInstance().destroy()
            printer = null
            selectPrinter = null
            _isPrinterReady.value = false
            Log.d("SunmiPrintManager", "Printer released")
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error releasing printer: ${e.message}")
        }
    }

    // Helper method to format price values
    private fun formatPrice(value: String?): String {
        if (value == null) return "0.000"
        return try {
            convertStringToPriceFormat(value)
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error formatting price: ${e.message}")
            value
        }
    }

    // Helper method to format quantity values
    private fun formatQuantity(value: String?): String {
        if (value == null || value.isEmpty()) return "0"
        return try {
            // Remove trailing zeros
            val doubleValue = stringToDouble(value)
            if (doubleValue == doubleValue.toInt().toDouble()) {
                // If it's a whole number, return as integer
                return doubleValue.toInt().toString()
            } else {
                // Otherwise return with decimal places
                return doubleValue.toString()
            }
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error formatting quantity: ${e.message}")
            value
        }
    }

    // Helper method to print ticket payment details
    private fun printTicketPaymentDetails(lineApi: com.sunmi.printerx.api.LineApi,
                                     mntEspece: Double,
                                     mntCheque: Double,
                                     mntTraite: Double) {
        try {
            // Print dividing line
            printDividingLine(lineApi, DividingLine.DOTTED, 1)

            // Print cash amount if applicable
            if (mntEspece > 0.0) {
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.printTexts(
                    arrayOf("Espece:", formatPrice(mntEspece.toString())),
                    intArrayOf(1, 1),
                    arrayOf(
                        TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(21),
                        TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(21)
                    )
                )
            }

            // Print check amount if applicable
            if (mntCheque > 0.0) {
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.printTexts(
                    arrayOf("Cheque:", formatPrice(mntCheque.toString())),
                    intArrayOf(1, 1),
                    arrayOf(
                        TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(21),
                        TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(21)
                    )
                )
            }

            // Print restaurant tickets amount if applicable
            if (mntTraite > 0.0) {
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.printTexts(
                    arrayOf("Tikets Restaurant:", formatPrice(mntTraite.toString())),
                    intArrayOf(1, 1),
                    arrayOf(
                        TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(21),
                        TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(21)
                    )
                )
            }
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error printing payment details: ${e.message}")
        }
    }

    // Helper method to get the reglement number
    private fun getReglementNumber(reglementCaisse: ReglementCaisse?, ticket: Ticket?, prefixList: List<Prefixe>): String {
        try {
            if (reglementCaisse == null) return ""

            val prefixeReg = prefixList.firstOrNull { it.pREIdTable == "Reglement" }?.pREPrefixe ?: "REG_M_"

            // Check if this is a partial payment
            val numTicketPart = reglementCaisse.rEGNumTicketPart
            if (numTicketPart != null && numTicketPart.isNotEmpty() && numTicketPart != "0") {
                return "${prefixeReg}${numTicketPart}"
            }

            // Regular payment
            return reglementCaisse.rEGCCode
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error getting reglement number: ${e.message}")
            return ""
        }
    }

    // Helper method to print dividing lines
    private fun printDividingLine(lineApi: com.sunmi.printerx.api.LineApi, type: com.sunmi.printerx.enums.DividingLine, count: Int) {
        try {
            // Use the SDK's DividingLine enum directly
            lineApi.printDividingLine(type, count)
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error printing dividing line: ${e.message}")
        }
    }

    /**
     * Demonstrates the usage of different dividing line types with the SDK
     */
    fun printDividingLineDemo() {
        selectPrinter?.lineApi()?.run {
            // Use the SDK's DividingLine enum directly
            printDividingLine(com.sunmi.printerx.enums.DividingLine.EMPTY, 20)
            printDividingLine(com.sunmi.printerx.enums.DividingLine.DOTTED, 5)
            printDividingLine(com.sunmi.printerx.enums.DividingLine.EMPTY, 20)
            autoOut()
        }
    }

    // Helper method to print segmented line with different alignments
    private fun printSegmentedLine(lineApi: com.sunmi.printerx.api.LineApi, segments: List<Pair<String, Align>>) {
        try {
            lineApi.initLine(BaseStyle.getStyle())
            for (segment in segments) {
                lineApi.addText(segment.first, TextStyle.getStyle().setAlign(segment.second))
            }
            lineApi.autoOut()
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error printing segmented line: ${e.message}")
        }
    }



    // Print the company logo if available and enabled in settings
    private fun printLogo(lineApi: com.sunmi.printerx.api.LineApi, printParams: PrintingData) {
        try {
            if (!printParams.printIcon || logo.isEmpty()) return

            // Decode the Base64 logo
            val decodedString: ByteArray = Base64.decode(logo, Base64.DEFAULT)
            val bmp = BitmapFactory.decodeByteArray(decodedString, 0, decodedString.size)

            if (bmp != null) {
                // Print the logo
                lineApi.printBitmap(bmp, com.sunmi.printerx.style.BitmapStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText(" ", TextStyle.getStyle())
            }
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error printing logo: ${e.message}")
        }
    }

    // Print establishment information if enabled in settings
    private fun printEstablishmentInfo(lineApi: com.sunmi.printerx.api.LineApi, printParams: PrintingData) {
        try {
            if (!printParams.printCompanyName) return


            // Print establishment name
            lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
            lineApi.printText(etablisement.desgEt ?: "N/A", TextStyle.getStyle().setTextSize(21).enableBold(true))

            // Print establishment address
            lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))
            lineApi.printText("Adresse: ${etablisement.etAdresse ?: "N/A"}", TextStyle.getStyle().setTextSize(19))

            // Print establishment phone
            lineApi.printText("Tel: ${etablisement.etTel1 ?: etablisement.etTel2 ?: "N/A"}", TextStyle.getStyle().setTextSize(19))

            // Print establishment fiscal ID
            lineApi.printText("MF: ${etablisement.etMAtriculeF ?: "N/A"}", TextStyle.getStyle().setTextSize(19))

            // Print separator
                        printDividingLine(lineApi,DividingLine.EMPTY, 30)
            printDividingLine(lineApi,DividingLine.DOTTED, 2)
            printDividingLine(lineApi,DividingLine.EMPTY, 30)
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error printing establishment info: ${e.message}")
        }
    }

    // Print client information
    private fun printClientInfo(lineApi: com.sunmi.printerx.api.LineApi, client: Client?, clientName: String?, clientCode: String?, clientAddress: String = "", note: String = "", printParams: PrintingData = PrintingData()) {
        Log.d("SunmiPrintManager", "printClientInfo called with note: '$note', enableNotes: ${printParams.enableNotes}")
        try {
            lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))

            // Print client name with special handling for long names
            if (!clientName.isNullOrEmpty()) {
                if (clientName.length > 24) {
                    lineApi.printText("Client: $clientName", TextStyle.getStyle().setTextSize(19))
                } else {
                    lineApi.initLine(BaseStyle.getStyle())
                    lineApi.printTexts(
                        arrayOf("Client:", clientName),
                        intArrayOf(1, 1),
                        arrayOf(
                            TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(19),
                            TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(19)
                        )
                    )
                }
            }

            // Print client code
            if (!clientCode.isNullOrEmpty()) {
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.printTexts(
                    arrayOf("Code Client:", clientCode),
                    intArrayOf(1, 1),
                    arrayOf(
                        TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(19),
                        TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(19)
                    )
                )
            }

            // Print client fiscal ID if available
            if (client != null && !client.cLIMatFisc.isNullOrEmpty()) {
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.printTexts(
                    arrayOf("MF Client:", client.cLIMatFisc),
                    intArrayOf(1, 1),
                    arrayOf(
                        TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(19),
                        TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(19)
                    )
                )
            }

            // Print client telephone if available
            if (client != null && (!client.cLITel1.isNullOrEmpty() || !client.cLITel2.isNullOrEmpty())) {
                val telephone = client.cLITel1 ?: client.cLITel2 ?: "N/A"
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.printTexts(
                    arrayOf("Tel Client:", telephone),
                    intArrayOf(1, 1),
                    arrayOf(
                        TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(19),
                        TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(19)
                    )
                )
            }

            // Print client address if available
            val address = if (clientAddress.isNotEmpty()) clientAddress else client?.cLIAdresse ?: ""
            if (address.isNotEmpty()) {
                if (address.length > 24) {
                    lineApi.printText("Adresse Client: $address", TextStyle.getStyle().setTextSize(19))
                } else {
                    lineApi.initLine(BaseStyle.getStyle())
                    lineApi.printTexts(
                        arrayOf("Adresse Client:", address),
                        intArrayOf(1, 1),
                        arrayOf(
                            TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(19),
                            TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(19)
                        )
                    )
                }
            }

            // Print note if enabled and available
            Log.d("SunmiPrintManager", "Checking note: enableNotes=${printParams.enableNotes}, note='$note'")
            if (printParams.enableNotes && note.isNotEmpty()) {
                Log.d("SunmiPrintManager", "Printing note: '$note'")
                if (note.length > 24) {
                    lineApi.printText("Note: $note", TextStyle.getStyle().setTextSize(19))
                } else {
                    lineApi.initLine(BaseStyle.getStyle())
                    lineApi.printTexts(
                        arrayOf("Note:", note),
                        intArrayOf(1, 1),
                        arrayOf(
                            TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(19),
                            TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(19)
                        )
                    )
                }
                Log.d("SunmiPrintManager", "Note printed successfully")
            } else {
                Log.d("SunmiPrintManager", "Note not printed: enableNotes=${printParams.enableNotes}, note is empty=${note.isEmpty()}")
            }

            // Print separator
                        printDividingLine(lineApi,DividingLine.EMPTY, 30)
            printDividingLine(lineApi,DividingLine.DOTTED, 2)
            printDividingLine(lineApi,DividingLine.EMPTY, 30)
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error printing client info: ${e.message}")
        }
    }

    // Print operator information
    private fun printOperatorInfo(lineApi: com.sunmi.printerx.api.LineApi, idCaisse: String?, utilisateur: Utilisateur) {
        try {
            lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))

            // Print cash register ID if available
            if (!idCaisse.isNullOrEmpty()) {
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.printTexts(
                    arrayOf("Caisse:", idCaisse),
                    intArrayOf(1, 1),
                    arrayOf(
                        TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(19),
                        TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(19)
                    )
                )
            }

            // Print operator name
            val operatorName = "${utilisateur.Nom ?: ""} ${utilisateur.Prenom ?: ""}"
            lineApi.initLine(BaseStyle.getStyle())
            lineApi.printTexts(
                arrayOf("Operateur:", operatorName),
                intArrayOf(1, 1),
                arrayOf(
                    TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(19),
                    TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(19)
                )
            )

            // Print separator
                        printDividingLine(lineApi,DividingLine.EMPTY, 30)
            printDividingLine(lineApi,DividingLine.DOTTED, 2)
            printDividingLine(lineApi,DividingLine.EMPTY, 30)
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error printing operator info: ${e.message}")
        }
    }

    // Print footer with app version if enabled
    private fun printFooter(lineApi: com.sunmi.printerx.api.LineApi, client: Client?, printParams: PrintingData) {
        try {
            // Print client balance if enabled
            if (printParams.printClientSold && client != null) {
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.printTexts(
                    arrayOf("Solde Client:", formatPrice(client.cLISolde.toString())),
                    intArrayOf(1, 1),
                    arrayOf(
                        TextStyle.getStyle().setAlign(Align.LEFT).setTextSize(19),
                        TextStyle.getStyle().setAlign(Align.RIGHT).setTextSize(19)
                    )
                )
            }

            // Signature boxes

            // Print signature boxes if enabled
            if (printParams.printCachet) {
                            printDividingLine(lineApi,DividingLine.EMPTY, 30)
            printDividingLine(lineApi,DividingLine.DOTTED, 2)
            printDividingLine(lineApi,DividingLine.EMPTY, 30)
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                if (defaultPaperWidth == PAPER_WIDTH_80MM) {
                    // 80mm paper signature boxes
                    lineApi.printText("+--------------------+  +-------------------+ ", TextStyle.getStyle().setTextSize(23))
                    lineApi.printText("|                    |  |                   | ", TextStyle.getStyle().setTextSize(23))
                    lineApi.printText("|                    |  |                   | ", TextStyle.getStyle().setTextSize(23))
                    lineApi.printText("|                    |  |                   | ", TextStyle.getStyle().setTextSize(23))
                    lineApi.printText("|                    |  |                   | ", TextStyle.getStyle().setTextSize(23))
                    lineApi.printText("+--------------------+  +-------------------+ ", TextStyle.getStyle().setTextSize(23))
                    lineApi.printText("  Cachet et signature     Cachet et signature ", TextStyle.getStyle().setTextSize(23))
                    lineApi.printText("    fournisseur               client ", TextStyle.getStyle().setTextSize(23))
                } else {
                    // 58mm paper signature boxes
                    lineApi.printText("+--------------+ +-------------+", TextStyle.getStyle().setTextSize(17))
                    lineApi.printText("|              | |             | ", TextStyle.getStyle().setTextSize(21))
                    lineApi.printText("|              | |             |", TextStyle.getStyle().setTextSize(17))
                    lineApi.printText("|              | |             |", TextStyle.getStyle().setTextSize(17))
                    lineApi.printText("+--------------+ +-------------+", TextStyle.getStyle().setTextSize(17))
                    lineApi.printText("   Fournisseur       Client", TextStyle.getStyle().setTextSize(17))
                }
            }

            // Print app version at the end if enabled
            if (printParams.printAppVersion) {
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                // Make sure to use the correct version code from MobileCodeGeneration
                val versionCode = com.asmtunis.procaisseinventory.core.utils.mobilecode.MobileCodeGeneration.versionCode
                lineApi.printText(versionCode, TextStyle.getStyle().setTextSize(17))
                Log.d("SunmiPrintManager", "Printing app version: $versionCode")
            }
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error printing footer: ${e.message}")
        }
    }

    // Check printer paper type and adjust layout accordingly
    fun checkPrinterPaper(printer: PrinterSdk.Printer): String {
        return try {
            val paper = printer.queryApi().getInfo(com.sunmi.printerx.enums.PrinterInfo.PAPER)
            val printerType = printer.queryApi().getInfo(com.sunmi.printerx.enums.PrinterInfo.TYPE)

            when(paper) {
                PAPER_WIDTH_58MM -> {
                    Log.d("SunmiPrintManager", "Printer paper: 58mm")
                    PAPER_WIDTH_58MM
                }
                PAPER_WIDTH_80MM -> {
                    Log.d("SunmiPrintManager", "Printer paper: 80mm")
                    PAPER_WIDTH_80MM
                }
                else -> {
                    if(printerType == com.sunmi.printerx.enums.PrinterType.THERMAL.toString()) {
                        Log.d("SunmiPrintManager", "Printer paper: Default to ${defaultPaperWidth} for thermal printer")
                        defaultPaperWidth
                    } else {
                        Log.d("SunmiPrintManager", "Non-thermal printer")
                        "Unknown"
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error checking printer paper: ${e.message}")
            defaultPaperWidth // Default to our preferred paper width
        }
    }

    // Set the paper width for the printer
    fun setPaperWidth(printer: PrinterSdk.Printer?, paperWidth: String = PAPER_WIDTH_80MM) {
        try {
            if (printer == null) {
                Log.e("SunmiPrintManager", "Cannot set paper width: printer is null")
                return
            }

            // Update the default paper width
            defaultPaperWidth = paperWidth

            // Try to set the paper width on the printer
            // Note: Not all printers support changing paper width programmatically
            Log.d("SunmiPrintManager", "Setting paper width to $paperWidth")

            // For Sunmi printers, we can adjust the print width in the line API
            val lineApi = printer.lineApi()
            if (lineApi != null) {
                val printWidth = when(paperWidth) {
                    PAPER_WIDTH_80MM -> 576 // 72mm print width (80mm paper)
                    PAPER_WIDTH_58MM -> 384 // 48mm print width (58mm paper)
                    else -> 576 // Default to 80mm
                }

                // For Sunmi printers, we can't directly set the print width
                // but we can adjust the text and formatting based on paper width
                try {
                    // Instead of trying to set the width directly, we'll just log the intended width
                    // and adjust our formatting in the print methods
                    Log.d("SunmiPrintManager", "Using paper width: $paperWidth (print width: $printWidth)")

                    // Store the paper width in a variable that can be accessed by the print methods
                    defaultPaperWidth = paperWidth

                    // We'll adjust the text size and formatting in each print method based on the paper width
                    // For 80mm paper, we'll use larger text sizes and wider separators
                } catch (e: Exception) {
                    Log.e("SunmiPrintManager", "Error configuring printer: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error setting paper width: ${e.message}")
        }
    }

    /**
     * Print an Inventory (Inventaire) using the SUNMI printer
     */
    fun printInventaire(
        context: Context,
        inventaire: com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.Inventaire,
        lgInventaire: List<com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire>,
        articleMapByBarCode: Map<String, com.asmtunis.procaisseinventory.articles.data.article.domaine.Article>,
        station: com.asmtunis.procaisseinventory.data.station.domaine.Station,
        utilisateur: Utilisateur,
        printParams: PrintingData
    ) {
        Log.d("SunmiPrintManager", "Printing inventaire using Sunmi printer")
        Log.d("SunmiPrintManager", "Current printer: $printer, selectPrinter: $selectPrinter")

        // If printer is not ready, initialize and retry after ready
        if (printer == null && selectPrinter == null) {
            Log.e("SunmiPrintManager", "Printer not initialized, initializing now...")
            Toast.makeText(context, "Initializing Sunmi printer...", Toast.LENGTH_SHORT).show()
            initPrinter(context) {
                Log.d("SunmiPrintManager", "Printer initialized in callback, now printing inventaire")
                // Retry print after printer is ready
                printInventaire(context, inventaire, lgInventaire, articleMapByBarCode, station, utilisateur, printParams)
            }
            return
        }

        viewModelScope.launch(Dispatchers.IO) {
            try {
                // Use the global printer instance if available
                val printerToUse = printer ?: selectPrinter
                if (printerToUse == null) {
                    Log.e("SunmiPrintManager", "No printer available")
                    _printerStatus.value = "No printer available"
                    return@launch
                }

                // Set paper width to 80mm
                setPaperWidth(printerToUse, PAPER_WIDTH_80MM)

                // Enable transaction mode for better performance
                printerToUse.lineApi()?.enableTransMode(true)

                // Get the line API for printing
                val lineApi = printerToUse.lineApi() ?: return@launch

                // Print header with larger text for 80mm paper
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.addText("******", TextStyle.getStyle())
                lineApi.addText("INVENTAIRE", TextStyle.getStyle().enableBold(true).setTextWidthRatio(1).setTextHeightRatio(1))
                lineApi.printText("******", TextStyle.getStyle())

                // Print inventaire number
                lineApi.printText("Num: ${inventaire.iNVCode}", TextStyle.getStyle().setTextSize(21))

                // Print inventaire state if available
                if (!inventaire.iNVEtat.isNullOrBlank()) {
                    lineApi.printText(inventaire.iNVEtat ?: "", TextStyle.getStyle().enableBold(true).setTextSize(21))
                }

                // Print date centered
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("Date: ${inventaire.iNVDateFormatted}", TextStyle.getStyle().setTextSize(21))

                // Print logo
                printLogo(lineApi, printParams)

                // Print establishment info
                printEstablishmentInfo(lineApi, printParams)

                // Print station information
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.addText("Station: ", TextStyle.getStyle().enableBold(true))
                lineApi.printText(station.sTATDesg, TextStyle.getStyle())

                // Print operator info
                printOperatorInfo(lineApi, "", utilisateur)

                // Print dividing line before table
                printDividingLine(lineApi, DividingLine.SOLID, 1)

                // Print table header with bold text
                lineApi.initLine(BaseStyle.getStyle())
                val headerTextStyle = TextStyle.getStyle().enableBold(true)
                lineApi.printTexts(
                    arrayOf("CODE", "DESIGNATION", "QTE STOCK", "QTE STATION"),
                    intArrayOf(1, 2, 1, 1),
                    arrayOf(
                        headerTextStyle.setAlign(Align.LEFT),
                        headerTextStyle.setAlign(Align.LEFT),
                        headerTextStyle.setAlign(Align.CENTER),
                        headerTextStyle.setAlign(Align.RIGHT)
                    )
                )

                // Print dividing line after header
                printDividingLine(lineApi, DividingLine.SOLID, 1)

                // Print items in a structured table format
                for (ligne in lgInventaire) {
                    val article = articleMapByBarCode[ligne.lGINVCodeArticle] ?: continue
                    val qteStation = ligne.lGINVQteReel ?: "0"
                    val qteStock = ligne.lGINVQteStock ?: "0"
                    val code = article.aRTCode ?: article.aRTCodeBar ?: "N/A"
                    val designation = article.aRTDesignation ?: "N/A"

                    // Truncate designation if too long
                    val maxLength = 20
                    val truncatedDesignation = if (designation.length > maxLength) {
                        designation.substring(0, maxLength) + "..."
                    } else {
                        designation
                    }

                    // Print article details in one row
                    lineApi.initLine(BaseStyle.getStyle())
                    lineApi.printTexts(
                        arrayOf(code, truncatedDesignation, qteStock, qteStation),
                        intArrayOf(1, 2, 1, 1),
                        arrayOf(
                            TextStyle.getStyle().setAlign(Align.LEFT),
                            TextStyle.getStyle().setAlign(Align.LEFT),
                            TextStyle.getStyle().setAlign(Align.CENTER),
                            TextStyle.getStyle().setAlign(Align.RIGHT).enableBold(true)
                        )
                    )

                    // Print a light dividing line between items
                    printDividingLine(lineApi, DividingLine.DOTTED, 1)
                }

                // Print dividing line before total
                printDividingLine(lineApi, DividingLine.SOLID, 1)

                // Print total articles with bold text
                lineApi.initLine(BaseStyle.getStyle())
                lineApi.printTexts(
                    arrayOf("TOTAL ARTICLES", lgInventaire.size.toString()),
                    intArrayOf(4, 1),
                    arrayOf(
                        TextStyle.getStyle().enableBold(true).setAlign(Align.LEFT),
                        TextStyle.getStyle().enableBold(true).setAlign(Align.RIGHT)
                    )
                )

                // Print dividing line after total
                printDividingLine(lineApi, DividingLine.SOLID, 1)

                // Print app version
                if (printParams.printAppVersion) {
                    lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                    lineApi.printText("Version: ${BuildConfig.VERSION_NAME}", TextStyle.getStyle().setTextSize(18))
                }

                // Cut paper and output
                lineApi.autoOut()

                // Monitor print results
                printerToUse.lineApi()?.printTrans(object : com.sunmi.printerx.api.PrintResult() {
                    override fun onResult(resultCode: Int, message: String?) {
                        if (resultCode == 0) {
                            Log.d("SunmiPrintManager", "Inventaire printed successfully")
                            _printerStatus.value = "Print Success"
                        } else {
                            Log.e("SunmiPrintManager", "Error printing inventaire: $message")
                            _printerStatus.value = "Print Error: $message"
                        }
                    }
                })

                // Disable transaction mode
                printerToUse.lineApi()?.enableTransMode(false)

            } catch (e: Exception) {
                Log.e("SunmiPrintManager", "Error printing inventaire: ${e.message}")
                _printerStatus.value = "Print Error: ${e.message}"
            }
        }
    }
}
