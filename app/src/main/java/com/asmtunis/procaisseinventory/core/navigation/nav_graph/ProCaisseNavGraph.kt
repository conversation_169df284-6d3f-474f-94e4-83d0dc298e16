@file:OptIn(ExperimentalCoroutinesApi::class)

package com.asmtunis.procaisseinventory.core.navigation.nav_graph

import android.content.Intent
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.compose.navigation
import androidx.navigation.toRoute
import com.asmtunis.procaisseinventory.articles.consultation.screens.ArticlesScreen
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.ArticleTextValidationViewModel
import com.asmtunis.procaisseinventory.articles.consultation.view_model.ArticlesViewModel
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticlesScreenMobility
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.SelectPatrimoineScreen
import com.asmtunis.procaisseinventory.core.Globals.PRO_CAISSE_MOBILITY
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationViewModule
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AddBonCommandeRoute
import com.asmtunis.procaisseinventory.core.navigation.AddBonLivraisonRoute
import com.asmtunis.procaisseinventory.core.navigation.AddBonRetourRoute
import com.asmtunis.procaisseinventory.core.navigation.AddClientRoute
import com.asmtunis.procaisseinventory.core.navigation.AddModifyDigitalDistributionRoute
import com.asmtunis.procaisseinventory.core.navigation.AddReglementRoute
import com.asmtunis.procaisseinventory.core.navigation.AjoutAffectationBatimentRoute
import com.asmtunis.procaisseinventory.core.navigation.AjoutAffectationPatrimoineRoute
import com.asmtunis.procaisseinventory.core.navigation.AjoutDepInBatimentRoute
import com.asmtunis.procaisseinventory.core.navigation.AjoutDepInPatrimoineRoute
import com.asmtunis.procaisseinventory.core.navigation.AjoutDepOutBatimentRoute
import com.asmtunis.procaisseinventory.core.navigation.AjoutDepOutPatrimoineRoute
import com.asmtunis.procaisseinventory.core.navigation.AjoutInventaireBatimentRoute
import com.asmtunis.procaisseinventory.core.navigation.AjoutInventairePatrimoineRoute
import com.asmtunis.procaisseinventory.core.navigation.ArchiveRoute
import com.asmtunis.procaisseinventory.core.navigation.AutreDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.BarCodeCameraRoute
import com.asmtunis.procaisseinventory.core.navigation.BonCommandeDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.BonCommandeRoute
import com.asmtunis.procaisseinventory.core.navigation.BonLivraisonDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.BonLivraisonRoute
import com.asmtunis.procaisseinventory.core.navigation.BonRetourDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.BonRetourRoute
import com.asmtunis.procaisseinventory.core.navigation.ChooseImmoRoute
import com.asmtunis.procaisseinventory.core.navigation.ChooseSiteFinancierRoute
import com.asmtunis.procaisseinventory.core.navigation.ClientListRoute
import com.asmtunis.procaisseinventory.core.navigation.ClientsInfoRoute
import com.asmtunis.procaisseinventory.core.navigation.DepenceRoute
import com.asmtunis.procaisseinventory.core.navigation.DeplacementOutByUserDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.DeplacementOutByUserRoute
import com.asmtunis.procaisseinventory.core.navigation.DeplacementOutByUserWaitingRoute
import com.asmtunis.procaisseinventory.core.navigation.DigitalDistributionRoute
import com.asmtunis.procaisseinventory.core.navigation.FamilleProduitDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.HomePageRoute
import com.asmtunis.procaisseinventory.core.navigation.InventaireBatimentDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.InventaireBatimentRoute
import com.asmtunis.procaisseinventory.core.navigation.InventairePatrimoineDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.InventairePatrimoineRoute
import com.asmtunis.procaisseinventory.core.navigation.MainImageTiketRoute
import com.asmtunis.procaisseinventory.core.navigation.NewProductDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.PaymentListRoute
import com.asmtunis.procaisseinventory.core.navigation.PrixDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.ProCaisseSyncRoute
import com.asmtunis.procaisseinventory.core.navigation.ProCaisseUpdateRoute
import com.asmtunis.procaisseinventory.core.navigation.ProcaisseGraph
import com.asmtunis.procaisseinventory.core.navigation.ProductListRoute
import com.asmtunis.procaisseinventory.core.navigation.PromotionDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.RecapBonLivraisonRoute
import com.asmtunis.procaisseinventory.core.navigation.ReglementRoute
import com.asmtunis.procaisseinventory.core.navigation.SelectArticlesScreensCalculRoute
import com.asmtunis.procaisseinventory.core.navigation.SelectPatrimoineRoute
import com.asmtunis.procaisseinventory.core.navigation.SettingRoute
import com.asmtunis.procaisseinventory.core.navigation.StatGraphRoute
import com.asmtunis.procaisseinventory.core.navigation.TourneRoute
import com.asmtunis.procaisseinventory.core.navigation.VeilleConcurentielleRoute
import com.asmtunis.procaisseinventory.core.navigation.ZoneConsomationDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.ZoneConsomationRoute
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.wifi.WifiPrintViewModel
import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.asmtunis.procaisseinventory.core.sync_workmanager.SyncWorkerViewModel
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.network_errors.view_model.NetworkErrorsViewModel
import com.asmtunis.procaisseinventory.pro_caisse.ArchiveScreen
import com.asmtunis.procaisseinventory.pro_caisse.DepenceScreen
import com.asmtunis.procaisseinventory.pro_caisse.ProCaisseSyncScreen
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.screens.AddBonCommandeScreen
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.screens.BonCommandeDetailPane
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.screens.BonCommandeScreen
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.screens.AddBonLivraisonScreen
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.screens.BonLivraisonDetailPane
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.screens.BonLivraisonScreen
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.screens.PaymentsScreen
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.screens.RecapBonLivraisonScreen
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.screens.AddBonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.screens.BonRetourDetailPane
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.screens.BonRetourScreen
import com.asmtunis.procaisseinventory.pro_caisse.client.screens.AddClientScreen
import com.asmtunis.procaisseinventory.pro_caisse.client.screens.ClientInfoScreen
import com.asmtunis.procaisseinventory.pro_caisse.client.screens.ClientScreen
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.DashboardScreenViewModel
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.screen.DashboardScreen
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.stat.BillsScreen
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.screens.AddModifyVisiteSreen
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.screens.DigitalDistributionScreen
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.screens.FamilleProduitDetailScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.ui.DeplacementOutByUserDetailScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.ui.DeplacementOutByUserScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.affectation.AddAffectationImmoScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.consultation.InventaireImmobilisationDetailScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.consultation.InventaireImmobilisationScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.deplacement_in.AddDeplacementInImmoScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.deplacement_in.DeplacementOutByUserWaitingScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.deplacement_out.AddDeplacementOutImmoScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.deplacement_out.ChooseSiteFinancierScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.deplacement_out.ChooseSocieteScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.inventaire.AddInventaireImmoScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.zone_consomation.ZoneConsomationDetailScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.zone_consomation.ZoneConsomationScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.patrimoine.ui.InventairePatrimoineDetailScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.patrimoine.ui.InventairePatrimoineScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.patrimoine.ui.affectation.AddAffectationScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.patrimoine.ui.deplacement_in.AddDeplacementInScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.patrimoine.ui.deplacement_out.AddDeplacementOutScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.patrimoine.ui.inventaire.AddInventaireScreen
import com.asmtunis.procaisseinventory.pro_caisse.reglement.screens.AddReglement
import com.asmtunis.procaisseinventory.pro_caisse.reglement.screens.ReglementScreen
import com.asmtunis.procaisseinventory.pro_caisse.tournee.screens.CircuitScreen
import com.asmtunis.procaisseinventory.pro_caisse.updates.ProCaisseUpdateDataScreen
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.screens.AutreDetailScreen
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.screens.NewProductDetailScreen
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.screens.PrixDetailScreen
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.screens.PromotionDetailScreen
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.ui.VeilleConcurentielleScreen
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingScreen
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.MainImageTiket
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeCameraPreviewScreen
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import kotlinx.coroutines.ExperimentalCoroutinesApi


@OptIn(ExperimentalPermissionsApi::class, ExperimentalMaterial3Api::class)
fun NavGraphBuilder.proCaisseNavGraph(
    navigate: (route: Any) -> Unit,
    navigatePopUpTo: (route: Any, popUpTo: Any, isInclusive: Boolean) -> Unit,
    popBackStack: () -> Unit,
    navigateUp: () -> Unit,
    networkErrorsVM: NetworkErrorsViewModel,
    intent: Intent,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    locationViewModule: LocationViewModule,
    articlesViewModel: ArticlesViewModel,
    mainViewModel: MainViewModel,
    selectArtMobilityVM: SelectArticleCalculViewModel,
    cameraViewModel: CameraViewModel,
    barCodeViewModel: BarCodeViewModel,
    articleTxtValidViewModel: ArticleTextValidationViewModel,
    settingVM: SettingViewModel,

    proCaisseViewModels: ProCaisseViewModels,

    wifiPrintVM: WifiPrintViewModel,
    printViewModel: PrintViewModel,
    bluetoothVM: BluetoothViewModel,
    sunmiPrintManager: SunmiPrintManager,
    syncWorkerVM: SyncWorkerViewModel,


    syncInventoryViewModel: SyncInventoryViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,

    dashboardScreenVM: DashboardScreenViewModel

) {

    navigation<ProcaisseGraph>(
        startDestination = HomePageRoute
    ) {



        composable<HomePageRoute> {

            DashboardScreen(
                navigate = { navigate(it) },
                navDrawerViewModel = navigationDrawerViewModel,
                networkErrorsVM = networkErrorsVM,
                intent = intent,
                dataViewModel = dataViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                //  authViewModel= authViewModel,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                networkViewModel = networkViewModel,
                mainViewModel = mainViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                syncInventoryViewModel = syncInventoryViewModel,
                settingViewModel = settingVM,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
                dashboardScreenVM = dashboardScreenVM
            )
        }

        composable<StatGraphRoute> {
            BillsScreen(
                dashboardScreenVM = dashboardScreenVM
            )
        }

        composable<BonLivraisonRoute> { arg ->
            val args = arg.toRoute<BonLivraisonRoute>()

            BonLivraisonScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                clientId = args.clientId,
                navDrawerViewmodel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                selectArtMobilityVM = selectArtMobilityVM,
                bonLivraisonVM = proCaisseViewModels.bonLivraisonViewModel,
                mainViewModel = mainViewModel,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                wifiPrintVM = wifiPrintVM,
                sunmiPrintManager = sunmiPrintManager,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                settingVM = settingVM,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
                syncInventoryViewModel = syncInventoryViewModel,
            )
        }

        composable<RecapBonLivraisonRoute> {

            RecapBonLivraisonScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                bonLivraisonVM = proCaisseViewModels.bonLivraisonViewModel,
                dataViewModel = dataViewModel,
                selectArtMobilityVM = selectArtMobilityVM,
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                mainViewModel = mainViewModel,
                wifiPrintVM = wifiPrintVM,
                sunmiPrintManager = sunmiPrintManager,
                settingVM = settingVM,
                networkViewModel = networkViewModel,
                navDrawerViewmodel = navigationDrawerViewModel,
            )
        }

        composable<BonLivraisonDetailRoute> {
            BonLivraisonDetailPane(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navigationDrawerViewModel = navigationDrawerViewModel,
                bonLivraisonViewModel = proCaisseViewModels.bonLivraisonViewModel,
                mainViewModel = mainViewModel,
                selectArtMobilityVM = selectArtMobilityVM,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                printViewModel = printViewModel,
                wifiPrintVM = wifiPrintVM,
                bluetoothVM = bluetoothVM,
                sunmiPrintManager = sunmiPrintManager,
                settingViewModel = settingVM
            )
        }

        composable<AddBonLivraisonRoute> { arg ->
            val args = arg.toRoute<AddBonLivraisonRoute>()

            AddBonLivraisonScreen(
                navigateTo = { navigate(it) },
                popBackStack = { popBackStack() },
                clientId = args.clientId,
                navDrawerVM = navigationDrawerViewModel,
                proCaisseViewModels = proCaisseViewModels,
                mainViewModel = mainViewModel,
                selectArtMobilityVM = selectArtMobilityVM,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                barCodeViewModel = barCodeViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                settingViewModel = settingVM
            )
        }

        composable<PaymentListRoute> { arg ->
            val args = arg.toRoute<PaymentListRoute>()
            PaymentsScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                clientId = args.clientId,
                navDrawerViewModel = navigationDrawerViewModel,
                mainViewModel = mainViewModel,
               proCaisseViewModels = proCaisseViewModels,
                selectArtMobilityVM = selectArtMobilityVM,
                dataViewModel = dataViewModel,
                locationViewModule = locationViewModule,
                networkViewModel = networkViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                settingViewModel = settingVM
            )
        }


        composable<BonCommandeRoute> { arg ->
            val args = arg.toRoute<BonCommandeRoute>()
            BonCommandeScreen(
                navigate = { navigate(it) },
                clientId = args.clientId,
                navDrawerViewmodel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                bonCommandeVM = proCaisseViewModels.bonCommandeViewModel,
                mainViewModel = mainViewModel,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                selectArtMobilityVM = selectArtMobilityVM,
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                wifiPrintVM = wifiPrintVM,
                settingVM = settingVM,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
                syncInventoryViewModel = syncInventoryViewModel,

                )
        }

        composable<AddBonCommandeRoute> { arg ->
            val args = arg.toRoute<AddBonCommandeRoute>()

            AddBonCommandeScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                clientId = args.clientId,
                navigateUp = { navigateUp() },
                bonCommandeVM = proCaisseViewModels.bonCommandeViewModel,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                barCodeViewModel = barCodeViewModel,
                mainViewModel = mainViewModel,
                navigationDrawerViewModel = navigationDrawerViewModel,
                selectArtMobilityVM = selectArtMobilityVM,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                settingViewModel = settingVM
            )
        }

        composable<BonCommandeDetailRoute> {
            BonCommandeDetailPane(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navigationDrawerViewModel = navigationDrawerViewModel,
                bonCommandeViewModel = proCaisseViewModels.bonCommandeViewModel,
                mainViewModel = mainViewModel,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                selectArtMobilityVM = selectArtMobilityVM,
                printViewModel = printViewModel,
                wifiPrintVM = wifiPrintVM,
                bluetoothVM = bluetoothVM,
                sunmiPrintManager = sunmiPrintManager,
                settingViewModel = settingVM
            )
        }

        composable<BonRetourRoute> { arg ->
            val args = arg.toRoute<BonRetourRoute>()

            BonRetourScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                clientId = args.clientId,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                navDrawerViewmodel = navigationDrawerViewModel,
                bonRetourViewModel = proCaisseViewModels.bonRetourViewModel,
                mainViewModel = mainViewModel,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                selectArtMobilityVM = selectArtMobilityVM,
                wifiPrintVM = wifiPrintVM,
                sunmiPrintManager = sunmiPrintManager,
                settingVM = settingVM,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,

                syncInventoryViewModel = syncInventoryViewModel,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels
            )
        }

        composable<BonRetourDetailRoute> {

            BonRetourDetailPane(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navigationDrawerViewModel = navigationDrawerViewModel,
                bonRetourViewModel = proCaisseViewModels.bonRetourViewModel,
                mainViewModel = mainViewModel,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                selectArtMobilityVM = selectArtMobilityVM,
                printViewModel = printViewModel,
                wifiPrintVM = wifiPrintVM,
                sunmiPrintManager = sunmiPrintManager,
                bluetoothVM = bluetoothVM,
                settingViewModel = settingVM
            )
        }

        composable<AddBonRetourRoute> { arg ->
            val args = arg.toRoute<AddBonRetourRoute>()

            AddBonRetour(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navigateUp = { navigateUp() },
                clientId = args.clientId,
                bonRetourViewModel = proCaisseViewModels.bonRetourViewModel,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                barCodeViewModel = barCodeViewModel,
                mainViewModel = mainViewModel,
                navigationDrawerViewModel = navigationDrawerViewModel,
                selectArtMobilityVM = selectArtMobilityVM,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                settingViewModel = settingVM
            )
        }


        composable<InventairePatrimoineRoute> { arg ->
            val args = arg.toRoute<InventairePatrimoineRoute>()
            InventairePatrimoineScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                clientId = args.clientId,
                navDrawerViewModel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                mainViewModel = mainViewModel,
                invPatViewModel = proCaisseViewModels.invPatViewModel,
                selectArtMobilityVM = selectArtMobilityVM,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,
                syncInventoryViewModel = syncInventoryViewModel,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
                settingViewModel = settingVM
            )
        }

        composable<AjoutAffectationPatrimoineRoute> { arg ->
            val args = arg.toRoute<AjoutAffectationPatrimoineRoute>()
            AddAffectationScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navigateUp = { navigateUp() },
                clientId = args.clientId,
                navDrawerViewModel = navigationDrawerViewModel,
                proCaisseViewModels = proCaisseViewModels,
                mainViewModel = mainViewModel,
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel
            )
        }


        composable<AjoutDepInPatrimoineRoute> { arg ->
            val args = arg.toRoute<AjoutDepInPatrimoineRoute>()
            AddDeplacementInScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navigateUp = { navigateUp() },
                clientId = args.clientId,
                navDrawerViewModel = navigationDrawerViewModel,
                proCaisseViewModels = proCaisseViewModels,
                mainViewModel = mainViewModel,
                barCodeViewModel = barCodeViewModel,
                networkViewModel = networkViewModel,
                settingViewModel = settingVM,
                dataViewModel = dataViewModel
            )
        }

        composable<AjoutDepOutPatrimoineRoute> { arg ->
            val args = arg.toRoute<AjoutDepOutPatrimoineRoute>()
            AddDeplacementOutScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navigateUp = { navigateUp() },
                clientId = args.clientId,
                navDrawerViewModel = navigationDrawerViewModel,
                mainViewModel = mainViewModel,
                barCodeViewModel = barCodeViewModel,
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,
                settingViewModel = settingVM,
                proCaisseViewModels = proCaisseViewModels
            )
        }

        composable<AjoutInventairePatrimoineRoute> { arg ->
            val args = arg.toRoute<AjoutInventairePatrimoineRoute>()
            AddInventaireScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navigateUp = { navigateUp() },
                clientId = args.clientId,
                navDrawerViewModel = navigationDrawerViewModel,
                mainViewModel = mainViewModel,
                barCodeViewModel = barCodeViewModel,
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,
                settingViewModel = settingVM,
                proCaisseViewModels = proCaisseViewModels
            )
        }

        composable<InventairePatrimoineDetailRoute> {
            InventairePatrimoineDetailScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                mainViewModel = mainViewModel,
                proCaisseViewModels = proCaisseViewModels
            )
        }
        composable<SelectPatrimoineRoute> {
            SelectPatrimoineScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                articleTxtValidViewModel = articleTxtValidViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                articlesViewModel = articlesViewModel,
                barCodeViewModel = barCodeViewModel,
                mainViewModel = mainViewModel,
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,
                settingViewModel = settingVM,
                proCaisseViewModels = proCaisseViewModels
            )
        }






        composable<InventaireBatimentRoute> {
            InventaireImmobilisationScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navDrawerViewModel = navigationDrawerViewModel,
                mainViewModel = mainViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,
                syncInventoryViewModel = syncInventoryViewModel,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
                settingViewModel = settingVM,

                proCaisseViewModels = proCaisseViewModels
            )
        }

        composable<AjoutAffectationBatimentRoute> { arg ->
            val args = arg.toRoute<AjoutAffectationBatimentRoute>()
            AddAffectationImmoScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                args = args,
                navDrawerViewModel = navigationDrawerViewModel,
                mainViewModel = mainViewModel,
                barCodeViewModel = barCodeViewModel,
                cameraViewModel = cameraViewModel,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                settingViewModel = settingVM,
                proCaisseViewModels = proCaisseViewModels,
            )
        }


        composable<AjoutDepInBatimentRoute> {
            AddDeplacementInImmoScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navDrawerViewModel = navigationDrawerViewModel,
                mainViewModel = mainViewModel,
                barCodeViewModel = barCodeViewModel,
                cameraViewModel = cameraViewModel,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                proCaisseViewModels = proCaisseViewModels,
                settingViewModel = settingVM
            )
        }

        composable<AjoutDepOutBatimentRoute> {
            AddDeplacementOutImmoScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                dataViewModel = dataViewModel,
                navDrawerViewModel = navigationDrawerViewModel,
                mainViewModel = mainViewModel,
                barCodeViewModel = barCodeViewModel,
                cameraViewModel = cameraViewModel,
                networkViewModel = networkViewModel,
                proCaisseViewModels = proCaisseViewModels,
                settingViewModel = settingVM
            )
        }

        composable<AjoutInventaireBatimentRoute> {
            AddInventaireImmoScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navDrawerViewModel = navigationDrawerViewModel,
                mainViewModel = mainViewModel,
                barCodeViewModel = barCodeViewModel,
                cameraViewModel = cameraViewModel,
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,
                proCaisseViewModels = proCaisseViewModels,
                settingViewModel = settingVM
            )
        }

        composable<InventaireBatimentDetailRoute> {
            InventaireImmobilisationDetailScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                mainViewModel = mainViewModel,
                cameraViewModel = cameraViewModel,
                proCaisseViewModels = proCaisseViewModels,
                settingViewModel = settingVM
            )
        }






        composable<ZoneConsomationRoute> {
            ZoneConsomationScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navigatePopUpTo = { route: Any, popUpTo: Any, isInclusive: Boolean ->
                    navigatePopUpTo(route, popUpTo, isInclusive)
                },
                navigationDrawerViewModel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                mainViewModel = mainViewModel,
                dataViewModel = dataViewModel,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                networkViewModel = networkViewModel,
                batimentViewModel = proCaisseViewModels.batimentViewModel,
                barCodeViewModel = barCodeViewModel,

                syncInventoryViewModel = syncInventoryViewModel,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
                settingViewModel = settingVM
            )
        }

        composable<DeplacementOutByUserRoute> {
            DeplacementOutByUserScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navigationDrawerViewModel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                mainViewModel = mainViewModel,
                dataViewModel = dataViewModel,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                networkViewModel = networkViewModel,
                deplacementOutByUserViewModel = proCaisseViewModels.deplacementOutByUserViewModel,
                syncInventoryViewModel = syncInventoryViewModel,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
                settingViewModel = settingVM
            )
        }

        composable<DeplacementOutByUserWaitingRoute> { arg ->
            val args = arg.toRoute<DeplacementOutByUserWaitingRoute>()


            DeplacementOutByUserWaitingScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                args = args,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                mainViewModel = mainViewModel,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                deplacementOutByUserViewModel = proCaisseViewModels.deplacementOutByUserViewModel
            )
        }




        composable<DeplacementOutByUserDetailRoute> {
            DeplacementOutByUserDetailScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                mainViewModel = mainViewModel,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                proCaisseViewModels = proCaisseViewModels,
                settingViewModel = settingVM
            )
        }



        composable<ChooseImmoRoute> {
            ChooseSocieteScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navigationDrawerViewModel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                mainViewModel = mainViewModel,
                dataViewModel = dataViewModel,
                locationViewModule = locationViewModule,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                networkViewModel = networkViewModel,
                batimentViewModel = proCaisseViewModels.batimentViewModel,
                barCodeViewModel = barCodeViewModel,

                syncInventoryViewModel = syncInventoryViewModel,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
                settingViewModel = settingVM
            )
        }

        composable<ChooseSiteFinancierRoute> {
            ChooseSiteFinancierScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navigationDrawerViewModel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                mainViewModel = mainViewModel,
                dataViewModel = dataViewModel,
                locationViewModule = locationViewModule,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                networkViewModel = networkViewModel,
                batimentViewModel = proCaisseViewModels.batimentViewModel,
                barCodeViewModel = barCodeViewModel,
                syncInventoryViewModel = syncInventoryViewModel,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
                settingViewModel = settingVM
            )
        }









        composable<ZoneConsomationDetailRoute> {
            ZoneConsomationDetailScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navigatePopUpTo = { route: Any, popUpTo: Any, isInclusive: Boolean ->
                    navigatePopUpTo(route, popUpTo, isInclusive)
                },
                navigationDrawerViewModel = navigationDrawerViewModel,
                mainViewModel = mainViewModel,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                barCodeViewModel = barCodeViewModel,
                syncInvBatimentViewModel = syncProcaisseViewModels.syncInvBatimentViewModel,
                proCaisseViewModels = proCaisseViewModels,
                settingViewModel = settingVM
            )
        }


        composable<ClientListRoute> {
            ClientScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navigationDrawerViewModel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                mainViewModel = mainViewModel,
                dataViewModel = dataViewModel,
                locationViewModule = locationViewModule,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                networkViewModel = networkViewModel,
                syncInventoryViewModel = syncInventoryViewModel,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
                selectArtMobilityVM = selectArtMobilityVM,
                settingViewModel = settingVM,
                proCaisseViewModels = proCaisseViewModels
            )
        }




        composable<ClientsInfoRoute> { arg ->
            val args = arg.toRoute<ClientsInfoRoute>()
            ClientInfoScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                clientId = args.clientId,
                mainViewModel = mainViewModel,
                dataViewModel = dataViewModel,
                navDrawerViewModel = navigationDrawerViewModel,
                selectArtMobilityVM = selectArtMobilityVM,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                locationViewModule = locationViewModule,
                networkViewModel = networkViewModel,
                settingViewModel = settingVM,
                proCaisseViewModels = proCaisseViewModels
            )
        }

        composable<AddClientRoute> { arg ->
            val args = arg.toRoute<AddClientRoute>()

            AddClientScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                clientId = args.clientId,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                networkViewModel = networkViewModel,
                locationViewModule = locationViewModule,
                navigationDrawerViewModel = navigationDrawerViewModel,
                dataViewModel = dataViewModel,
                mainViewModel = mainViewModel,
                settingViewModel = settingVM,
                proCaisseViewModels = proCaisseViewModels
            )
        }

        composable<AddModifyDigitalDistributionRoute> { arg ->
            val args = arg.toRoute<AddModifyDigitalDistributionRoute>()

            AddModifyVisiteSreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                clientId = args.clientId,
                distNumViewModel = proCaisseViewModels.distNumViewModel,
                locationViewModule = locationViewModule,
                navigationDrawerViewModel = navigationDrawerViewModel,
                dataViewModel = dataViewModel,
                mainViewModel = mainViewModel,
                networkViewModel = networkViewModel,
                settingViewModel = settingVM

            )
        }

        composable<FamilleProduitDetailRoute> {
            FamilleProduitDetailScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                distNumViewModel = proCaisseViewModels.distNumViewModel,
                navigationDrawerViewModel = navigationDrawerViewModel,
                dataViewModel = dataViewModel,
                mainViewModel = mainViewModel,
                networkViewModel = networkViewModel,
                settingViewModel = settingVM

            )
        }





        composable<ProCaisseSyncRoute> {
            ProCaisseSyncScreen(
                navigate = { navigate(it) },
                navigateUp = { navigateUp() },
                mainViewModel = mainViewModel,
                dataViewModel = dataViewModel,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
                networkViewModel = networkViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
            )
        }


        composable<ProCaisseUpdateRoute> {
            ProCaisseUpdateDataScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navDrawerViewModel = navigationDrawerViewModel,
                dataViewModel = dataViewModel,
                mainViewModel = mainViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                networkViewModel = networkViewModel
            )
        }



        composable<ProductListRoute> {
            ArticlesScreen(
                navigate = { navigate(it) },
                navigationDrawerViewModel = navigationDrawerViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                articlesViewModel = articlesViewModel,
                articleTxtValidViewModel = articleTxtValidViewModel,
                barCodeViewModel = barCodeViewModel,
                from = PRO_CAISSE_MOBILITY,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,
                mainViewModel = mainViewModel,
                syncInventoryViewModel = syncInventoryViewModel,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
                settingViewModel = settingVM
            )
        }

        composable<BarCodeCameraRoute> {

            BarCodeCameraPreviewScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                settingViewModel = settingVM,
                barCodeViewModel = barCodeViewModel,
                navigationDrawerViewModel = navigationDrawerViewModel
            )
        }


        composable<TourneRoute> {

            CircuitScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navDrawerVM = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,
                mainViewModel = mainViewModel,
                locationViewModule = locationViewModule,
                syncInventoryViewModel = syncInventoryViewModel,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
                proCaisseViewModels = proCaisseViewModels,
                settingViewModel = settingVM
            )

        }
        composable<ArchiveRoute> {
            ArchiveScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                mainViewModel = mainViewModel,
                navigationDrawerViewModel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                printViewModel = printViewModel,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,
                syncInventoryViewModel = syncInventoryViewModel,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
                settingViewModel = settingVM
            )
        }
        composable<SettingRoute> {
            SettingScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                mainViewModel = mainViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                settingVM = settingVM,
                dataVm = dataViewModel,
                syncWorkerVM = syncWorkerVM,
                networkViewModel = networkViewModel,
                navDrawerViewModel = navigationDrawerViewModel
            )
        }

        composable<DepenceRoute> {
            DepenceScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                mainViewModel = mainViewModel,
                navDrawerViewModel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,

                syncInventoryViewModel = syncInventoryViewModel,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
                settingViewModel = settingVM
            )
        }
        composable<DigitalDistributionRoute> { arg ->
            val args = arg.toRoute<DigitalDistributionRoute>()
            DigitalDistributionScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                clientId = args.clientId,
                navigationDrawerViewModel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                distNumViewModel = proCaisseViewModels.distNumViewModel,
                mainViewModel = mainViewModel,
                locationViewModule = locationViewModule,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,

                syncInventoryViewModel = syncInventoryViewModel,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
                settingViewModel = settingVM
            )
        }

        composable<ReglementRoute> { arg ->
            val args = arg.toRoute<ReglementRoute>()
            ReglementScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                clientId = args.clientId,
                navigationDrawerViewModel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                proCaisseViewModels = proCaisseViewModels,
                mainViewModel = mainViewModel,
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                networkViewModel = networkViewModel,
                wifiPrintVM = wifiPrintVM,
                sunmiPrintManager = sunmiPrintManager,
                settingVM = settingVM,
                dataViewModel = dataViewModel,
                syncInventoryViewModel = syncInventoryViewModel,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
            )
        }

        composable<AddReglementRoute> { arg ->
            val args = arg.toRoute<AddReglementRoute>()
            AddReglement(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                clientId = args.clientId,
                navDrawerViewModel = navigationDrawerViewModel,
                mainViewModel = mainViewModel,
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,
                paymentViewModel = proCaisseViewModels.paymentViewModel
            )

        }
        composable<VeilleConcurentielleRoute> {
            VeilleConcurentielleScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                cameraViewModel = cameraViewModel,
                navDrawerViewModel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                proCaisseViewModels = proCaisseViewModels,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,
                syncInventoryViewModel = syncInventoryViewModel,
                syncSharedViewModels = syncSharedViewModels,
                syncProcaisseViewModels = syncProcaisseViewModels,
                mainViewModel = mainViewModel,
                settingViewModel = settingVM
            )
        }

        composable<NewProductDetailRoute> {
            NewProductDetailScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                proCaisseViewModels = proCaisseViewModels,
                navigationDrawerViewModel = navigationDrawerViewModel,
                dataViewModel = dataViewModel,
                cameraViewModel = cameraViewModel,
                networkViewModel = networkViewModel,
                mainViewModel = mainViewModel,
                settingViewModel = settingVM
            )
        }
        composable<AutreDetailRoute> {
            AutreDetailScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                proCaisseViewModels = proCaisseViewModels,
                navigationDrawerViewModel = navigationDrawerViewModel,
                dataViewModel = dataViewModel,
                cameraViewModel = cameraViewModel,
                networkViewModel = networkViewModel,
                mainViewModel = mainViewModel,
                settingViewModel = settingVM
            )
        }




        composable<PromotionDetailRoute> {
            PromotionDetailScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                proCaisseViewModels = proCaisseViewModels,
                navigationDrawerViewModel = navigationDrawerViewModel,
                dataViewModel = dataViewModel,
                cameraViewModel = cameraViewModel,
                networkViewModel = networkViewModel,
                mainViewModel = mainViewModel,
                settingViewModel = settingVM
            )
        }

        composable<PrixDetailRoute> {
            PrixDetailScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navigationDrawerViewModel = navigationDrawerViewModel,
                dataViewModel = dataViewModel,
                cameraViewModel = cameraViewModel,
                networkViewModel = networkViewModel,
                mainViewModel = mainViewModel,
                settingViewModel = settingVM,
                proCaisseViewModels = proCaisseViewModels
            )
        }



        composable<MainImageTiketRoute> {
            MainImageTiket(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                barCodeViewModel = barCodeViewModel,
                cameraViewModel = cameraViewModel,
            )
        }




        composable<SelectArticlesScreensCalculRoute> {
            SelectArticlesScreenMobility(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                navigateUp = { navigateUp() },
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                navigationDrawerViewModel = navigationDrawerViewModel,
                articlesViewModel = articlesViewModel,
                barCodeViewModel = barCodeViewModel,
                mainViewModel = mainViewModel,
                selectArtCalculVM = selectArtMobilityVM,
                settingViewModel = settingVM
            )
        }

    }
}
