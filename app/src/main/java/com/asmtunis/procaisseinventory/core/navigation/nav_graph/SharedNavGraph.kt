package com.asmtunis.procaisseinventory.core.navigation.nav_graph

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.compose.navigation
import com.asmtunis.procaisseinventory.articles.consultation.screens.ArticleAddScreen
import com.asmtunis.procaisseinventory.articles.consultation.screens.ArticlesDetailScreen
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.ArticleTextValidationViewModel
import com.asmtunis.procaisseinventory.articles.consultation.view_model.ArticlesViewModel
import com.asmtunis.procaisseinventory.auth.AuthViewModel
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothConnectScreen
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AddArticlesRoute
import com.asmtunis.procaisseinventory.core.navigation.ArticlesDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.BluetoothConnectRoute
import com.asmtunis.procaisseinventory.core.navigation.NetworkErrorRoute
import com.asmtunis.procaisseinventory.core.navigation.SharedGraph
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.network_errors.screens.NetworkErrorScreen
import com.asmtunis.procaisseinventory.network_errors.view_model.NetworkErrorsViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.screens.TicketRayonViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels


fun NavGraphBuilder.sharedNavGraph(
    navigate: (route: Any, findStartDestination: Boolean) -> Unit,
    navigateUp: () -> Unit,
    navigatePopUpTo: (route: String, popUpTo: String, isInclusive: Boolean) -> Unit,
    popBackStack: () -> Unit,
    dataViewModel: DataViewModel,
    settingViewModel: SettingViewModel,
    networkViewModel: NetworkViewModel,
    authViewModel: AuthViewModel,
    getSharedDataViewModel : GetSharedDataViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    articlesViewModel : ArticlesViewModel,
    articleTxtValidViewModel : ArticleTextValidationViewModel,
    syncSharedViewModels : SyncSharedViewModels,
    barCodeViewModel : BarCodeViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    syncInventoryViewModel : SyncInventoryViewModel,
    mainViewModel : MainViewModel,
    ticketRayonViewModel: TicketRayonViewModel,
    printViewModel: PrintViewModel,
    bluetoothVM: BluetoothViewModel,
    sunmiPrintManager: SunmiPrintManager,
    networkErrorsVM: NetworkErrorsViewModel,
    ) {
        navigation<SharedGraph>(
           // startDestination = InventoryHome
            startDestination = NetworkErrorRoute
        ) {
            composable<NetworkErrorRoute> {
                NetworkErrorScreen(
                    popBackStack = { popBackStack() },
                    navDrawerViewmodel = navigationDrawerViewModel,
                    getProCaisseDataViewModel = getProCaisseDataViewModel,
                    mainViewModel = mainViewModel,
                    dataViewModel = dataViewModel,
                    networkViewModel = networkViewModel,
                    getProInventoryDataViewModel = getProInventoryDataViewModel,
                    getSharedDataViewModel = getSharedDataViewModel,
                    authViewModel = authViewModel,
                    networkErrorsVM = networkErrorsVM
                )
            }


            composable<ArticlesDetailRoute> {
                ArticlesDetailScreen(
                    navigate = { navigate(it, true) },
                    popBackStack = { popBackStack() },
                    networkViewModel = networkViewModel,
                    getProCaisseDataViewModel = getProCaisseDataViewModel,
                    getProInventoryDataViewModel = getProInventoryDataViewModel,
                    articlesViewModel = articlesViewModel,
                    articleTxtValidViewModel = articleTxtValidViewModel,
                    navigationDrawerViewModel = navigationDrawerViewModel,
                    dataViewModel = dataViewModel,
                    ticketRayonViewModel =  ticketRayonViewModel,
                    mainViewModel = mainViewModel,
                    printViewModel = printViewModel,
                    bluetoothVM = bluetoothVM,
                    sunmiPrintManager = sunmiPrintManager,
                    settingViewModel = settingViewModel
                )
            }

            composable<AddArticlesRoute> {
                ArticleAddScreen(
                    navigate = { navigate(it, false ) },
                    popBackStack = { popBackStack() },
                    barCodeViewModel = barCodeViewModel,
                    networkViewModel = networkViewModel,
                    getProCaisseDataViewModel = getProCaisseDataViewModel,
                    getProInventoryDataViewModel = getProInventoryDataViewModel,
                    articlesViewModel = articlesViewModel,
                    articleTxtValidVM = articleTxtValidViewModel,
                    navigationDrawerViewModel = navigationDrawerViewModel,
                    dataViewModel = dataViewModel,
                    mainViewModel = mainViewModel,
                    ticketRayonViewModel =  ticketRayonViewModel,
                    settingViewModel = settingViewModel
                )
            }

            composable<BluetoothConnectRoute> {
                BluetoothConnectScreen(
                    navigate = { navigate(it, true) },
                    navigateUp = { navigateUp() },
                    printViewModel = printViewModel,
                    bluetoothVM = bluetoothVM,
                    navigationDrawerViewModel = navigationDrawerViewModel,
                    settingViewModel = settingViewModel
                )
            }



        }
}