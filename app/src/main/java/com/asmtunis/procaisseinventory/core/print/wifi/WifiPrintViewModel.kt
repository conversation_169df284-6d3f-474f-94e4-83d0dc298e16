package com.asmtunis.procaisseinventory.core.print.wifi

import android.content.Context
import android.icu.text.MessageFormat
import android.print.PrintAttributes
import android.print.PrintJob
import android.print.PrintManager
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.print.PrintingData
import com.asmtunis.procaisseinventory.core.print.wifi.Comman.getAppPath
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertDoubleToDoubleFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToDoubleFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.etablisement.domaine.Etablisement
import com.asmtunis.procaisseinventory.data.facture.domaine.Facture
import com.asmtunis.procaisseinventory.data.parametrages.domaine.Parametrages
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommandeWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommandeWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicketWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetourWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetourWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisseWithTicketAndClient
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.itextpdf.io.image.ImageData
import com.itextpdf.io.image.ImageDataFactory
import com.itextpdf.kernel.colors.ColorConstants
import com.itextpdf.kernel.geom.PageSize
import com.itextpdf.kernel.pdf.PdfDocument
import com.itextpdf.kernel.pdf.PdfWriter
import com.itextpdf.layout.Document
import com.itextpdf.layout.borders.Border
import com.itextpdf.layout.element.Cell
import com.itextpdf.layout.element.Image
import com.itextpdf.layout.element.Paragraph
import com.itextpdf.layout.element.Table
import com.itextpdf.layout.properties.TextAlignment
import com.itextpdf.layout.properties.UnitValue
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileNotFoundException
import java.util.Locale
import javax.inject.Inject
import kotlin.io.encoding.Base64
import kotlin.io.encoding.ExperimentalEncodingApi
import kotlin.math.abs
import kotlin.math.max

@OptIn(ExperimentalEncodingApi::class)
@HiltViewModel
class WifiPrintViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val proInventoryLocalDb: ProInventoryLocalDb,
    private val proCaisseLocalDb: ProCaisseLocalDb
) : ViewModel() {


    init {
        getEtablissementList()
        getParametrages()
    }
    var parametrage by mutableStateOf(Parametrages())
        private set

    private fun getParametrages() {
        viewModelScope.launch {
            proInventoryLocalDb.parametrage.getOne().collect {


                parametrage = it?: Parametrages()

                logo =parametrage.pARAMLogo
            }
        }
    }

    var logo by mutableStateOf("")
        private set

    var etablisement by mutableStateOf(Etablisement())
        private set

    private fun getEtablissementList() {
        viewModelScope.launch {
            proCaisseLocalDb.etablisement.getAll().collect {

                if (it.isNullOrEmpty()) return@collect
                etablisement = it.first()
            }
        }
    }



    private fun getCell(textLeft: String?,
                        alignment: TextAlignment?,
                        noBorder: Boolean = true,
                        bold: Boolean = false): Cell? {
        val cell = Cell().add(Paragraph(textLeft))
        cell.setPadding(0f)
        cell.setTextAlignment(alignment)
        if (bold) cell.simulateBold()
        if (noBorder) cell.setBorder(Border.NO_BORDER)
        return cell
    }

    fun createBCPDFFile(context: Context,
                        bonCommandeWithClient: BonCommandeWithClient,
                        lgBonCommandeWithArticle: List<LigneBonCommandeWithArticle>,
                        listener: OnJobCompletedListener
    ) {
        viewModelScope.launch {
            val bonCommande = bonCommandeWithClient.bonCommande!!
            val client =  bonCommandeWithClient.client!!

            var num = ""

            if (bonCommande.isSync) num = num + bonCommande.dEVNum + "" else {
                num = num + bonCommande.devCodeM + ""
                if (num.contains("_")) {
                    val numTicket = num.split("_").toTypedArray()
                    num = "DEV_M_" + numTicket[2] + "_" + numTicket[3] + "_" + numTicket[5]
                }
            }


            val title = "Bon Commande"

            val filename = title.replace(" ", "_") + num + ".pdf"


            val path = getAppPath(context) + filename
            if (File(path).exists()) File(path).delete()
            try {
                val pdfWriter = PdfWriter(path)

                val pdf = PdfDocument(pdfWriter)

                val document = Document(pdf, PageSize.A4)

                val today = bonCommande.dEVDate

                header(
                    document = document,
                    today = today,
                    title = title,
                    num = num,
                    client = client,
                    cLICode = bonCommande.dEVCodeClient?:""
                )





                val devRemise = stringToDouble(bonCommande.dEVRemise)


                val tableArticles = if (devRemise > 0.0) Table(UnitValue.createPercentArray(floatArrayOf(2f, 2f, 1f, 1f, 1f, 1f, 1f, 1f, 1f))).useAllAvailableWidth()
                else Table(UnitValue.createPercentArray(floatArrayOf(2f, 2f, 1f, 1f, 1f, 1f, 1f, 1f))).useAllAvailableWidth()

                tableArticles.addCell(Cell().add(Paragraph("Code Art")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("Intitulé Article")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("Qte")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("PUHT")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                if (devRemise > 0.0) tableArticles.addCell(Cell().add(Paragraph("Rem%")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("TVA%")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("PU TTC")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("Net HT")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("Mnt TTC")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))




                for (lgBonCommandesWithArticle in lgBonCommandeWithArticle) {
                    val lgBonCommandes = lgBonCommandesWithArticle.ligneBonCommande ?: LigneBonCommande()
                    val article = lgBonCommandesWithArticle.article

                    val qte = lgBonCommandes.lGDEVQte.toString()
                    val label = article?.aRTDesignation?: lgBonCommandes.lGDEVCodeArt

                    tableArticles.addCell(Cell().add(Paragraph(lgBonCommandes.lGDEVCodeArt)).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(label)).setBorderLeft(Border.NO_BORDER).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(qte)).setBorderLeft(Border.NO_BORDER).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgBonCommandes.lGDEVMntBrutHT))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    if (devRemise > 0.0) tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgBonCommandes.lGDEVRemise))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgBonCommandes.lGDEVMntTva))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgBonCommandes.lGDEVPUTTC))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgBonCommandes.lGDEVMntHT))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgBonCommandes.lGDEVMntTTC))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                }

                document.add(tableArticles)

                val totalNetHT = stringToDouble(bonCommande.dEVMntNetHt) + stringToDouble(bonCommande.dEVRemise)
                val totalNet = stringToDouble(bonCommande.dEVMntNetHt)  + stringToDouble(bonCommande.dEVMntTva)


                footer(
                    document = document,
                    tauxTva = calculTauxTva(mnHt = stringToDouble(bonCommande.dEVMntht),
                        mntTva = stringToDouble(bonCommande.dEVMntTva)
                    ),
                    totalHT = convertStringToPriceFormat(totalNetHT.toString()),
                    totalRemise = devRemise.toString(),
                    haveDiscount = devRemise >0.0,
                    totalNetHT = convertStringToPriceFormat(bonCommande.dEVMntNetHt),
                    totalTVA = convertStringToPriceFormat(bonCommande.dEVMntTva),
                    timbre = convertStringToDoubleFormat(bonCommande.dEVTimbre),
                    netToPay = convertStringToPriceFormat((stringToDouble(bonCommande.dEVMntNetHt)  + stringToDouble(bonCommande.dEVMntTva)).toString()),
                    title = title

                )




                document.close()

                printPDF(context, filename) { listener.onJobCompleted() }



            } catch (e: FileNotFoundException) {
                e.printStackTrace()
            }
        }

    }


    fun createBonRetourPDFFile(
        context: Context,
        bonRetourWithClient: BonRetourWithClient,
        lgBonRetourWithArticle: List<LigneBonRetourWithArticle>,
        listener: OnJobCompletedListener
    ) {
        viewModelScope.launch {
            val bonDeRetour = bonRetourWithClient.bonRetour?: BonRetour()
            val client = bonRetourWithClient.client
            val num = bonDeRetour.bORNumero + ""

            val title = "Bon Retour"


            val filename = title.replace(" ", "_") + num + ".pdf"


            val path = getAppPath(context) + filename
            if (File(path).exists()) File(path).delete()
            try {
                val pdfWriter = PdfWriter(path)

                val pdf = PdfDocument(pdfWriter)

                val document = Document(pdf, PageSize.A4)
                val today = bonDeRetour.bORDate

                header(
                    document = document,
                    today = today,
                    title = title,
                    num = num,
                    client = client,
                    cLICode = bonDeRetour.bORCodefrs?:"N/A",
                )





                val borMntRemise = stringToDouble(bonDeRetour.bORMntRemise)




                val tableArticles = Table(UnitValue.createPercentArray(floatArrayOf(1.5f, 1.5f, 1f, 1f, 1f, 1f, 1f, 1f))).useAllAvailableWidth()

                tableArticles.addCell(Cell().add(Paragraph("Code Art")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("Intitulé Article")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("Qte")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("PUHT")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("TVA")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("PU TTC")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("Net HT")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("Mnt TTC")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))




                for (lgBcWithArticle in lgBonRetourWithArticle) {
                    val lgBc = lgBcWithArticle.ligneBonRetour?: LigneBonRetour()
                    val article = lgBcWithArticle.article
                    val qte = lgBc.lIGBonEntreeQte
                    val label = article?.designation?:lgBc.lIGBonEntreeCodeArt

                    tableArticles.addCell(Cell().add(Paragraph(lgBc.lIGBonEntreeCodeArt)).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(label)).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(qte)).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgBc.lIGBonEntreePUHT))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgBc.lIGBonEntreeTva))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgBc.lIGBonEntreeMntTTC))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgBc.lIGBonEntreeMntNetHt))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgBc.lIGBonEntreeMntTTC))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                }

                document.add(tableArticles)

                footer(
                    document = document,
                    tauxTva = calculTauxTva(mnHt = stringToDouble(bonDeRetour.bORMntHT),
                        mntTva = stringToDouble(bonDeRetour.bORMntTva)
                    ),
                    totalHT = "-${convertStringToPriceFormat(bonDeRetour.bORMntHT)}",
                    totalRemise = "-${convertStringToPriceFormat(borMntRemise.toString())}",
                    haveDiscount = borMntRemise >0.0,
                    totalNetHT = buildString {
                        append("-")
                        append(convertStringToPriceFormat(bonDeRetour.bORMntMntNetHt ?: bonDeRetour.bORMntTTC))
                    },
                    totalTVA = "-${convertStringToPriceFormat(bonDeRetour.bORMntTva?:"0")}",
                    timbre = "",
                    netToPay = "-${convertStringToPriceFormat(bonDeRetour.bORMntTTC)}",
                    title = title
                )


                document.close()
                printPDF(context, filename){ listener.onJobCompleted() }
            } catch (e: FileNotFoundException) {
                e.printStackTrace()
            }
        }

    }


    fun createTicketPDFFile(
        context: Context,
        listLigneTicketWithArticle: List<LigneTicketWithArticle>,
        ticketWithFactureAndPayments: TicketWithFactureAndPayments,
        printParams: PrintingData,
        listener: OnJobCompletedListener
    ) {
        viewModelScope.launch {
            val client = ticketWithFactureAndPayments.client?: Client(cLICode = ticketWithFactureAndPayments.ticket?.tIKCodClt?: "")

            var invoiced = false
            var numFact: String? = ""
            for (ligneTicket in listLigneTicketWithArticle) {
                if(ligneTicket.ligneTicket!= null) {
                    if (ligneTicket.ligneTicket?.lTNumFacture != null) {
                        invoiced = true
                        numFact = ligneTicket.ligneTicket?.lTNumFacture
                    }
                }

            }

            if ((ticketWithFactureAndPayments.ticket?.tIKNumeroBL != null || stringToDouble(ticketWithFactureAndPayments.ticket?.tIKNumFact?:"0.0")  != 0.0) && !ticketWithFactureAndPayments.ticket?.tIKNumeroBL?.contains("BL_M")!!) {
                invoiced = true
                numFact = ticketWithFactureAndPayments.ticket?.tIKNumeroBL
            }
            var num = "" + if (!invoiced) ticketWithFactureAndPayments.ticket?.tIKNumTicketM else numFact


            if (num.contains("_") && !invoiced) {
                val numTicket = num.split("_").toTypedArray()
                num = "BL_M_" + numTicket[2] + "_" + numTicket[3] + "_" + numTicket[4]
            }

            val title = if (invoiced) "Facture" else "Bon de Livraison"


            val filename = title.replace(" ", "_") + num + ".pdf"


            val path =  getAppPath(context) + filename
            if (File(path).exists()) File(path).delete()
            try {
                val pdfWriter = PdfWriter(path)

                val pdf = PdfDocument(pdfWriter)

                val document = Document(pdf, PageSize.A4)
                val today = ticketWithFactureAndPayments.ticket?.tIKDateHeureTicket


                header(
                    document = document,
                    today = today,
                    title = title,
                    num = num,
                    client = client,
                    cLICode = ticketWithFactureAndPayments.ticket?.tIKCodClt?: ""
                )




                val tIKMtRemise = stringToDouble(ticketWithFactureAndPayments.ticket?.tIKMtRemise)

                

                val tableArticles = if (tIKMtRemise > 0.0)
                    Table(UnitValue.createPercentArray(floatArrayOf(2.5f, 1f, 1f, 1f, 1f, 1f, 1f))).useAllAvailableWidth()
                else
                    Table(UnitValue.createPercentArray(floatArrayOf(2.5f, 1f, 1f, 1f, 1f, 1f))).useAllAvailableWidth()
                tableArticles.addCell(Cell().add(Paragraph("Désignation")).setFontSize(10f).setTextAlignment(TextAlignment.CENTER).simulateBold())
                tableArticles.addCell(Cell().add(Paragraph("Qte")).setFontSize(10f).setTextAlignment(TextAlignment.CENTER).simulateBold())
                tableArticles.addCell(Cell().add(Paragraph("PUHT")).setFontSize(10f).setTextAlignment(TextAlignment.CENTER).simulateBold())
                if (tIKMtRemise > 0.0) tableArticles.addCell(Cell().add(Paragraph("Rem%")).setFontSize(10f).setTextAlignment(TextAlignment.CENTER).simulateBold())
                tableArticles.addCell(Cell().add(Paragraph("Net HT")).setFontSize(10f).setTextAlignment(TextAlignment.CENTER).simulateBold())
                tableArticles.addCell(Cell().add(Paragraph("TVA%")).setFontSize(10f).setTextAlignment(TextAlignment.CENTER).simulateBold())
                tableArticles.addCell(Cell().add(Paragraph("Mnt TTC")).setFontSize(10f).setTextAlignment(TextAlignment.CENTER).simulateBold())




                for (ligneTicketWithArticle in listLigneTicketWithArticle) {

                    val lgTicket = ligneTicketWithArticle.ligneTicket?: LigneTicket()
                    val article = ligneTicketWithArticle.article
                    val qte = lgTicket.lTQte
                    val label =  article?.aRTDesignation?: lgTicket.lTCodArt


                    val lgPUHt = stringToDouble(lgTicket.lTMtHT)
                   // val lgNetHt = (stringToDouble(lgTicket.lTMtHT) + stringToDouble(lgTicket.lTRemise) )   / stringToDouble(qte)
                    val lgNetHt = (stringToDouble(lgTicket.lTMtHT) - stringToDouble(lgTicket.lTRemise) )

                    tableArticles.addCell(Cell().add(Paragraph(label)).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(qte)).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertDoubleToDoubleFormat(lgPUHt))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    if (tIKMtRemise > 0.0)  tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgTicket.lTTauxRemise))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertDoubleToDoubleFormat(lgNetHt))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgTicket.lTTVA))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgTicket.lTMtTTC))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))



                }

                document.add(tableArticles)

                // Add note if enabled and available
                addNoteToDocument(document, printParams)

                val ttHt = (stringToDouble(ticketWithFactureAndPayments.ticket?.tIKMtTTC?:"")  + stringToDouble(ticketWithFactureAndPayments.ticket?.tIKMtRemise?:"") - stringToDouble(ticketWithFactureAndPayments.ticket?.tIKTimbre?:"")) - stringToDouble(ticketWithFactureAndPayments.ticket?.tIKMtTVA?:"")
                val totalHT = ttHt.toString()
                val totalNetHt = ttHt  -  stringToDouble(ticketWithFactureAndPayments.ticket?.tIKMtRemise?:"")



                footer(
                    facture = ticketWithFactureAndPayments.facture,
                    document = document,
                    tauxTva = calculTauxTva(mnHt = totalNetHt, mntTva = stringToDouble(ticketWithFactureAndPayments.ticket?.tIKMtTVA?:"")),
                    totalHT = convertStringToPriceFormat(totalHT),
                    haveDiscount = tIKMtRemise > 0.0,
                    totalRemise = tIKMtRemise.toString(),
                    totalNetHT = convertStringToPriceFormat(totalNetHt.toString()),
                    totalTVA = convertStringToPriceFormat(ticketWithFactureAndPayments.ticket?.tIKMtTVA?:"0"),
                    timbre = ticketWithFactureAndPayments.ticket?.tIKTimbre?:"",
                    netToPay = ticketWithFactureAndPayments.ticket?.tIKMtTTC?:"0",
                    title = title
                )


                document.close()

                printPDF(context, filename){ listener.onJobCompleted() }
            } catch (e: FileNotFoundException) {
                e.printStackTrace()
            }
        }

    }

    fun createReceapTicketPDFFile(
        context: Context,
        ticket: Ticket,
        listLigneTicketWithArticle: List<LigneTicketWithArticle>,
        client: Client,
        listener: OnJobCompletedListener
    ) {
        viewModelScope.launch {
            val title = "Recap Tickets"


            val filename = title.replace(" ", "_") + ".pdf"


            val path = getAppPath(context) + filename
            if (File(path).exists()) File(path).delete()
            try {
                val pdfWriter = PdfWriter(path)

                val pdf = PdfDocument(pdfWriter)

                val document = Document(pdf, PageSize.A4)

                val today = ticket.tIKDateHeureTicket
                header(
                    document = document,
                    tIKNumeroBL = ticket.tIKNumeroBL?: "N/A",
                    today = today,
                    title = title,
                    num = "",
                    client = client,
                    cLICode = ticket.tIKCodClt
                )




                val tIKMtRemise = stringToDouble(ticket.tIKMtRemise)


                val tableArticles = if (tIKMtRemise > 0.0)
                    Table(UnitValue.createPercentArray(floatArrayOf(1.5f, 2.5f, 1f, 1f, 1f, 1f, 1f, 1f, 1f))).useAllAvailableWidth()
                else
                    Table(UnitValue.createPercentArray(floatArrayOf(1.5f, 2.5f, 1f, 1f, 1f, 1f, 1f, 1f))).useAllAvailableWidth()

                tableArticles.addCell(Cell().add(Paragraph("Code Art")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("Intitulé Article")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("Qte")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("PUHT")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                if (tIKMtRemise > 0.0) tableArticles.addCell(Cell().add(Paragraph("Rem%")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("TVA")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("PU TTC")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("Net HT")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                tableArticles.addCell(Cell().add(Paragraph("Mnt TTC")).setBorder(Border.NO_BORDER).setBackgroundColor(ColorConstants.LIGHT_GRAY).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))




                for (lgTicketWithArticle in listLigneTicketWithArticle) {
                    val lgTicket = lgTicketWithArticle.ligneTicket?: LigneTicket()

                    val qte = lgTicket.lTQte
                    val label: String = lgTicketWithArticle.article?.aRTDesignation?: lgTicket.lTCodArt


                    tableArticles.addCell(Cell().add(Paragraph(lgTicket.codeBarFils)).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(label)).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(qte)).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat( (stringToDouble(lgTicket.lTMtHT) / qte.toDouble()).toString()))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    if (tIKMtRemise > 0.0) tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgTicket.lTTauxRemise))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgTicket.lTTVA))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgTicket.lTPrixVente))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgTicket.lTMtHT))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                    tableArticles.addCell(Cell().add(Paragraph(convertStringToDoubleFormat(lgTicket.lTMtTTC))).setFontSize(10f).setTextAlignment(TextAlignment.CENTER))
                }

                document.add(tableArticles)
                val ttHt = (stringToDouble(ticket.tIKMtTTC)  + stringToDouble(ticket.tIKMtRemise)  - stringToDouble(ticket.tIKTimbre)) - stringToDouble(ticket.tIKMtTVA)


                footer(
                    document = document,
                    tauxTva = calculTauxTva(
                        mnHt = stringToDouble(ticket.tIKMtHT),
                        mntTva = stringToDouble(ticket.tIKMtTVA)),
                    totalHT = convertStringToPriceFormat(ttHt.toString()),
                    totalRemise =  tIKMtRemise.toString(),
                    haveDiscount = tIKMtRemise > 0.0,
                    totalNetHT = convertStringToPriceFormat((ttHt - stringToDouble(ticket.tIKMtRemise)).toString()),
                    totalTVA = convertStringToPriceFormat(ticket.tIKMtTVA),
                    timbre = ticket.tIKTimbre,
                    netToPay = convertStringToPriceFormat(ticket.tIKMtTTC),
                    title = title
                )


                document.close()
                printPDF(context, filename){ listener.onJobCompleted() }
            } catch (e: FileNotFoundException) {
                e.printStackTrace()
            }
        }


    }

    fun createReglementPDFFile(context: Context,
                               reglementCaisseWithTicketAndClient: ReglementCaisseWithTicketAndClient,
                               listener: OnJobCompletedListener
    ) {
        viewModelScope.launch {
            val cliCode = reglementCaisseWithTicketAndClient.reglementCaisse?.rEGCCodeClient?:""
            val client = reglementCaisseWithTicketAndClient.client?: Client(cLICode = cliCode, cLINomPren = cliCode)

            val ticket = reglementCaisseWithTicketAndClient.ticket
            val reglementCaisse = reglementCaisseWithTicketAndClient.reglementCaisse?: ReglementCaisse()

            var tikNum: String
            //  val ticket = App.database.ticketDAO().getOneByCode(reglementCaisse.getrEGCNumTicket().toInt(), App.prefUtils.exercice)
            if (ticket != null) {
                //  viewHolder.toolbar.setSubtitle(reglementCaisse.getrEGCNumTicket() > 0 ? String.format(context.getString(R.string.ticket_number_field), App.database.ticketDAO().getOneByCode((int) reglementCaisse.getrEGCNumTicket(), App.prefUtils.getExercice()).tikNumTicketM + "") : context.getString(R.string.credit_label));
                // viewHolder.toolbar.setSubtitle(reglementCaisse.getrEGCNumTicket() > 0 ? App.database.ticketDAO().getOneByCode((int) reglementCaisse.getrEGCNumTicket(), App.prefUtils.getExercice()).tikNumTicketM : context.getString(R.string.credit_label));
                if (ticket.tIKNumeroBL == null) {
                    tikNum = ticket.tIKNumTicketM
                    if (tikNum.contains("_")) {
                        val numTicket = tikNum.split("_").toTypedArray()
                        tikNum = context.getString(
                            R.string.ticket_number_field,
                            numTicket[2] + "_" + numTicket[3] + "_" + numTicket[4]
                        )
                    }
                } else tikNum =
                    context.getString(R.string.fact, ticket.tIKNumeroBL.toString())
                if (ticket.tIKAnnuler == "1") {
                    tikNum = "$tikNum (Annuler)"
                }
                tikNum = if (reglementCaisse.rEGNumTicketPart != null) {
                    if (stringToDouble(reglementCaisse.rEGNumTicketPart) > 0) context.getString(R.string.paiement_Partiel) else if (stringToDouble(reglementCaisse.rEGCNumTicket) > 0) tikNum else context.getString(
                        R.string.credit_label
                    )
                } else {
                    if (reglementCaisse.rEGCRemarque == "Regler Acpt") "Reglement Libre" else if (stringToDouble(reglementCaisse.rEGCNumTicket) > 0) tikNum else context.getString(
                        R.string.credit_label
                    )
                }
            } else {
                tikNum = if (reglementCaisse.rEGNumTicketPart != null) {
                    if (stringToDouble(reglementCaisse.rEGNumTicketPart) > 0) context.getString(R.string.paiement_Partiel) else {
                        if (reglementCaisse.rEGCRemarque == "Regler Acpt") "Reglement Libre" else if (stringToDouble(reglementCaisse.rEGCNumTicket) > 0) reglementCaisse.rEGCNumTicket.toString() else context.getString(
                            R.string.credit_label
                        )
                    }
                } else {
                    if (reglementCaisse.rEGCRemarque == "Regler Acpt") "Reglement Libre" else if (stringToDouble(reglementCaisse.rEGNumTicketPart) > 0) reglementCaisse.rEGCNumTicket.toString() else context.getString(
                        R.string.credit_label
                    )
                }
            }

            val title = "Reglement"
            val filename = title.replace(" ", "_") + tikNum + ".pdf"


            val path = getAppPath(context) + filename
            if (File(path).exists()) File(path).delete()
            try {
                val pdfWriter = PdfWriter(path)

                val pdf = PdfDocument(pdfWriter)

                val document = Document(pdf, PageSize.A4)

                val today = reglementCaisse.rEGCDateReg


                header(
                    document = document,
                    today = today,
                    title = title,
                    num = tikNum,
                    client = client,
                    cLICode = reglementCaisse.rEGCCodeClient?:"N/A"
                )


                val paymenttable = Table(UnitValue.createPercentArray(floatArrayOf(1f, 1f))).useAllAvailableWidth()

                if (abs(reglementCaisse.rEGCMntEspece?:0.0) > 0) {
                    paymenttable.addCell(Cell().add(Paragraph("Espéce")).setBorder(Border.NO_BORDER).setFontSize(15f).setTextAlignment(TextAlignment.CENTER))
                    paymenttable.addCell(Cell().add(Paragraph(convertStringToPriceFormat(reglementCaisse.rEGCMntEspece.toString())).setFontSize(15f).setTextAlignment(TextAlignment.CENTER)).setBorder(Border.NO_BORDER))

                }


                if (abs(reglementCaisse.rEGCMntCheque?:0.0) > 0) {
                    paymenttable.addCell(Cell().add(Paragraph("Chéque")).setBorder(Border.NO_BORDER).setFontSize(15f).setTextAlignment(TextAlignment.CENTER))
                    paymenttable.addCell(Cell().add(Paragraph(convertStringToPriceFormat(reglementCaisse.rEGCMntCheque.toString())).setFontSize(15f).setTextAlignment(TextAlignment.CENTER)).setBorder(Border.NO_BORDER))
                }
                if (abs(reglementCaisse.rEGCMntTraite?:0.0) > 0) {
                    paymenttable.addCell(Cell().add(Paragraph("Tikets Restaurant")).setBorder(Border.NO_BORDER).setFontSize(15f).setTextAlignment(TextAlignment.CENTER))
                    paymenttable.addCell(Cell().add(Paragraph(convertStringToPriceFormat(reglementCaisse.rEGCMntTraite.toString())).setFontSize(15f).setTextAlignment(TextAlignment.CENTER)).setBorder(Border.NO_BORDER))

                }


                val total = reglementCaisse.rEGCMntEspece ?: (0.0 + (reglementCaisse.rEGCMntCheque ?: 0.0) + (reglementCaisse.rEGCMntTraite ?: 0.0))
                paymenttable.addCell(Cell().add(Paragraph("Total")).setBorder(Border.NO_BORDER).setFontSize(20f).setTextAlignment(TextAlignment.CENTER).setMarginTop(70f))
                paymenttable.addCell(Cell().add(Paragraph(convertStringToPriceFormat(total.toString())).setFontSize(20f).setTextAlignment(TextAlignment.CENTER)).setBorder(Border.NO_BORDER).setMarginTop(70f))


                paymenttable.setMarginBottom(200f)
                document.add(paymenttable)


                val splitDouble = total.toString().split(".")
                val numberToWords = buildString {
                    append(splitDouble[0].toInt().toWords("fr", "FR").uppercase(Locale.getDefault()))
                    append(" ")
                    append("Dinars")
                    append(" ")
                    append(splitDouble[1])
                    append(" ")
                    append("millimes")
                }

                document.topMargin = 100f

                document.add(Paragraph("Arrété le présent Règlement à la somme de:\n$numberToWords"))
                val pg = Paragraph("Cachet & signature").simulateBold().setTextAlignment(TextAlignment.RIGHT)
                document.add(pg)


                document.close()

                printPDF(context, filename){ listener.onJobCompleted() }
            } catch (e: FileNotFoundException) {
                e.printStackTrace()
            }
        }

    }


    private fun printPDF(context: Context, fileName: String, action: () -> Unit): PrintJob {
        val printManager = context.getSystemService(Context.PRINT_SERVICE) as PrintManager
        val printAdapter = PdfDocumentAdapter(context, getAppPath(context) + fileName,action)
        return printManager.print("Documents", printAdapter, PrintAttributes.Builder().build())
    }


    private fun calculTauxTva(mnHt: Double, mntTva: Double): String {

        return convertDoubleToDoubleFormat((mntTva * 100) / mnHt)

    }

    private fun Int.toWords(language: String, country: String): String {
        val formatter = MessageFormat("{0,spellout,currency}", Locale(language, country))

        return formatter.format(arrayOf(this))
    }

    /**
     * Add note to PDF document if enabled and available
     */
    private fun addNoteToDocument(document: Document, printParams: PrintingData) {
        // Check if notes are enabled and noteText is not empty
        if (printParams.enableNotes && printParams.noteText.isNotEmpty()) {
            // Create a table for the note with "Note:" label and the actual text content
            val noteTable = Table(UnitValue.createPercentArray(floatArrayOf(1f, 3f))).useAllAvailableWidth()

            noteTable.addCell(
                Cell().add(Paragraph("Note:"))
                    .setFontSize(12f)
                    .setTextAlignment(TextAlignment.LEFT)
                    .simulateBold()
                    .setBorder(Border.NO_BORDER)
                    .setPaddingRight(10f)
            )

            noteTable.addCell(
                Cell().add(Paragraph(printParams.noteText))
                    .setFontSize(12f)
                    .setTextAlignment(TextAlignment.LEFT)
                    .setBorder(Border.NO_BORDER)
            )

            // Add some spacing before the note
            noteTable.setMarginTop(15f)
            noteTable.setMarginBottom(15f)

            document.add(noteTable)
        }
    }

    suspend fun header(
        document: Document,
        tIKNumeroBL: String = "",
        today: String?,
        title: String,
        num: String,
        client: Client?,
        cLICode: String
    ) {
      //  val date: DateFormat = SimpleDateFormat("dd/MM/yyyy")
      //  val time: DateFormat = SimpleDateFormat("HH:mm")


             var cltSociete = ""
             var cltCode = ""
            var cltAdresse = ""
            var cltTel = ""
            var cltMF = ""
            var cltName = client?.cLINomPren?: cLICode

           // if (client != null) {
                 cltCode = client?.cLICode?: "N/A"
                cltSociete = client?.cLISociete ?: "N/A"
                cltAdresse = client?.cLIAdresse ?: "N/A"
                cltTel = client?.cLITel1 ?: client?.cLITel2 ?: "N/A"
                cltMF = client?.cLIMatFisc ?: "N/A"

                cltName = cltName.ifEmpty { client?.cLINomPren?: cltCode }
           // }


        val etablisementDes: String = etablisement.desgEt ?: "N/A"
        val etablisementAdress: String = etablisement.etAdresse ?: "Adresse : N/A"
        val etablisementMF: String = etablisement.etMAtriculeF ?: "MF : N/A"
        val etablisementAct: String = etablisement.etActivite ?: "Activité : N/A"
        val etablisementcodeEt: String = etablisement.codeEt


        val tableIconHeader = Table(UnitValue.createPercentArray(floatArrayOf(1f, 1f))).useAllAvailableWidth()

        if(logo.isNotEmpty()) {
               //

              //  val data: ImageData = ImageDataFactory.create(decodedString)
               //  img = Image(data).setHeight(100F).setWidth(100F)



            val decodedString = Base64.decode(logo)
            val data: ImageData = ImageDataFactory.create(decodedString)
            val img = Image(data).setHeight(80F).setWidth(80F)

            tableIconHeader.addCell(Cell().add(img).setBorder(Border.NO_BORDER))


            document.add(tableIconHeader)
        }














        val tableHeader = Table(UnitValue.createPercentArray(floatArrayOf(2f,1f, 1f, 1f))).useAllAvailableWidth()

        tableHeader.addCell(getCell(etablisementDes, TextAlignment.CENTER, bold = true, noBorder = true))
        if(num.isNotEmpty())  tableHeader.addCell(getCell(" $title", TextAlignment.CENTER, bold = true, noBorder = false))
        else tableHeader.addCell(getCell("\n $title \n $tIKNumeroBL", TextAlignment.CENTER, bold = true))
        // if(tIKNumeroBL.isNotEmpty())  tableHeader.addCell(getCell("\n ${tIKNumeroBL}\n", TextAlignment.LEFT, bold = true, noBorder = false))

        tableHeader.addCell(getCell("Date", TextAlignment.CENTER, bold = true, noBorder = false))
        tableHeader.addCell(getCell("Page", TextAlignment.CENTER, bold = true, noBorder = false))



        tableHeader.addCell(getCell("", TextAlignment.CENTER, bold = false, noBorder = true))
        if(num.isNotEmpty())  tableHeader.addCell(getCell("N°: $num", TextAlignment.CENTER, bold = false, noBorder = false))
        else tableHeader.addCell(getCell("\n $title \n $tIKNumeroBL", TextAlignment.CENTER, bold = false))
        // if(tIKNumeroBL.isNotEmpty())  tableHeader.addCell(getCell("\n ${tIKNumeroBL}\n", TextAlignment.LEFT, bold = true, noBorder = false))

       // tableHeader.addCell(getCell("${today?.let { date.format(it) }}  ${today?.let { time.format(it) }} ", TextAlignment.CENTER, bold = false, noBorder = false))
        tableHeader.addCell(getCell(today?.substringBefore(".")?: "N/A" , TextAlignment.CENTER, bold = false, noBorder = false))
        tableHeader.addCell(getCell("${document.pdfDocument.numberOfPages} ", TextAlignment.CENTER, bold = false, noBorder = false))

        tableHeader.setMarginBottom(30f)
        document.add(tableHeader)




        val tableInfoHeader = Table(UnitValue.createPercentArray(floatArrayOf(1f, 1f, 0.5f, 1f))).useAllAvailableWidth()


        tableInfoHeader.addCell(Cell().add(Paragraph("")).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER))
        tableInfoHeader.addCell(Cell().add(Paragraph("")).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER))
        tableInfoHeader.addCell(Cell().add(Paragraph("Réf")).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).simulateBold().setBorderBottom(Border.NO_BORDER).setBorderRight(Border.NO_BORDER).setPaddingLeft(3f))
        tableInfoHeader.addCell(Cell().add(Paragraph(cltCode)).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).setBorderBottom(Border.NO_BORDER).setBorderLeft(Border.NO_BORDER))


        val cell = Cell(1,2).add(Paragraph(etablisementAdress)).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER)

        //tableInfoHeader.addCell(Cell().add(Paragraph(etablisementAdress)).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER))
        tableInfoHeader.addCell(cell)
        //   tableInfoHeader.addCell(Cell().add(Paragraph("")).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER))
        tableInfoHeader.addCell(Cell().add(Paragraph("Client")).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).simulateBold().setBorderBottom(Border.NO_BORDER).setBorderRight(Border.NO_BORDER).setBorderTop(Border.NO_BORDER).setPaddingLeft(3f))
        tableInfoHeader.addCell(Cell().add(Paragraph(cltName)).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).setBorderBottom(Border.NO_BORDER).setBorderLeft(Border.NO_BORDER).setBorderTop(Border.NO_BORDER))

        tableInfoHeader.addCell(Cell().add(Paragraph("Tel:  $cltTel")).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER))
        tableInfoHeader.addCell(Cell().add(Paragraph("")).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER))
        tableInfoHeader.addCell(Cell().add(Paragraph("Adresse")).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).simulateBold().setBorderBottom(Border.NO_BORDER).setBorderRight(Border.NO_BORDER).setBorderTop(Border.NO_BORDER).setPaddingLeft(3f))
        tableInfoHeader.addCell(Cell().add(Paragraph(cltAdresse)).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).setBorderBottom(Border.NO_BORDER).setBorderLeft(Border.NO_BORDER).setBorderTop(Border.NO_BORDER))


        tableInfoHeader.addCell(Cell().add(Paragraph("MF:  $etablisementMF")).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER))
        tableInfoHeader.addCell(Cell().add(Paragraph("R.N.E:")).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER))
        tableInfoHeader.addCell(Cell().add(Paragraph("TEL:")).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).simulateBold().setBorderBottom(Border.NO_BORDER).setBorderRight(Border.NO_BORDER).setBorderTop(Border.NO_BORDER).setPaddingLeft(3f))
        tableInfoHeader.addCell(Cell().add(Paragraph(cltTel)).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).setBorderBottom(Border.NO_BORDER).setBorderLeft(Border.NO_BORDER).setBorderTop(Border.NO_BORDER))

        tableInfoHeader.addCell(Cell().add(Paragraph("")).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER))
        tableInfoHeader.addCell(Cell().add(Paragraph("")).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER))
        tableInfoHeader.addCell(Cell().add(Paragraph("MF:")).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).simulateBold().setBorderTop(Border.NO_BORDER).setBorderRight(Border.NO_BORDER).setPaddingLeft(3f))
        tableInfoHeader.addCell(Cell().add(Paragraph("$cltMF R.N.E:")).setFontSize(10f).setTextAlignment(TextAlignment.LEFT).setBorderTop(Border.NO_BORDER).setBorderLeft(Border.NO_BORDER))






        tableInfoHeader.setMarginBottom(30f)
        document.add(tableInfoHeader)





    }


    private fun footer(
        facture: Facture? = null,
        document: Document,
        tauxTva: String,
        totalHT: String,
        totalRemise: String,
        haveDiscount: Boolean,
        totalNetHT: String,
        totalTVA: String,
        timbre: String,
        netToPay: String,
        title: String) {


        val totRemise = stringToDouble(totalRemise)


        val tableFooter = Table(UnitValue.createPercentArray(floatArrayOf(1f, 1f, 1f, 2f, 1.5f, 1f))).useAllAvailableWidth()

        tableFooter.addCell(Cell().add(Paragraph("Base TVA")).setBorderRight(Border.NO_BORDER).simulateBold())
        tableFooter.addCell(Cell().add(Paragraph("Taux")).setBorderLeft(Border.NO_BORDER).setBorderRight(Border.NO_BORDER).simulateBold())
        tableFooter.addCell(Cell().add(Paragraph("Montant")).setBorderLeft(Border.NO_BORDER).simulateBold())
        tableFooter.addCell(Cell().add(Paragraph("Cachet & signature ")).setBorder(Border.NO_BORDER).setPaddingLeft(3f))
        tableFooter.addCell(Cell().add(Paragraph("Total HT:")).setBorderRight(Border.NO_BORDER).simulateBold())
        tableFooter.addCell(Cell().add(Paragraph(totalHT)).setBorderLeft(Border.NO_BORDER).setTextAlignment(TextAlignment.CENTER))


        if (totRemise > 0.0) {
            tableFooter.addCell(Cell().add(Paragraph(totalNetHT)).setBorderRight(Border.NO_BORDER).simulateBold())
            tableFooter.addCell(Cell().add(Paragraph(tauxTva)).setBorderLeft(Border.NO_BORDER).setBorderRight(Border.NO_BORDER).simulateBold())
            tableFooter.addCell(Cell().add(Paragraph(totalTVA)).setBorderLeft(Border.NO_BORDER).simulateBold())
            tableFooter.addCell(Cell().add(Paragraph()).setBorder(Border.NO_BORDER))
            tableFooter.addCell(Cell().add(Paragraph("Total Remise:")).setBorderRight(Border.NO_BORDER).simulateBold())
            tableFooter.addCell(Cell().add(Paragraph(convertStringToPriceFormat(totalRemise))).setBorderLeft(Border.NO_BORDER).setTextAlignment(TextAlignment.CENTER))



            tableFooter.addCell(Cell().add(Paragraph("           ")).setBorder(Border.NO_BORDER))
            tableFooter.addCell(Cell().add(Paragraph("        ")).setBorder(Border.NO_BORDER))
            tableFooter.addCell(Cell().add(Paragraph("        ")).setBorder(Border.NO_BORDER))
            tableFooter.addCell(Cell().add(Paragraph("")).setBorder(Border.NO_BORDER))
            tableFooter.addCell(Cell().add(Paragraph("Total Net HT:")).setBorderRight(Border.NO_BORDER).simulateBold())
            tableFooter.addCell(Cell().add(Paragraph(totalNetHT)).setBorderLeft(Border.NO_BORDER).setTextAlignment(TextAlignment.CENTER))


        }
        else {
            tableFooter.addCell(Cell().add(Paragraph(totalNetHT)).setBorderRight(Border.NO_BORDER).simulateBold())
            tableFooter.addCell(Cell().add(Paragraph(tauxTva)).setBorderLeft(Border.NO_BORDER).setBorderRight(Border.NO_BORDER).simulateBold())
            tableFooter.addCell(Cell().add(Paragraph(totalTVA)).setBorderLeft(Border.NO_BORDER).simulateBold())
            tableFooter.addCell(Cell().add(Paragraph("")).setBorder(Border.NO_BORDER))
            tableFooter.addCell(Cell().add(Paragraph("Total Net HT:")).setBorderRight(Border.NO_BORDER).simulateBold())
            tableFooter.addCell(Cell().add(Paragraph(totalNetHT)).setBorderLeft(Border.NO_BORDER).setTextAlignment(TextAlignment.CENTER))
        }


        tableFooter.addCell(Cell().add(Paragraph("")).setBorder(Border.NO_BORDER))
        tableFooter.addCell(Cell().add(Paragraph("")).setBorder(Border.NO_BORDER))
        tableFooter.addCell(Cell().add(Paragraph("")).setBorder(Border.NO_BORDER))
        tableFooter.addCell(Cell().add(Paragraph("")).setBorder(Border.NO_BORDER))
        tableFooter.addCell(Cell().add(Paragraph("Total TVA:")).setBorderRight(Border.NO_BORDER).simulateBold())
        tableFooter.addCell(Cell().add(Paragraph(totalTVA)).setBorderLeft(Border.NO_BORDER).setTextAlignment(TextAlignment.CENTER))


        if(facture!= null) {
            if(stringToDouble(facture.factMntRevImp) > 0.0) {
                tableFooter.addCell(Cell().add(Paragraph("")).setBorder(Border.NO_BORDER).simulateBold())
                tableFooter.addCell(Cell().add(Paragraph("")).setBorder(Border.NO_BORDER).simulateBold())
                tableFooter.addCell(Cell().add(Paragraph("")).setBorder(Border.NO_BORDER).simulateBold())
                tableFooter.addCell(Cell().add(Paragraph("")).setBorder(Border.NO_BORDER))
                tableFooter.addCell(Cell().add(Paragraph("Avance Imp:")).setBorderRight(Border.NO_BORDER).simulateBold())
                //  tableFooter.addCell(Cell().add(Paragraph(StringUtils.priceFormat(parseDouble(facture.FACT_MntRevImp, 0.0))).setBorderLeft(Border.NO_BORDER).setTextAlignment(TextAlignment.CENTER)))
                tableFooter.addCell(Cell().add(Paragraph(convertStringToPriceFormat(facture.factMntRevImp?:"0.0"))).setBorderLeft(Border.NO_BORDER).setTextAlignment(TextAlignment.CENTER))

            }

        }

        if (timbre.isNotEmpty() || facture!= null) {


            tableFooter.addCell(Cell().add(Paragraph("")).setBorder(Border.NO_BORDER).simulateBold())
            tableFooter.addCell(Cell().add(Paragraph("")).setBorder(Border.NO_BORDER).simulateBold())
            tableFooter.addCell(Cell().add(Paragraph("")).setBorder(Border.NO_BORDER).simulateBold())
            tableFooter.addCell(Cell().add(Paragraph("")).setBorder(Border.NO_BORDER))
            tableFooter.addCell(Cell().add(Paragraph("Timbre Fiscal:")).setBorderRight(Border.NO_BORDER).simulateBold())
            tableFooter.addCell(Cell().add(Paragraph(convertStringToPriceFormat(facture?.factTimbre?: timbre))).setBorderLeft(Border.NO_BORDER).setTextAlignment(TextAlignment.CENTER))

        }
        val tikMttc: String = if(facture!= null) {
            convertStringToPriceFormat(max(
                stringToDouble(facture.factMntTTC),
                stringToDouble(netToPay)
            ).toString()
            )

        } else convertStringToPriceFormat(netToPay)
        //keepNumbers
        val splitDouble = stringToDouble(convertStringToDoubleFormat(netToPay)).toString().split(".")

        val numberToWords = buildString {
            append(splitDouble[0].toInt().toWords("fr", "FR").uppercase(Locale.getDefault()))
            append(" ")
            append(Globals.devise.devise.ifEmpty { "Dinars" })
            append(" ")
            append(splitDouble[1])
            append(" ")
            append(Globals.devise.unite?.ifEmpty { "millimes" }?: "millimes")
        }

        val cell = Cell(1,4).add(Paragraph("Arrété le présent $title à la somme de: $numberToWords")).simulateBold().setTextAlignment(TextAlignment.LEFT).setFontSize(8f).setBorder(Border.NO_BORDER)


        tableFooter.addCell(cell)
        tableFooter.addCell(Cell().add(Paragraph("Net à payer:")).setBorderRight(Border.NO_BORDER).simulateBold())
        tableFooter.addCell(Cell().add(Paragraph(tikMttc)).setBorderLeft(Border.NO_BORDER).setTextAlignment(TextAlignment.CENTER))





        tableFooter.setMarginTop(50f)

        document.add(tableFooter)
    }



}

