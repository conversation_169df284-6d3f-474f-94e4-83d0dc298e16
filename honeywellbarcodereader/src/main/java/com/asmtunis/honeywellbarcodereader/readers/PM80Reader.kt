package com.asmtunis.honeywellbarcodereader.readers

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.RECEIVER_NOT_EXPORTED
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.util.Log
import com.asmtunis.honeywellbarcodereader.listener.BarcodeListener
import com.asmtunis.honeywellbarcodereader.listener.IBareCodeReader
import device.common.DecodeResult
import device.common.ScanConst
import device.common.ScanConst.SymbologyID
import device.sdk.ScanManager
import java.text.Normalizer


/*

class PM80Reader(context: Activity, listener: BarcodeListener?) : IBareCodeReader {
    var context: Activity

    init {
        Companion.listener = listener
        this.context = context
        receiver = ScanResultReceiver()
    }

    private fun startPM80Listener() {
        try {
            if (mScanner == null) {
                mDecodeResult = DecodeResult()
                mScanner = ScanManager()
                mScanner!!.aDecodeSetResultType(ScanConst.ResultType.DCD_RESULT_USERMSG)
                mScanner!!.aDecodeSetTriggerMode(ScanConst.TriggerMode.DCD_TRIGGER_MODE_ONESHOT)
            }
            val filter = IntentFilter()
            filter.addAction("device.common.USERMSG")
            filter.addAction("device.scanner.EVENT")
            context.registerReceiver(receiver, filter)
        } catch (e: Exception) {
            Log.d("error", e.message!!)
        }
    }



    override fun startListening() {
        val manufacturer = Build.MANUFACTURER?:""
        if (manufacturer == "POINTMOBILE") {
            startPM80Listener()
            val filter = IntentFilter()
            filter.addAction("device.common.USERMSG")
            filter.addAction("device.scanner.EVENT")
            context.registerReceiver(receiver, filter)
        }
    }

    override fun resume() {
        startPM80Listener()
    }


    override fun destroy() {
        mScanner = null
        mDecodeResult = null
        try {
            context.unregisterReceiver(receiver)
        } catch (ignored: Exception) {
        }

        /*
        receiver = null;
*/
    }


    class ScanResultReceiver : BroadcastReceiver() {
        override fun onReceive(context2: Context, intent: Intent) {
            println("data received")
            if (mScanner != null) {
                if (ScanConst.INTENT_USERMSG == intent.action) {
                    try {
                        mScanner!!.aDecodeGetResult(mDecodeResult!!.recycle())
                        if ((mDecodeResult.toString().equals(
                                "READ_FAIL",
                                ignoreCase = true
                            ) || (mDecodeResult!!.symName.equals("QR CODE", ignoreCase = true)))
                        ) {
                            listener?.onFail("Failer read code")
                        } else {
                            val normalized =
                                Normalizer.normalize(mDecodeResult.toString(), Normalizer.Form.NFD)
                            val code = normalized.replace("[^A-Za-z0-9]".toRegex(), "")
                            if (listener != null) {
                                Log.d("ScanResultReceiver", code + "")
                                listener!!.onSuccess(code)
                            } else {
                                Log.d("ScanResultReceiver", "null reader")
                            }
                        }
                    } catch (e: Exception) {
                        Log.d("pm",e.message.toString());
                      //  Tos.info(context2, e.message).show()
                    }
                }
            }
        }
    }

    companion object {
        var listener: BarcodeListener? = null
         var mScanner: ScanManager? = ScanManager()
         var mDecodeResult: DecodeResult? = DecodeResult()
        private lateinit var receiver: ScanResultReceiver
    }
}
*/

class PM80Reader(context: Context, private val listener: BarcodeListener?) : IBareCodeReader {
    private val activityContext = context as? Activity ?: throw IllegalArgumentException("Context must be an Activity")
    private var scanManager: ScanManager? = null
    private var decodeResult: DecodeResult? = null
    private var isReceiverRegistered = false
    private val receiver = ScanResultReceiver()

    override fun startListening() {
//        if (Build.MANUFACTURER.equals("POINTMOBILE", ignoreCase = true)) {
        setupScanner()
        registerReceiverIfNeeded()
//        }
    }

    override fun resume() {
        setupScanner()
        registerReceiverIfNeeded()
    }

    override fun destroy() {
        scanManager = null
        decodeResult = null
        unregisterReceiverIfNeeded()
    }

    private fun setupScanner() {
        try {
            if (scanManager == null) {
                decodeResult = DecodeResult()
                scanManager = ScanManager().apply {
                    aDecodeSetResultType(ScanConst.ResultType.DCD_RESULT_USERMSG)
                    aDecodeSetTriggerMode(ScanConst.TriggerMode.DCD_TRIGGER_MODE_ONESHOT)
                    //     aDecodeSymSetEnable(ScanConst.SymbologyID.DCD_SYM_UPCA, 1)

                    for (i in SymbologyID.DCD_SYM_NIL..SymbologyID.DCD_SYM_LAST) {
                        aDecodeSymSetEnable(i, 1) // Enable each symbology type
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("PM80Reader", "Failed to setup scanner: ${e.message}")
        }
    }


    private fun registerReceiverIfNeeded() {
        if (!isReceiverRegistered) {
            val filter = IntentFilter().apply {
                addAction("device.common.USERMSG")
                addAction("device.scanner.EVENT")
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                activityContext.registerReceiver(receiver, filter, RECEIVER_NOT_EXPORTED)
            } else {
                @Suppress("UnspecifiedRegisterReceiverFlag")
                activityContext.registerReceiver(receiver, filter)
            }


            isReceiverRegistered = true
        }
    }

    private fun unregisterReceiverIfNeeded() {
        if (isReceiverRegistered) {
            try {
                activityContext.unregisterReceiver(receiver)
                isReceiverRegistered = false
            } catch (e: Exception) {
                Log.w("PM80Reader", "Error unregistering receiver: ${e.message}")
            }
        }
    }

    private inner class ScanResultReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (scanManager == null || ScanConst.INTENT_USERMSG != intent.action) return

            try {
                scanManager?.aDecodeGetResult(decodeResult?.recycle())
                val resultString = decodeResult.toString()
                if (resultString.equals("READ_FAIL", ignoreCase = true)) {
                    listener?.onFail("Failed to read code")
                } else {
                    val normalized = Normalizer.normalize(resultString, Normalizer.Form.NFD)
                        .replace("[^A-Za-z0-9]".toRegex(), "")
                    listener?.onSuccess(normalized)
                }
            } catch (e: Exception) {
                Log.e("PM80Reader", "Error decoding result: ${e.message}")
            }
        }
    }
}


